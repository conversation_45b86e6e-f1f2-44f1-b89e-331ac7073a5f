/**
 * Admin audit log retrieval API endpoint
 * Provides access to comprehensive audit trail for admin actions
 */

import { type NextRequest, NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth/admin-utils";
import { createClient } from "@/lib/supabase/server";
import { auth } from "@/app/(auth)/auth-server";
import { ChatSDKError } from "@/lib/errors";

interface AuditLogEntry {
  id: string;
  user_id: string;
  amount: number;
  description: string;
  transaction_type: string;
  created_at: string;
  user_email?: string;
}

interface AuditLogResponse {
  logs: AuditLogEntry[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  filters: {
    user_id?: string;
    start_date?: string;
    end_date?: string;
    transaction_type?: string;
  };
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Authenticate and verify admin access
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    requireAdmin(session.user, "audit log access");

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = Math.max(1, Number.parseInt(searchParams.get("page") || "1", 10));
    const limit = Math.min(100, Math.max(1, Number.parseInt(searchParams.get("limit") || "50", 10)));
    const offset = (page - 1) * limit;

    // Filter parameters
    const userId = searchParams.get("user_id") || undefined;
    const startDate = searchParams.get("start_date") || undefined;
    const endDate = searchParams.get("end_date") || undefined;
    const transactionType = searchParams.get("transaction_type") || undefined;
    const search = searchParams.get("search") || undefined;

    const supabase = await createClient();

    // Build query for audit logs
    let query = supabase
      .from("credit_transactions")
      .select(`
        id,
        user_id,
        amount,
        description,
        transaction_type,
        created_at,
        User!inner(email)
      `)
      .order("created_at", { ascending: false });

    // Apply filters
    if (userId) {
      query = query.eq("user_id", userId);
    }

    if (startDate) {
      query = query.gte("created_at", startDate);
    }

    if (endDate) {
      query = query.lte("created_at", endDate);
    }

    if (transactionType) {
      query = query.eq("transaction_type", transactionType);
    }

    if (search) {
      // Search in description or user email
      query = query.or(`description.ilike.%${search}%,User.email.ilike.%${search}%`);
    }

    // Get paginated results
    const { data: logs, error: logsError } = await query.range(offset, offset + limit - 1);

    if (logsError) {
      console.error("Failed to fetch audit logs:", logsError);
      return NextResponse.json({ error: "Failed to fetch audit logs" }, { status: 500 });
    }

    // Get total count with same filters
    let countQuery = supabase
      .from("credit_transactions")
      .select("*", { count: "exact", head: true });

    // Apply same filters for count
    if (userId) {
      countQuery = countQuery.eq("user_id", userId);
    }

    if (startDate) {
      countQuery = countQuery.gte("created_at", startDate);
    }

    if (endDate) {
      countQuery = countQuery.lte("created_at", endDate);
    }

    if (transactionType) {
      countQuery = countQuery.eq("transaction_type", transactionType);
    }

    if (search) {
      // For count query, we need to join with User table
      countQuery = supabase
        .from("credit_transactions")
        .select("*, User!inner(email)", { count: "exact", head: true })
        .or(`description.ilike.%${search}%,User.email.ilike.%${search}%`);
    }

    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Failed to count audit logs:", countError);
      return NextResponse.json({ error: "Failed to count audit logs" }, { status: 500 });
    }

    // Transform the data to include user email at the top level
    const transformedLogs: AuditLogEntry[] = (logs || []).map((log: any) => ({
      id: log.id,
      user_id: log.user_id,
      amount: log.amount,
      description: log.description,
      transaction_type: log.transaction_type,
      created_at: log.created_at,
      user_email: log.User?.email,
    }));

    const response: AuditLogResponse = {
      logs: transformedLogs,
      total: count || 0,
      page,
      limit,
      hasMore: (count || 0) > offset + limit,
      filters: {
        user_id: userId,
        start_date: startDate,
        end_date: endDate,
        transaction_type: transactionType,
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Audit log retrieval error:", error);

    if (error instanceof ChatSDKError) {
      if (error.type === "unauthorized") {
        return NextResponse.json({ error: "Unauthorized access" }, { status: 401 });
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

/**
 * Get audit log statistics
 */
export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Authenticate and verify admin access
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    requireAdmin(session.user, "audit statistics access");

    const supabase = await createClient();

    // Get audit statistics
    const [
      totalTransactions,
      adminAdjustments,
      recentActivity,
      topUsers
    ] = await Promise.all([
      // Total transactions count
      supabase
        .from("credit_transactions")
        .select("*", { count: "exact", head: true }),

      // Admin adjustments count
      supabase
        .from("credit_transactions")
        .select("*", { count: "exact", head: true })
        .eq("transaction_type", "admin_adjustment"),

      // Recent activity (last 24 hours)
      supabase
        .from("credit_transactions")
        .select("*", { count: "exact", head: true })
        .gte("created_at", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()),

      // Top users by transaction count
      supabase
        .from("credit_transactions")
        .select(`
          user_id,
          User!inner(email),
          count:id.count()
        `)
        .limit(10)
    ]);

    const statistics = {
      total_transactions: totalTransactions.count || 0,
      admin_adjustments: adminAdjustments.count || 0,
      recent_activity_24h: recentActivity.count || 0,
      top_users: topUsers.data || [],
    };

    return NextResponse.json(statistics);
  } catch (error) {
    console.error("Audit statistics error:", error);

    if (error instanceof ChatSDKError) {
      if (error.type === "unauthorized") {
        return NextResponse.json({ error: "Unauthorized access" }, { status: 401 });
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
