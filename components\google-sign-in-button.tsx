"use client";

import { But<PERSON> } from "@/components/ui/button";
import { LogoGoogle } from "@/components/icons";
import { useState } from "react";
import { toast } from "@/components/toast";
import { trackLogin } from "@/lib/analytics";
import { signInWithGoogle } from "@/app/(auth)/auth";
import { createClient } from "@/lib/supabase/client";

export function GoogleSignInButton() {
  const [isLoading, setIsLoading] = useState(false);

  const handleSignInWithGoogle = async () => {
    try {
      setIsLoading(true);

      const result = await signInWithGoogle();

      if (!result.ok) {
        throw new Error(result.error);
      }

      // If we have a URL, redirect to it
      if (result.data?.url) {
        // Track the Google login attempt
        const supabase = createClient();
        supabase.auth.getUser().then(({ data }) => {
          if (data.user) {
            const userType = data.user.email && /^guest-\d+$/.test(data.user.email) ? "guest" : "regular";
            trackLogin(data.user.id, userType, "google");
          }
        });

        // The OAuth flow will handle the redirect
        window.location.href = result.data.url;
      }
    } catch (error) {
      console.error("Error signing in with Google:", error);
      toast({
        type: "error",
        description: "Failed to sign in with Google. Please try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button type="button" variant="outline" onClick={handleSignInWithGoogle} disabled={isLoading} className="w-full">
      {isLoading ? (
        <span className="animate-spin mr-2">
          <svg className="size-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        </span>
      ) : (
        <LogoGoogle size={20} />
      )}
      <span className="ml-2">Sign in with Google</span>
    </Button>
  );
}
