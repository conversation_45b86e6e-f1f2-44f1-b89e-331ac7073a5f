import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { getServerCreditService } from "@/lib/services/credit-service";
import { isEmailAdmin } from "@/lib/auth/admin-config";

export type UserType = "regular";

export interface SupabaseUser {
  id: string;
  email?: string | null;
  type: UserType;
  credits: number;
  isAdmin: boolean;
}

export interface SupabaseSession {
  user: SupabaseUser;
}

/**
 * Get the current user's session
 * @returns The user's session or null if not authenticated
 */
export async function auth(): Promise<SupabaseSession | null> {
  "use server";

  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return null;
  }

  // Get user credits
  let credits = 0;
  try {
    const creditService = getServerCreditService();
    credits = await creditService.getUserCredits(user.id);
  } catch (error) {
    console.error("Failed to fetch user credits in auth():", error);
    // Continue with 0 credits rather than failing authentication
  }

  // Check admin status
  const isAdmin = isEmailAdmin(user.email);

  return {
    user: {
      id: user.id,
      email: user.email,
      type: "regular",
      credits,
      isAdmin,
    },
  };
}

/**
 * Server-side function to get the current session
 * This is exported separately to avoid conflicts with the client-side auth function
 */
export async function getServerSession(): Promise<SupabaseSession | null> {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return null;
  }

  // Get user credits
  let credits = 0;
  try {
    const creditService = getServerCreditService();
    credits = await creditService.getUserCredits(user.id);
  } catch (error) {
    console.error("Failed to fetch user credits in getServerSession():", error);
    // Continue with 0 credits rather than failing authentication
  }

  // Check admin status
  const isAdmin = isEmailAdmin(user.email);

  return {
    user: {
      id: user.id,
      email: user.email,
      type: "regular",
      credits,
      isAdmin,
    },
  };
}

/**
 * Sign in with email and password
 * @param credentials Email and password
 * @returns Result of sign in attempt
 */
export async function signIn(credentials: { email: string; password: string }, options?: { redirectTo?: string }) {
  "use server";

  const supabase = await createClient();

  const { data, error } = await supabase.auth.signInWithPassword({
    email: credentials.email,
    password: credentials.password,
  });

  if (error) {
    console.error("Error signing in:", error.message);
    return { ok: false, error: error.message };
  }

  if (options?.redirectTo) {
    redirect(options.redirectTo);
  }

  return { ok: true, data };
}

/**
 * Sign out the current user
 * @param options Options for sign out
 */
export async function signOut(options?: { redirectTo?: string }) {
  "use server";

  const supabase = await createClient();
  await supabase.auth.signOut();

  if (options?.redirectTo) {
    redirect(options.redirectTo);
  }
}
