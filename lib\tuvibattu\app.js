const { SolarDate, LunarDate } = require("@nghiavuive/lunar_date_vi");
const CONSTANTS = require("./constants");
const { getDungThanByNhatTru } = require("./ultilities/getBasicDungThan");
const Common = require("./ultilities/common");
const { getHiddenCan, getHiddenCans, calculateFiveElements } = Common;
const DaiVanUtil = require("./ultilities/daivan");
const { getDungThan } = require("./ultilities/dungthan");
const lunarDate = new SolarDate({ day: 21, month: 7, year: 1991, hour: 8 }).toLunarDate();
const Personality = require("./ultilities/personality");

const calculateThanSinhPhu = (elementalForces, nhatTru) => {
  const result = {};
  const relationship = Common.getElementRelationships(Common.getElementByCan(nhatTru));
  const total = Object.values(elementalForces).reduce((sum, value) => sum + value, 0);
  for (const key in relationship) {
    let sum = Common.getCanByElement(relationship[key]).reduce((sum, elem) => sum + elementalForces[elem], 0);
    if (key === "Tỷ Kiếp") {
      sum -= 100;
    }
    if (sum / total < 0.15) {
      result[key] = "Ít";
    } else {
      result[key] = "Nhiều";
    }
  }
  const thienQuan = Common.getThienQuan(nhatTru);
  if (elementalForces[thienQuan] / total < 0.15) {
    result["Thiên Quan"] = "Ít";
  } else {
    result["Thiên Quan"] = "Nhiều";
  }
  return result;
};

const calculateThanSinhPhuPercentage = (elementalForces, nhatTru) => {
  const result = {};
  const relationship = Common.getElementRelationships(Common.getElementByCan(nhatTru));
  const total = Object.values(elementalForces).reduce((sum, value) => sum + value, 0);
  for (const key in relationship) {
    let sum = Common.getCanByElement(relationship[key]).reduce((sum, elem) => sum + elementalForces[elem], 0);
    if (key === "Tỷ Kiếp") {
      sum -= 100;
    }
    result[key] = `${((sum / total) * 100).toFixed(2)}%`;
  }
  return result;
};

const checkLunarDate = (birthDate, hour, gender) => {
  const personInfo = Common.getLunarDateInfo(birthDate, hour);
  return personInfo;
};

const checkBatTu = (birthDate, hour, gender, timezone = "Asia/Ho_Chi_Minh") => {
  const personInfo = Common.getLunarDateInfo(birthDate, hour, timezone);
  const hiddenCans = getHiddenCans(personInfo);
  const hiddenCansRestructure = [];
  for (let i = 0; i < hiddenCans.length; i++) {
    hiddenCansRestructure.push({ [hiddenCans.personInfo[i]]: hiddenCans[i] });
  }
  const elementalForces = calculateFiveElements(hiddenCans);
  const sumOfElementalForces = {};
  for (let i = 0; i < Object.keys(elementalForces).length; i += 2) {
    sumOfElementalForces[Common.getElementByCan(Object.keys(elementalForces)[i])] =
      elementalForces[Object.keys(elementalForces)[i]] + elementalForces[Object.keys(elementalForces)[i + 1]];
  }
  const bonusCombo = checkBonusCombo(personInfo);
  if (bonusCombo) {
    sumOfElementalForces[Object.keys(bonusCombo)[0]] += bonusCombo[Object.keys(bonusCombo)[0]];
  }
  // console.log(sumOfElementalForces);
  const thanSinhPhu = calculateThanSinhPhuPercentage(elementalForces, personInfo.day.split(" ")[0]);
  const thapThan = Common.getThapThan(personInfo.day.split(" ")[0]);
  const thapThanElement = {};
  for (const key in thapThan) {
    thapThanElement[thapThan[key]] = thapThanElement[thapThan[key]]
      ? thapThanElement[thapThan[key]].push(Common.getElementByCan(key))
      : Common.getElementByCan(key);
  }
  // console.log(thapThanElement);
  const power = Personality.checkPower(personInfo);

  // Restructure Tứ trụ to include detailed can and chi information
  const tuTruRestructured = {};
  const pillars = ["year", "month", "day", "hour"];

  pillars.forEach((pillar) => {
    const pillarValue = personInfo[pillar];
    const [canName, chiName] = pillarValue.split(" ");

    // Get element for can
    const canElement = Common.getElementByCan(canName);

    // Get element for chi
    const chiElement = Common.getElementByChi(chiName);

    // Get hidden cans for chi
    const hiddenCans = Common.getHiddenCan(chiName);

    tuTruRestructured[pillar] = {
      can: {
        name: canName,
        element: canElement,
      },
      chi: {
        name: chiName,
        element: chiElement,
        hiddens: hiddenCans,
      },
    };
  });

  const result = {
    "Tứ trụ": personInfo,
    "Tứ trụ restructure": tuTruRestructured,
    "Lực lượng ngũ hành": sumOfElementalForces,
    "Thần sinh phù": thanSinhPhu,
    "Thập Thần": thapThan,
    "Tứ Trụ Power": power,
    "Thập Thần Element": thapThanElement,
  };
  const dungThan = getDungThan(result);
  return { ...result, "Dụng Thần": dungThan.dungThan, "Kỵ Thần": dungThan.kyThan, "Hỷ Thần": dungThan.hyThan };
};

const checkPersonality = (birthDate, hour, gender, timezone = "Asia/Ho_Chi_Minh") => {
  const personInfo = Common.getLunarDateInfo(birthDate, hour, timezone);
  const hiddenCans = [];
  hiddenCans.personInfo = [];
  const nhatTru = personInfo.day.split(" ")[0];
  // console.log(personInfo);
  for (const key in personInfo) {
    const hiddenCan = getHiddenCan(personInfo[key].split(" ")[1]);
    hiddenCans.push(hiddenCan);
    hiddenCans.personInfo.push(personInfo[key]);
  }
  const elementalForces = calculateFiveElements(hiddenCans);
  // sum 2 values next to each other of elementalForces, then assign it with corresponding elements using Common.getElementByCan
  const sumOfElementalForces = {};
  for (let i = 0; i < Object.keys(elementalForces).length; i += 2) {
    sumOfElementalForces[Common.getElementByCan(Object.keys(elementalForces)[i])] =
      elementalForces[Object.keys(elementalForces)[i]] + elementalForces[Object.keys(elementalForces)[i + 1]];
  }
  const bonusCombo = checkBonusCombo(personInfo);
  if (bonusCombo) {
    sumOfElementalForces[Object.keys(bonusCombo)[0]] += bonusCombo[Object.keys(bonusCombo)[0]];
  }
  // find the 2 keys with highest values in sumOfElementalForces
  const sortedSumOfElementalForces = Object.entries(sumOfElementalForces).sort((a, b) => b[1] - a[1]);
  const top2Keys = sortedSumOfElementalForces.slice(0, 2).map(([key]) => key);

  const resultsBoth = Common.findCharacteristicsByElements(CONSTANTS.NEW_PERSONALITY, top2Keys);
  const results1 = Common.findCharacteristicsByElements(CONSTANTS.NEW_PERSONALITY, [top2Keys[0]]);
  const results2 = Common.findCharacteristicsByElements(CONSTANTS.NEW_PERSONALITY, [top2Keys[1]]);

  const combinedResults = [...resultsBoth, ...results1, ...results2];

  const finalResults = {
    "Tính cách trưởng thành": "",
    "Mặt tối": "",
    "Khí chất": "",
    "Đặc điểm": "",
    "Khi trưởng thành": "",
    "Khi tiêu cực": "",
    "Khi rối loạn": "",
    "Cải thiện khí chất": "",
  };

  if (combinedResults.length > 0) {
    // Deduplicate results based on "Nguyên Mẫu Ngũ Hành" to avoid showing the same trait multiple times if it matches multiple searches
    const uniqueResults = combinedResults.filter(
      (item, index, self) => index === self.findIndex((t) => t["Nguyên Mẫu Ngũ Hành"] === item["Nguyên Mẫu Ngũ Hành"])
    );

    uniqueResults.forEach((item) => {
      finalResults["Tính cách trưởng thành"] += `${item["Tính cách trưởng thành"]}.`;
      finalResults["Mặt tối"] += `${item["Mặt Tối (chưa hoàn thiện/cần cải thiện)"]}.s`;
    });
    const extra = CONSTANTS.NEW_PERSONALITY_EXTRA[top2Keys[0]];
    for (const key in extra) {
      finalResults[key] = extra[key];
    }
    return finalResults;
  } else {
    console.log("No characteristics found for the given elements.");
  }
};

const checkBonusCombo = (personInfo) => {
  const diaChiArr = [];
  for (const key in personInfo) {
    diaChiArr.push(personInfo[key].split(" ")[1]);
  }
  const hasAllElements = (arr, elements) => {
    return elements.every((element) => arr.includes(element));
  };
  if (hasAllElements(diaChiArr, ["Hợi", "Tý", "Sửu"])) {
    return { Thuỷ: 100 };
  }
  if (hasAllElements(diaChiArr, ["Thân", "Tý", "Thìn"])) {
    return { Thuỷ: 200 };
  }
  if (hasAllElements(diaChiArr, ["Hợi", "Mão", "Mùi"])) {
    return { Mộc: 200 };
  }
  if (hasAllElements(diaChiArr, ["Dần", "Mão", "Thìn"])) {
    return { Mộc: 100 };
  }
  if (hasAllElements(diaChiArr, ["Dần", "Ngọ", "Tuất"])) {
    return { Hỏa: 200 };
  }
  if (hasAllElements(diaChiArr, ["Tỵ", "Ngọ", "Mùi"])) {
    return { Hỏa: 100 };
  }
  if (hasAllElements(diaChiArr, ["Tỵ", "Dậu", "Sửu"])) {
    return { Kim: 200 };
  }
  if (hasAllElements(diaChiArr, ["Thân", "Dậu", "Tuất"])) {
    return { Kim: 100 };
  }
};

// console.log(checkBatTu({ day: 23, month: 2, year: 1991 }, 13.8, "Nữ"));

module.exports = { checkLunarDate, checkPersonality, calculateThanSinhPhu, checkBatTu };
