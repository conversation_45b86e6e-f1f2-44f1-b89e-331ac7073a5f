import { tool } from "ai";
import { z } from "zod";

export const getCurrentDateTimeTool = tool({
  description: "Get the current date and time, in case user asks for something related to today's date or time, or this year, or this month, etc.",
  parameters: z.object({
    format: z.string().describe("Return the current date and time in format like '2025-05-21 10:00:00'"),
  }),
  execute: async ({ format }) => {
    return new Date().toISOString();
  },
});
