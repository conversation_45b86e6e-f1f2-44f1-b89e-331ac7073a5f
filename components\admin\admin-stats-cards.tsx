/**
 * Admin statistics cards component
 * Displays summary statistics for the admin dashboard
 */

"use client";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, CreditCard, TrendingUp, Activity } from "lucide-react";
import type { CreditAnalytics } from "@/lib/services/admin-service";

interface AdminStatsCardsProps {
  analytics: CreditAnalytics | null;
  isLoading?: boolean;
  error?: string | null;
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  description?: string;
  isLoading?: boolean;
}

function StatCard({ title, value, icon, description, isLoading }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <div className="size-4 text-muted-foreground">{icon}</div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{isLoading ? <div className="h-8 w-16 bg-muted animate-pulse rounded" /> : value}</div>
        {description && <p className="text-xs text-muted-foreground mt-1">{description}</p>}
      </CardContent>
    </Card>
  );
}

export function AdminStatsCards({ analytics, isLoading, error }: AdminStatsCardsProps) {
  if (error) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="col-span-full">
          <CardContent className="pt-6">
            <div className="text-center text-red-600">
              <p className="font-medium">Failed to load statistics</p>
              <p className="text-sm text-muted-foreground mt-1">{error}</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    }
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard
        title="Total Users"
        value={isLoading ? "..." : formatNumber(analytics?.total_users || 0)}
        icon={<Users className="size-4" />}
        description="Registered users"
        isLoading={isLoading}
      />

      <StatCard
        title="Credits Consumed"
        value={isLoading ? "..." : formatNumber(analytics?.total_credits_consumed || 0)}
        icon={<CreditCard className="size-4" />}
        description="Total credits used"
        isLoading={isLoading}
      />

      <StatCard
        title="Average Credits"
        value={isLoading ? "..." : analytics?.average_credits_per_user?.toFixed(1) || "0.0"}
        icon={<TrendingUp className="size-4" />}
        description="Per user balance"
        isLoading={isLoading}
      />

      <StatCard
        title="Active Users"
        value={isLoading ? "..." : formatNumber(analytics?.most_active_users?.length || 0)}
        icon={<Activity className="size-4" />}
        description="Users with activity"
        isLoading={isLoading}
      />
    </div>
  );
}

/**
 * PostHog Analytics Integration Card
 * Provides link to detailed analytics in PostHog dashboard
 */
export function PostHogAnalyticsCard() {
  const posthogProjectUrl = process.env.NEXT_PUBLIC_POSTHOG_HOST
    ? `${process.env.NEXT_PUBLIC_POSTHOG_HOST}/project/${process.env.NEXT_PUBLIC_POSTHOG_PROJECT_ID || "default"}`
    : "https://us.posthog.com";

  return (
    <Card className="col-span-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Activity className="size-5" />
          Detailed Analytics
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-muted-foreground">View detailed user behavior, feature usage, and performance metrics in PostHog</p>
            <div className="mt-2 space-y-1 text-xs text-muted-foreground">
              <p>• User activity and session tracking</p>
              <p>• Feature usage analytics</p>
              <p>• Tool call metrics and performance</p>
            </div>
          </div>
          <div className="flex flex-col gap-2">
            <a
              href={posthogProjectUrl}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
            >
              Open PostHog Dashboard
            </a>
            <p className="text-xs text-muted-foreground text-center">Opens in new tab</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Combined analytics section with both summary stats and PostHog integration
 */
interface AdminAnalyticsSectionProps {
  analytics: CreditAnalytics | null;
  isLoading?: boolean;
  error?: string | null;
}

export function AdminAnalyticsSection({ analytics, isLoading, error }: AdminAnalyticsSectionProps) {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight mb-4">Analytics Overview</h2>
        <AdminStatsCards analytics={analytics} isLoading={isLoading} error={error} />
      </div>

      <PostHogAnalyticsCard />
    </div>
  );
}
