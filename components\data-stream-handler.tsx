"use client";

import { useChat } from "@ai-sdk/react";
import { useEffect, useRef, useState } from "react";
import { artifactDefinitions, type ArtifactKind } from "./artifact";
import type { Suggestion } from "@/lib/db/schema";
import { initialArtifactData, useArtifact } from "@/hooks/use-artifact";
import { ThinkingAfterToolMessage } from "./message";

export type DataStreamDelta = {
  type:
    | "text-delta"
    | "code-delta"
    | "sheet-delta"
    | "image-delta"
    | "title"
    | "id"
    | "suggestion"
    | "clear"
    | "finish"
    | "kind"
    | "thinking-after-tool";
  content: string | Suggestion;
};

export function DataStreamHandler({ id }: { id: string }) {
  const { data: dataStream } = useChat({ id });
  const { artifact, setArtifact, setMetadata } = useArtifact();
  const lastProcessedIndex = useRef(-1);
  const [thinkingAfterTool, setThinkingAfterTool] = useState<string | null>(null);

  // Reset state when component unmounts
  useEffect(() => {
    return () => {
      // Reset state when component unmounts
      lastProcessedIndex.current = -1;
      setThinkingAfterTool(null);
    };
  }, []);

  useEffect(() => {
    if (!dataStream?.length) return;

    try {
      const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);
      lastProcessedIndex.current = dataStream.length - 1;

      (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {
        // Handle thinking after tool indicator
        if (delta.type === "thinking-after-tool") {
          setThinkingAfterTool(delta.content as string);
        }

        // Handle text delta (when LLM starts responding, clear thinking indicator)
        if (delta.type === "text-delta") {
          setThinkingAfterTool(null);
        }

        const artifactDefinition = artifactDefinitions.find((artifactDefinition) => artifactDefinition.kind === artifact.kind);

        if (artifactDefinition?.onStreamPart) {
          try {
            artifactDefinition.onStreamPart({
              streamPart: delta,
              setArtifact,
              setMetadata,
            });
          } catch (error) {
            console.error("Error in artifact stream handler:", error);
          }
        }

        setArtifact((draftArtifact) => {
          if (!draftArtifact) {
            return { ...initialArtifactData, status: "streaming" };
          }

          switch (delta.type) {
            case "id":
              return {
                ...draftArtifact,
                documentId: delta.content as string,
                status: "streaming",
              };

            case "title":
              return {
                ...draftArtifact,
                title: delta.content as string,
                status: "streaming",
              };

            case "kind":
              return {
                ...draftArtifact,
                kind: delta.content as ArtifactKind,
                status: "streaming",
              };

            case "clear":
              return {
                ...draftArtifact,
                content: "",
                status: "streaming",
              };

            case "finish":
              return {
                ...draftArtifact,
                status: "idle",
              };

            default:
              return draftArtifact;
          }
        });
      });
    } catch (error) {
      console.error("Error processing data stream:", error);
      // Reset state on error
      setThinkingAfterTool(null);
    }
  }, [dataStream, setArtifact, setMetadata, artifact]);

  return thinkingAfterTool ? <ThinkingAfterToolMessage toolName={thinkingAfterTool} /> : null;
}
