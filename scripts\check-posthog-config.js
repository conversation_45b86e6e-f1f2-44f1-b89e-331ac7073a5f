// Script to check PostHog configuration
require('dotenv').config({ path: '.env.local' });

console.log('Checking PostHog configuration:');
console.log('NODE_ENV:', process.env.NODE_ENV || 'Not set');
console.log('NEXT_PUBLIC_POSTHOG_KEY:', process.env.NEXT_PUBLIC_POSTHOG_KEY ? 'Set (value hidden)' : 'Not set');
console.log('NEXT_PUBLIC_POSTHOG_HOST:', process.env.NEXT_PUBLIC_POSTHOG_HOST || 'Not set');

// Check if we're in production mode
console.log('Is production environment:', process.env.NODE_ENV === 'production' ? 'Yes' : 'No');

// Check if we have the required environment variables
const hasRequiredEnvVars = Boolean(process.env.NEXT_PUBLIC_POSTHOG_KEY);
console.log('Has required environment variables:', hasRequiredEnvVars ? 'Yes' : 'No');

// Provide recommendations
if (!hasRequiredEnvVars) {
  console.log('\nRecommendations:');
  console.log('- Make sure NEXT_PUBLIC_POSTHOG_KEY is set in your .env.local file');
  console.log('- You can set NEXT_PUBLIC_POSTHOG_HOST to "https://us.i.posthog.com" if needed');
}
