/**
 * Admin Dashboard Page
 * Comprehensive admin interface for user and credit management
 */

"use client";

import { useState, useEffect, useCallback } from "react";
import { useIsAdmin } from "@/lib/hooks/use-is-admin";
import { AdminAnalyticsSection } from "@/components/admin/admin-stats-cards";
import { UserSearch } from "@/components/admin/user-search";
import { UserList } from "@/components/admin/user-list";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { toast } from "sonner";
import type { AdminUserListResponse, CreditAnalytics, UserSearchOptions, AdminUserData } from "@/lib/services/admin-service";

export default function AdminPage() {
  const { isAdmin, isLoading: isLoadingAuth, error: authError } = useIsAdmin();

  // State management
  const [userListData, setUserListData] = useState<AdminUserListResponse | null>(null);
  const [analyticsData, setAnalyticsData] = useState<CreditAnalytics | null>(null);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [isLoadingAnalytics, setIsLoadingAnalytics] = useState(false);
  const [userListError, setUserListError] = useState<string | null>(null);
  const [analyticsError, setAnalyticsError] = useState<string | null>(null);
  const [currentSearchOptions, setCurrentSearchOptions] = useState<UserSearchOptions>({});
  const [currentPage, setCurrentPage] = useState(1);

  // Fetch analytics data
  const fetchAnalytics = useCallback(async () => {
    setIsLoadingAnalytics(true);
    setAnalyticsError(null);

    try {
      const response = await fetch("/api/admin/analytics");

      if (!response.ok) {
        throw new Error(`Failed to fetch analytics: ${response.statusText}`);
      }

      const data = await response.json();
      setAnalyticsData(data);
    } catch (error) {
      console.error("Failed to fetch analytics:", error);
      setAnalyticsError(error instanceof Error ? error.message : "Failed to fetch analytics");
    } finally {
      setIsLoadingAnalytics(false);
    }
  }, []);

  // Fetch user list data
  const fetchUsers = useCallback(async (options: UserSearchOptions = {}, page = 1) => {
    setIsLoadingUsers(true);
    setUserListError(null);

    try {
      const searchParams = new URLSearchParams();

      if (options.search) searchParams.set("search", options.search);
      if (options.minCredits !== undefined) searchParams.set("minCredits", options.minCredits.toString());
      if (options.maxCredits !== undefined) searchParams.set("maxCredits", options.maxCredits.toString());
      searchParams.set("page", page.toString());
      searchParams.set("limit", "20");

      const response = await fetch(`/api/admin/users?${searchParams.toString()}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch users: ${response.statusText}`);
      }

      const data = await response.json();
      setUserListData(data);
    } catch (error) {
      console.error("Failed to fetch users:", error);
      setUserListError(error instanceof Error ? error.message : "Failed to fetch users");
    } finally {
      setIsLoadingUsers(false);
    }
  }, []);

  // Handle search/filter changes
  const handleSearch = useCallback(
    (options: UserSearchOptions) => {
      setCurrentSearchOptions(options);
      setCurrentPage(1);
      fetchUsers(options, 1);
    },
    [fetchUsers]
  );

  // Handle page changes
  const handlePageChange = useCallback(
    (page: number) => {
      setCurrentPage(page);
      fetchUsers(currentSearchOptions, page);
    },
    [currentSearchOptions, fetchUsers]
  );

  // Handle user actions
  const handleUserAction = useCallback(
    (action: string, user: AdminUserData) => {
      if (action === "refresh") {
        // Refresh both user list and analytics after credit adjustment
        fetchUsers(currentSearchOptions, currentPage);
        fetchAnalytics();
        toast.success("Data refreshed successfully");
      } else {
        toast.info(`Action "${action}" for user ${user.email} - Coming in Phase 4`);
      }
    },
    [currentSearchOptions, currentPage, fetchUsers, fetchAnalytics]
  );

  // Initial data fetch
  useEffect(() => {
    if (isAdmin) {
      fetchAnalytics();
      fetchUsers();
    }
  }, [isAdmin, fetchAnalytics, fetchUsers]);

  // Loading state for authentication
  if (isLoadingAuth) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full size-8 border-b-2 border-primary mx-auto mb-4" />
          <p>Loading admin status...</p>
        </div>
      </div>
    );
  }

  // Authentication error
  if (authError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center text-red-600">
          <h1 className="text-2xl font-bold mb-4">Error</h1>
          <p>Failed to verify admin status: {authError}</p>
        </div>
      </div>
    );
  }

  // Access denied
  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p>You do not have permission to access this page.</p>
          <p className="text-sm text-muted-foreground mt-2">Admin access is required.</p>
        </div>
      </div>
    );
  }

  return (
    <SidebarProvider>
      <SidebarInset>
        <div className="flex h-screen flex-col">
          {/* Header */}
          <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
            <h1 className="text-2xl font-bold">Admin Dashboard</h1>
          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-auto p-6 space-y-8">
            {/* Analytics Section */}
            <AdminAnalyticsSection analytics={analyticsData} isLoading={isLoadingAnalytics} error={analyticsError} />

            {/* User Management Section */}
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold tracking-tight mb-4">User Management</h2>

                {/* Search and Filters */}
                <UserSearch onSearch={handleSearch} isLoading={isLoadingUsers} totalResults={userListData?.total} />
              </div>

              {/* User List */}
              <UserList
                data={userListData}
                isLoading={isLoadingUsers}
                error={userListError}
                onPageChange={handlePageChange}
                onUserAction={handleUserAction}
              />
            </div>
          </main>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
