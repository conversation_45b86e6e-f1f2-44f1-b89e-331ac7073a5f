import { tool } from "ai";
import { z } from "zod";
import { getLunarDateInfo } from "@/lib/tuvibattu/ultilities/common";

/**
 * A simple test tool to verify that tool calling is working
 * This tool will echo back the input with a timestamp
 */
export const checkLunarDateTool = tool({
  description: "Use this tool to get the Lunar date based on their birthdate, time of birth. ",
  parameters: z.object({
    birthDate: z
      .object({
        day: z.number().describe("The day of the birth date"),
        month: z.number().describe("The month of the birth date"),
        year: z.number().describe("The year of the birth date"),
      })
      .describe("The birth date of the person in object format {day: number, month: number, year: number}"),
    hour: z.number().describe("The birth hour of the person, should be in 24 hours format"),
  }),
  execute: async ({ birthDate, hour }) => {
    return getLunarDateInfo(birthDate, hour);
  },
});
