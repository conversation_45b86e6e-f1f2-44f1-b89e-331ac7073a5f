"use client";

import { createClient } from "@/lib/supabase/client";

export type UserType = "regular";

export interface SupabaseUser {
  id: string;
  email?: string | null;
  type: UserType;
  credits: number;
  isAdmin: boolean;
}

export interface SupabaseSession {
  user: SupabaseUser;
}

/**
 * Client-side function to get the current user
 * Following Supabase best practices - no custom timeout logic
 */
export async function getUser() {
  const supabase = createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();
  return user;
}

/**
 * Client-side function to sign in
 */
export async function signIn(credentials: { email: string; password: string }) {
  const supabase = createClient();
  const result = await supabase.auth.signInWithPassword({
    email: credentials.email,
    password: credentials.password,
  });

  if (result.error) {
    return { ok: false, error: result.error.message };
  }

  return { ok: true, data: result.data };
}

/**
 * Client-side function to sign in with Google OAuth
 */
export async function signInWithGoogle() {
  const supabase = createClient();
  // Use the environment variable for site URL, falling back to window.location.origin
  const siteUrl = process.env.NEXT_PUBLIC_SITE_URL || window.location.origin;

  const { data, error } = await supabase.auth.signInWithOAuth({
    provider: "google",
    options: {
      redirectTo: `${siteUrl}/auth/callback`,
    },
  });

  if (error) {
    return { ok: false, error: error.message };
  }

  return { ok: true, data };
}

/**
 * Client-side function to sign out
 */
export async function signOut() {
  // First, clear PostHog identity and tracking data
  try {
    if (typeof window !== "undefined" && window.posthog) {
      // Track the logout event before resetting
      try {
        window.posthog.capture("user_logged_out");
      } catch (e) {
        console.warn("Failed to capture logout event:", e);
      }

      // Reset PostHog identity with a small delay to ensure the event is sent
      setTimeout(() => {
        try {
          // Reset PostHog identity
          window.posthog?.reset();

          // Optionally flush any pending events
          if (window.posthog?.flush) {
            window.posthog.flush();
          }

          // Clear any PostHog-related cookies
          document.cookie = "ph_hasher=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
          document.cookie = "posthog_distinct_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        } catch (e) {
          console.warn("Error during PostHog cleanup:", e);
        }
      }, 100);
    }
  } catch (e) {
    console.error("Error clearing analytics data:", e);
  }

  // Then sign out from Supabase
  try {
    const supabase = createClient();
    const result = await supabase.auth.signOut();

    if (result.error) {
      console.error("Error signing out from Supabase:", result.error);
      return { ok: false, error: result.error.message };
    }

    // Clear any session-related cookies or local storage
    if (typeof window !== "undefined") {
      // Clear any additional cookies or storage if needed
      localStorage.removeItem("supabase.auth.token");
    }

    return { ok: true };
  } catch (error) {
    console.error("Unexpected error during sign out:", error);
    return { ok: false, error: error instanceof Error ? error.message : "Unknown error during sign out" };
  }
}

// No longer need to re-export handlers
