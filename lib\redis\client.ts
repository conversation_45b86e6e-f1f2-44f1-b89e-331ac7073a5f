import { createClient, type RedisClientType } from "redis";

const redisUrl = process.env.REDIS_URL;
let redisClient: RedisClientType | null = null;
let redisConnectionPromise: Promise<void> | null = null;
let isConnecting = false;

if (redisUrl) {
  console.log("[REDIS] Initializing shared Redis client...");
  redisClient = createClient({
    url: redisUrl,
    socket: {
      connectTimeout: 5000, // 5-second connect timeout
      reconnectStrategy: (retries) => Math.min(retries * 50, 1000), // Standard reconnect strategy
    },
  }) as RedisClientType;

  redisClient.on("error", (err) => console.error("[REDIS] Shared Redis Client Error", err));
  redisClient.on("connect", () => console.log("[REDIS] Shared Redis client connected"));
  redisClient.on("reconnecting", () => console.log("[REDIS] Shared Redis client reconnecting"));
  redisClient.on("end", () => console.log("[REDIS] Shared Redis client connection ended"));

  const connectToRedis = async () => {
    if (isConnecting || (redisClient && redisClient.isOpen)) {
      return;
    }
    isConnecting = true;
    try {
      console.log("[REDIS] Attempting to connect shared Redis client...");
      await redisClient!.connect();
    } catch (err) {
      console.error("[REDIS] Failed to connect shared Redis client on init/reconnect:", err);
      // In a serverless environment, retrying might be handled by subsequent invocations.
      // For persistent servers, more robust retry logic or circuit breakers might be needed.
    } finally {
      isConnecting = false;
    }
  };

  // Store the promise for the initial connection attempt
  redisConnectionPromise = connectToRedis();
} else {
  console.warn("[REDIS] REDIS_URL not found, shared Redis client not initialized.");
}

export async function getRedisClient(): Promise<RedisClientType | null> {
  if (!redisClient) {
    console.warn("[REDIS] Attempted to get Redis client, but it's not initialized (REDIS_URL missing or init failed).");
    return null;
  }

  // Wait for the initial connection attempt to resolve if it's still pending
  if (redisConnectionPromise) {
    try {
      await redisConnectionPromise;
      redisConnectionPromise = null; // Clear promise once resolved
    } catch (e) {
      // Error already logged by connectToRedis
      return null; // Initial connection failed
    }
  }

  if (!redisClient.isOpen && !isConnecting) {
    console.warn("[REDIS] Shared client is not open. Attempting to reconnect...");
    // Attempt to reconnect if not already open.
    // This handles cases where the connection might have dropped after initial setup.
    isConnecting = true;
    try {
      await redisClient.connect(); // Attempt to connect again
    } catch (err) {
      console.error("[REDIS] Failed to reconnect shared Redis client:", err);
      isConnecting = false;
      return null; // Return null if reconnect fails
    }
    isConnecting = false;
  }

  if (!redisClient.isOpen) {
    console.error("[REDIS] Shared client is still not open after connection attempt.");
    return null;
  }

  return redisClient;
}

export async function checkRedisHealthWithSharedClient(): Promise<boolean> {
  const client = await getRedisClient();
  if (!client) {
    // getRedisClient now returns null if client is not available/connected
    console.warn("[REDIS] Health check: Shared client not available or not connected.");
    return false;
  }
  try {
    const pong = await client.ping();
    return pong === "PONG";
  } catch (error) {
    console.error("[REDIS] Health check with shared client failed:", error);
    return false;
  }
}

// Graceful shutdown for serverful environments (won't reliably run in serverless)
// Consider if Vercel has specific hooks for cleanup if needed.
// For serverless, connections are typically short-lived per invocation.
// The goal here is more about reusing the client object across requests within the same invocation/instance.
// process.on('SIGTERM', async () => {
//   if (redisClient && redisClient.isOpen) {
//     console.log('[REDIS] Quitting shared Redis client on SIGTERM...');
//     await redisClient.quit();
//   }
// });
