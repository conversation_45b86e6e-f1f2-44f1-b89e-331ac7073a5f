/**
 * Credit system type definitions
 * These types define the structure for credit operations, transactions, and related functionality
 */

export interface CreditTransaction {
  id: string;
  user_id: string;
  amount: number;
  description: string;
  transaction_type: "deduction" | "addition" | "admin_adjustment";
  created_at: string;
}

export interface CreditBalance {
  credits: number;
  last_updated?: string;
}

export interface CreditDeductionResult {
  success: boolean;
  balance: number;
  deducted?: number;
  error?: string;
}

export interface CreditAdditionResult {
  success: boolean;
  balance: number;
  added?: number;
  error?: string;
}

export interface CreditHistoryQuery {
  userId: string;
  limit?: number;
  offset?: number;
  transactionType?: "deduction" | "addition" | "admin_adjustment";
  startDate?: string;
  endDate?: string;
}

export interface CreditValidationResult {
  isValid: boolean;
  currentBalance: number;
  requiredAmount: number;
  message?: string;
}

// Custom error classes for credit operations
export class InsufficientCreditsError extends Error {
  constructor(public currentBalance: number, public requiredAmount: number) {
    super(`Insufficient credits: ${currentBalance} available, ${requiredAmount} required`);
    this.name = "InsufficientCreditsError";
  }
}

export class CreditOperationError extends Error {
  constructor(message: string, public cause?: unknown) {
    super(message);
    this.name = "CreditOperationError";
  }
}

export class UserNotFoundError extends Error {
  constructor(userId: string) {
    super(`User not found: ${userId}`);
    this.name = "UserNotFoundError";
  }
}

// Type guards for runtime type checking
export function isCreditDeductionResult(obj: any): obj is CreditDeductionResult {
  return typeof obj === "object" && obj !== null && typeof obj.success === "boolean" && typeof obj.balance === "number";
}

export function isCreditAdditionResult(obj: any): obj is CreditAdditionResult {
  return typeof obj === "object" && obj !== null && typeof obj.success === "boolean" && typeof obj.balance === "number";
}

export function isCreditTransaction(obj: any): obj is CreditTransaction {
  return (
    typeof obj === "object" &&
    obj !== null &&
    typeof obj.id === "string" &&
    typeof obj.user_id === "string" &&
    typeof obj.amount === "number" &&
    typeof obj.description === "string" &&
    ["deduction", "addition", "admin_adjustment"].includes(obj.transaction_type) &&
    typeof obj.created_at === "string"
  );
}

// Constants for credit system
export const CREDIT_CONSTANTS = {
  DEFAULT_CREDITS: 5,
  MIN_CREDITS: 0,
  COST_PER_QUESTION: 1,
  COST_PER_TOOL_USE: 1,
  MAX_TRANSACTION_HISTORY: 1000,
  DEFAULT_HISTORY_LIMIT: 50,
} as const;

// Enhanced credit package definitions with payment provider support
export interface CreditPackage {
  id: string;
  name: string;
  nameEn?: string;
  credits: number;
  priceVnd: number;
  priceUsd?: number;
  currency: string;
  description: string;
  features: string[];
  uiConfig?: {
    color?: string;
    popular?: boolean;
    sortOrder?: number;
  };
  providerConfigs?: Record<string, any>;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

// Updated credit packages based on payment page
export const CREDIT_PACKAGES: CreditPackage[] = [
  {
    id: "basic",
    name: "Cơ Bản",
    nameEn: "Basic",
    credits: 50,
    priceVnd: 120000,
    priceUsd: 5,
    currency: "VND",
    description: "Phù hợp cho người dùng không thường xuyên",
    features: ["50 tin nhắn chat", "Bao gồm sử dụng công cụ", "Sử dụng trong 30 ngày"],
    uiConfig: {
      color: "bg-gray-50 dark:bg-gray-900/50 border-gray-200 dark:border-gray-800",
      popular: false,
      sortOrder: 1,
    },
    isActive: true,
  },
  {
    id: "pro",
    name: "VIP",
    nameEn: "Pro",
    credits: 250,
    priceVnd: 500000,
    priceUsd: 20,
    currency: "VND",
    description: "Gói tốt nhất cho người dùng thường xuyên",
    features: [
      "250 tin nhắn chat",
      "Bao gồm tất cả công cụ",
      "Sử dụng trong 30 ngày",
      "Hỗ trợ ưu tiên",
      "Được 1 lần đọc lá số trực tiếp từ thầy Việt",
    ],
    uiConfig: {
      color: "bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800",
      popular: true,
      sortOrder: 2,
    },
    isActive: true,
  },
  {
    id: "enterprise",
    name: "VIP Pro",
    nameEn: "Enterprise",
    credits: 2000,
    priceVnd: 3000000,
    priceUsd: 125,
    currency: "VND",
    description: "Dành cho người dùng chuyên sâu",
    features: ["2000 tin nhắn chat", "Tất cả tính năng cao cấp", "Sử dụng trong 60 ngày", "Được 3 lần đọc lá số trực tiếp từ thầy Việt"],
    uiConfig: {
      color: "bg-emerald-50 dark:bg-emerald-900/20 border-emerald-200 dark:border-emerald-800",
      popular: false,
      sortOrder: 3,
    },
    isActive: true,
  },
] as const;

// Admin-specific types
export interface AdminCreditOperation {
  userId: string;
  amount: number;
  description: string;
  adminId: string;
  reason: string;
}

export interface CreditUsageAnalytics {
  totalCreditsConsumed: number;
  totalUsers: number;
  averageCreditsPerUser: number;
  mostActiveUsers: Array<{
    userId: string;
    email: string;
    creditsUsed: number;
  }>;
  dailyUsage: Array<{
    date: string;
    creditsUsed: number;
  }>;
}

export type TransactionType = "deduction" | "addition" | "admin_adjustment";

// Rate limiting types
export interface CreditRateLimit {
  maxDeductionsPerMinute: number;
  maxDeductionsPerHour: number;
  windowSizeMinutes: number;
  windowSizeHours: number;
}

export const CREDIT_RATE_LIMITS: CreditRateLimit = {
  maxDeductionsPerMinute: 10,
  maxDeductionsPerHour: 100,
  windowSizeMinutes: 1,
  windowSizeHours: 1,
} as const;

// Caching types
export interface CreditCacheEntry {
  credits: number;
  timestamp: number;
  ttl: number;
}

export const CREDIT_CACHE_CONFIG = {
  TTL_SECONDS: 300, // 5 minutes
  REDIS_KEY_PREFIX: "credit_balance:",
  RATE_LIMIT_KEY_PREFIX: "credit_rate_limit:",
} as const;

// Enhanced error types for credit operations
export class CreditRateLimitError extends Error {
  constructor(public limitType: "minute" | "hour", public currentCount: number, public maxAllowed: number, public resetTime: Date) {
    super(`Credit operation rate limit exceeded: ${currentCount}/${maxAllowed} ${limitType}ly. Resets at ${resetTime.toISOString()}`);
    this.name = "CreditRateLimitError";
  }
}

export class CreditCacheError extends Error {
  constructor(message: string, public cause?: unknown) {
    super(message);
    this.name = "CreditCacheError";
  }
}
