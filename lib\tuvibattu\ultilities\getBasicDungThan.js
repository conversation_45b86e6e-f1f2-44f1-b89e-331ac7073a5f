const getDungThanByNhatTru = (nhatTru, birthMonth) => {
  const can1 = getCan1(nhatTru, birthMonth);
  const can2 = getCan2(nhatTru, birthMonth);
  const can3 = getCan3(nhatTru, birthMonth);
  const can4 = getCan4(nhatTru, birthMonth);
  return {
    can1,
    can2,
    can3,
    can4,
  };
};

const getCan1 = (nhatTru, birthMonth) => {
  const kyLookup = ["<PERSON><PERSON>h", "<PERSON>i<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>u<PERSON>", "<PERSON>u<PERSON>", "<PERSON>u<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"];
  const canhLookup = ["<PERSON><PERSON>u", "<PERSON>inh", "<PERSON>i<PERSON><PERSON>", "<PERSON>hâ<PERSON>", "Nhâm", "<PERSON><PERSON>h", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>in<PERSON>"];
  const tanLookup = ["Kỷ", "<PERSON>hâ<PERSON>", "<PERSON>hâ<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>hâ<PERSON>", "Nhâ<PERSON>", "/", "<PERSON>hâ<PERSON>", "Nhâm", "Nhâm", "<PERSON><PERSON>h", "Bính"];
  const nhamLookup = ["Canh", "Mậu", "Giáp", "Nhâm", "Quý", "Tân", "Mậu", "Giáp", "Giáp", "Mậu", "Mậu", "Bính"];
  const quyLookup = ["Tân", "Canh", "Bính", "Tân", "Canh", "Canh", "Đinh", "Tân", "Tân", "Canh", "Bính", "Bính"];
  const dinhLookup = ["Giáp", "Canh", "Giáp", "Giáp", "Nhâm", "Giáp", "Giáp", "Giáp", "Giáp", "Giáp", "Giáp", "Giáp"];
  const giapLookup = ["Bính", "Canh", "Canh", "Quý", "Quý", "Quý", "Canh", "Canh", "Canh", "Canh", "Đinh", "Đinh"];
  const atLookup = ["Bính", "Bính", "Quý", "Quý", "Quý", "Quý", "Bính", "Quý", "Quý", "Bính", "Bính", "Bính"];
  const binhLookup = ["Nhâm", "Nhâm", "Nhâm", "Nhâm", "Nhâm", "Nhâm", "Nhâm", "Nhâm", "Giáp", "Giáp", "Nhâm", "Nhâm"];
  const mauLookup = ["Bính", "Bính", "Giáp", "Giáp", "Nhâm", "Quý", "Bính", "Bính", "Giáp", "Giáp", "Bính", "Bính"];

  const lookup = {
    Kỷ: kyLookup,
    Canh: canhLookup,
    Tân: tanLookup,
    Nhâm: nhamLookup,
    Quý: quyLookup,
    Đinh: dinhLookup,
    Giáp: giapLookup,
    Ất: atLookup,
    Bính: binhLookup,
    Mậu: mauLookup,
  };

  if (lookup[nhatTru]?.[birthMonth - 1]) {
    return lookup[nhatTru][birthMonth - 1];
  }

  return null;
};

const getCan2 = (nhatTru, birthMonth) => {
  const dinhLookup = ["Canh", "Giáp", "Canh", "Canh", "Canh", "Nhâm", "Canh", "Canh", "Canh", "Canh", "Canh", "Canh"];
  const giapLookup = ["Quý", "Bính", "Đinh", "Đinh", "Đinh", "Canh", "Đinh", "Đinh", "Giáp", "Đinh", "Canh", "Canh"];
  const atLookup = ["Quý", "Quý", "Bính", "/", "Bính", "Bính", "Quý", "Bính", "Tân", "Mậu", "/", "/"];
  const binhLookup = ["Canh", "Kỷ", "Giáp", "Canh", "Canh", "Canh", "Mậu", "Quý", "Nhâm", "Mậu", "Mậu", "Giáp"];
  const mauLookup = ["Giáp", "Giáp", "Bính", "Bính", "Giáp", "Bính", "Quý", "Quý", "Bính", "Bính", "Giáp", "Giáp"];
  const kyLookup = ["Canh", "Quý", "Quý", "Bính", "Bính", "Bính", "Quý", "Quý", "Bính", "Giáp", "Giáp", "Giáp"];
  const canhLookup = ["Giáp", "Giáp", "Đinh", "Bính", "Quý", "Giáp", "Giáp", "Giáp", "Nhâm", "Giáp", "Giáp", "Giáp"];
  const tanLookup = ["Nhâm", "Giáp", "Giáp", "Giáp", "Kỷ", "Giáp", "/", "Giáp", "Giáp", "Bính", "Mậu", "Mậu"];
  const nhamLookup = ["Bính", "Tân", "Canh", "Tân", "Canh", "Giáp", "Đinh", "Canh", "Bính", "Bính", "Bính", "Giáp"];
  const quyLookup = ["Bính", "Tân", "Tân", "Canh", "Tân", "Tân", "/", "Bính", "Giáp", "Tân", "Tân", "Đinh"];

  const lookup = {
    Đinh: dinhLookup,
    Giáp: giapLookup,
    Ất: atLookup,
    Bính: binhLookup,
    Mậu: mauLookup,
    Kỷ: kyLookup,
    Canh: canhLookup,
    Tân: tanLookup,
    Nhâm: nhamLookup,
    Quý: quyLookup,
  };

  if (lookup[nhatTru]?.[birthMonth - 1]) {
    return lookup[nhatTru][birthMonth - 1];
  }

  return null;
};

const getCan3 = (nhatTru, birthMonth) => {
  const dinhLookup = ["/", "/", "Mậu", "/", "Quý", "Canh", "Bính", "Bính", "Mậu", "Mậu", "/", "/"];
  const giapLookup = ["/", "Đinh", "Nhâm", "Canh", "Canh", "Đinh", "Nhâm", "Bính", "Đinh", "Bính", "Bính", "Bính"];
  const atLookup = ["/", "/", "Mậu", "/", "/", "/", "Kỷ", "Đinh", "/", "/", "/", "/"];
  const binhLookup = ["/", "Mậu", "/", "Quý", "/", "/", "/", "/", "/", "Canh", "Kỷ", "/"];
  const mauLookup = ["Quý", "Quý", "Quý", "Quý", "Bính", "Giáp", "Giáp", "/", "Quý", "/", "/", "/"];
  const kyLookup = ["Giáp", "Bính", "Giáp", "/", "/", "/", "Canh", "Tân", "Quý", "Mậu", "Mậu", "Mậu"];
  const canhLookup = ["Bính", "Bính", "Nhâm", "Đinh", "Mậu", "/", "/", "Bính", "/", "Bính", "Bính", "Bính"];
  const tanLookup = ["Canh", "/", "Bính", "Quý", "Quý", "Mậu", "/", "Đinh", "/", "/", "Nhâm", "Nhâm"];
  const nhamLookup = ["Mậu", "Canh", "Bính", "Canh", "Tân", "/", "/", "/", "/", "Canh", "/", "Đinh"];
  const quyLookup = ["Canh", "/", "Giáp", "/", "Quý", "Quý", "/", "/", "Quý", "Mậu", "/", "Canh"];

  const lookup = {
    Kỷ: kyLookup,
    Canh: canhLookup,
    Tân: tanLookup,
    Nhâm: nhamLookup,
    Quý: quyLookup,
    Đinh: dinhLookup,
    Giáp: giapLookup,
    Ất: atLookup,
    Bính: binhLookup,
    Mậu: mauLookup,
  };

  if (lookup[nhatTru]?.[birthMonth - 1]) {
    return lookup[nhatTru][birthMonth - 1];
  }

  return null;
};

const getCan4 = (nhatTru, birthMonth) => {
  const dinhLookup = ["/", "/", "/", "/", "/", "/", "Mậu", "Mậu", "/", "Quý", "/", "/"];
  const giapLookup = ["/", "/", "/", "/", "Canh", "/", "/", "/", "Nhâm", "Mậu", "/", "/"];
  const binhLookup = ["/", "/", "/", "/", "/", "/", "/", "/", "/", "Nhâm", "/", "/"];
  const mauLookup = ["/", "/", "/", "/", "/", "/", "/", "/", "/", "/", "/", "/"];
  const kyLookup = ["Mậu", "/", "/", "/", "/", "/", "/", "/", "/", "/", "/", "/"];
  const canhLookup = ["Nhâm", "Canh", "Quý", "Mậu", "/", "/", "/", "/", "/", "/", "/", "/"];
  const tanLookup = ["/", "/", "/", "/", "/", "/", "/", "/", "/", "/", "Giáp", "Kỷ"];
  const nhamLookup = ["/", "/", "/", "Quý", "/", "/", "/", "/", "/", "/", "/", "/"];
  const quyLookup = ["/", "/", "/", "/", "/", "/", "/", "/", "Nhâm", "Đinh", "/", "Tân"];

  const lookup = {
    Đinh: dinhLookup,
    Giáp: giapLookup,
    Bính: binhLookup,
    Mậu: mauLookup,
    Kỷ: kyLookup,
    Canh: canhLookup,
    Tân: tanLookup,
    Nhâm: nhamLookup,
    Quý: quyLookup,
  };

  if (lookup[nhatTru]?.[birthMonth - 1]) {
    return lookup[nhatTru][birthMonth - 1];
  }

  return null;
};

module.exports = {
  getDungThanByNhatTru,
};
