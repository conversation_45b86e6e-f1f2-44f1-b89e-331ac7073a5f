<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>T<PERSON> Trụ Game Card - Themed</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #3c3836;
            /* Dark background for page to make card pop */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* Theme Colors */
        :root {
            --theme-bg-cream: #fbf1c7;
            --theme-accent-red: #cc241d;
            --theme-accent-gold: #fabd2f;
            --theme-text-dark: #3c3836;
            --theme-text-light-gray: #7c6f64;
            /* Lighter gray for less emphasis */
            --theme-border-subtle: #d5c4a1;
            /* Subtle border for light theme */

            --color-water: #0077b6;
            /* Blue */
            --color-earth: #d4a373;
            /* Earthy Brown/Tan */
            --color-metal: #808080;
            /* Grey */
            --color-wood: #556b2f;
            /* Dark Olive Green - Also for beneficial gods */
            --color-fire: #cc241d;
            /* Theme Red - Also for unfavorable gods */
        }

        .game-card {
            width: 380px;
            background-color: var(--theme-bg-cream);
            border: 3px solid var(--theme-accent-gold);
            border-radius: 1.5rem;
            /* 24px */
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1), 0 0 20px rgba(250, 189, 47, 0.2);
            color: var(--theme-text-dark);
            padding: 1.25rem;
            /* 20px */
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            /* 12px */
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 2px solid var(--theme-accent-gold);
            padding-bottom: 0.6rem;
        }

        .card-header .title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--theme-accent-red);
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.05);
        }

        .card-header .type-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            font-weight: 700;
            font-size: 1.1rem;
            color: var(--theme-bg-cream);
            border: 1px solid var(--theme-accent-gold);
        }

        .type-icon.water-bg-icon {
            background-color: var(--color-water);
        }


        .image-placeholder {
            height: 100px;
            background-color: rgba(0, 0, 0, 0.03);
            border-radius: 0.75rem;
            /* 12px */
            display: flex;
            justify-content: center;
            align-items: center;
            border: 2px dashed var(--theme-accent-gold);
        }

        .image-placeholder .symbol {
            font-size: 3rem;
            /* 48px */
            color: var(--theme-accent-gold);
            opacity: 0.9;
        }

        .pillars-section {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 0.6rem;
            background-color: rgba(0, 0, 0, 0.02);
            padding: 0.6rem;
            border-radius: 0.5rem;
            border: 1px solid var(--theme-border-subtle);
        }

        .pillar {
            background-color: rgba(255, 255, 255, 0.3);
            padding: 0.5rem;
            border-radius: 0.375rem;
            /* 6px */
            text-align: center;
            border: 1px solid var(--theme-border-subtle);
        }

        .pillar-label {
            display: block;
            font-size: 0.75rem;
            font-weight: 600;
            color: var(--theme-text-light-gray);
            margin-bottom: 0.2rem;
        }

        .pillar-value {
            font-size: 1rem;
            font-weight: 700;
        }

        .can-quy,
        .can-nham,
        .chi-hoi,
        .chi-ty {
            color: var(--color-water);
        }

        .chi-mui,
        .chi-thin {
            color: var(--color-earth);
        }

        .can-canh {
            color: var(--color-metal);
        }


        .section-title {
            font-size: 1rem;
            /* Adjusted for column layout */
            font-weight: 700;
            color: var(--theme-accent-red);
            margin-top: 0.25rem;
            /* Adjusted margin top for spacing */
            margin-bottom: 0.4rem;
            text-align: center;
            padding-bottom: 0.35rem;
            border-bottom: 1px solid var(--theme-border-subtle);
        }

        .stats-section .stat-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-size: 0.85rem;
            margin-bottom: 0.35rem;
        }

        .stats-section .stat-item>span:first-child {
            flex-basis: 35%;
            color: var(--theme-text-dark);
        }

        .stats-section .stat-item>span:last-child {
            flex-basis: 15%;
            text-align: right;
            font-weight: 600;
            color: var(--theme-text-dark);
        }

        .stat-bar-container {
            flex-grow: 1;
            height: 12px;
            background-color: #e0e0e0;
            border-radius: 0.25rem;
            /* 4px */
            margin: 0 0.6rem;
            overflow: hidden;
            border: 1px solid var(--theme-border-subtle);
        }

        .stat-bar {
            height: 100%;
            border-radius: 0.25rem;
            /* 4px */
            transition: width 0.5s ease-in-out;
        }

        .water-bar {
            background-color: var(--color-water);
        }

        .earth-bar {
            background-color: var(--color-earth);
        }

        .metal-bar {
            background-color: var(--color-metal);
        }

        .wood-bar {
            background-color: var(--color-wood);
        }

        .fire-bar {
            background-color: var(--color-fire);
        }

        /* Container for the two gods columns */
        .gods-columns-container {
            display: flex;
            gap: 0.75rem;
            /* Space between the two columns */
            margin-top: 0.5rem;
            /* Overall top margin for this section */
        }

        .gods-section {
            flex: 1;
            /* Each column takes equal width */
            display: flex;
            flex-direction: column;
            align-items: center;
            /* Center title and list within the column */
        }

        .gods-section .section-title {
            /* Ensure title is full width of its column */
            width: 100%;
        }

        .gods-list {
            list-style: none;
            padding: 0;
            font-size: 0.85rem;
            /* Adjusted font size for gods in columns */
            display: flex;
            flex-direction: column;
            /* Stack gods vertically within each column */
            gap: 0.4rem;
            /* Space between god items */
            align-items: center;
            /* Center god items in the column */
            width: 100%;
            /* Ensure list takes full width of column */
        }

        .god-item {
            padding: 0.3rem 0.6rem;
            border-radius: 0.375rem;
            /* 6px */
            font-weight: 600;
            text-align: center;
            border: 1px solid var(--theme-border-subtle);
            background-color: rgba(255, 255, 255, 0.2);
            width: 90%;
            /* Make god items take most of the column width */
            box-sizing: border-box;
        }

        .god-item.dung-than {
            /* Useful God */
            color: var(--color-wood);
            border-color: var(--color-wood);
        }

        .god-item.ky-than {
            /* Unfavorable God */
            color: var(--color-fire);
            border-color: var(--color-fire);
        }


        .card-footer {
            font-size: 0.7rem;
            text-align: center;
            color: var(--theme-text-light-gray);
            padding-top: 0.6rem;
            border-top: 1px solid var(--theme-border-subtle);
            margin-top: auto;
        }
    </style>
</head>

<body>

    <div class="game-card">
        <div class="card-header">
            <h1 class="title">TỨ TRỤ BÁT TỰ</h1>
            <div class="type-icon water-bg-icon">水</div>
        </div>

        <div class="image-placeholder">
            <span class="symbol">命</span>
        </div>

        <div class="pillars-section">
            <div class="pillar">
                <span class="pillar-label">NĂM</span>
                <span class="pillar-value">
                    <span class="can-quy">Quý</span> <span class="chi-mui">Mùi</span>
                </span>
            </div>
            <div class="pillar">
                <span class="pillar-label">THÁNG</span>
                <span class="pillar-value">
                    <span class="can-quy">Quý</span> <span class="chi-hoi">Hợi</span>
                </span>
            </div>
            <div class="pillar">
                <span class="pillar-label">NGÀY</span>
                <span class="pillar-value">
                    <span class="can-nham">Nhâm</span> <span class="chi-thin">Thìn</span>
                </span>
            </div>
            <div class="pillar">
                <span class="pillar-label">GIỜ</span>
                <span class="pillar-value">
                    <span class="can-canh">Canh</span> <span class="chi-ty">Tý</span>
                </span>
            </div>
        </div>

        <div class="stats-section">
            <h2 class="section-title" style="margin-top: 0.5rem;">LỰC LƯỢNG NGŨ HÀNH</h2>
            <div class="stat-item">
                <span>THỦY (Water)</span>
                <div class="stat-bar-container">
                    <div class="stat-bar water-bar" style="width: 96%;"></div>
                </div>
                <span>480</span>
            </div>
            <div class="stat-item">
                <span>THỔ (Earth)</span>
                <div class="stat-bar-container">
                    <div class="stat-bar earth-bar" style="width: 24%;"></div>
                </div>
                <span>120</span>
            </div>
            <div class="stat-item">
                <span>KIM (Metal)</span>
                <div class="stat-bar-container">
                    <div class="stat-bar metal-bar" style="width: 20%;"></div>
                </div>
                <span>100</span>
            </div>
            <div class="stat-item">
                <span>MỘC (Wood)</span>
                <div class="stat-bar-container">
                    <div class="stat-bar wood-bar" style="width: 14%;"></div>
                </div>
                <span>70</span>
            </div>
            <div class="stat-item">
                <span>HỎA (Fire)</span>
                <div class="stat-bar-container">
                    <div class="stat-bar fire-bar" style="width: 6%;"></div>
                </div>
                <span>30</span>
            </div>
        </div>

        <div class="gods-columns-container">
            <div class="gods-section">
                <h2 class="section-title">DỤNG THẦN</h2>
                <ul class="gods-list">
                    <li class="god-item dung-than">Chính Ấn</li>
                    <li class="god-item dung-than">Thực Thần</li>
                </ul>
            </div>

            <div class="gods-section">
                <h2 class="section-title">KỴ THẦN</h2>
                <ul class="gods-list">
                    <li class="god-item ky-than">Kiếp Tài</li>
                    <li class="god-item ky-than">Thương Quan</li>
                </ul>
            </div>
        </div>

        <div class="card-footer">
            <p>Bát Tự Destiny Card - Series I</p>
        </div>
    </div>

</body>

</html>