const CONSTANTS = require("../constants");
const { SolarDate } = require("@nghiavuive/lunar_date_vi");
const { getSolarTermFromDate } = require("./tietkhi");
const { getYearCanChi } = require("./amlich");

const Common = {};

Common.getHourName = (dayName, hour) => {
  const hourIndex = Math.floor((hour + 1) / 2) % 12;
  let hourCan;
  switch (dayName.split(" ")[0]) {
    case "Giáp":
    case "Kỷ":
      hourCan = CONSTANTS.CAN[hourIndex % 10];
      break;
    case "Ất":
    case "Canh":
      hourCan = CONSTANTS.CAN[(hourIndex + 2) % 10];
      break;
    case "Bính":
    case "Tân":
      hourCan = CONSTANTS.CAN[(hourIndex + 4) % 10];
      break;
    case "Đinh":
    case "Nhâm":
      hourCan = CONSTANTS.CAN[(hourIndex + 6) % 10];
      break;
    case "Mậu":
    case "Quý":
      hourCan = CONSTANTS.CAN[(hourIndex + 8) % 10];
      break;
    default:
      hourCan = 1;
      break;
  }

  return `${hourCan} ${CONSTANTS.CHI[hourIndex]}`;
};

Common.getMonthName = (year, month) => {
  return `${CONSTANTS.CAN[(year * 12 + month + 3) % 10]} ${CONSTANTS.CHI[(month + 1) % 12]}`;
};

Common.getLunarDateInfo = (date, hour, timezone = "Asia/Ho_Chi_Minh") => {
  const lunarDate = new SolarDate(date).toLunarDate();
  // console.log(lunarDate);
  const season = getSolarTermFromDate(date, timezone);
  // console.log(season);
  let seasonMonth = CONSTANTS.SOLAR_TERMS[CONSTANTS.SEASONS.indexOf(season.name)];
  if (seasonMonth === 1) {
    seasonMonth = 12;
  } else {
    seasonMonth = seasonMonth - 1;
  }
  let correctYear = lunarDate.year;
  if (lunarDate.month === 12 && seasonMonth !== 12) {
    correctYear = lunarDate.year + 1;
  }
  const monthName = Common.getMonthName(correctYear, seasonMonth);
  return {
    month: monthName,
    day: lunarDate.getDayName(),
    year: getYearCanChi(correctYear),
    hour: Common.getHourName(lunarDate.getDayName(), hour),
  };
};

// console.log(Common.getLunarDateInfo(new Date("2005-02-03"), 5));

Common.getCanByElement = (element) => {
  return [CONSTANTS.CAN[CONSTANTS.ELEMENTS.indexOf(element) * 2], CONSTANTS.CAN[CONSTANTS.ELEMENTS.indexOf(element) * 2 + 1]];
};

Common.getElementByCan = (can) => {
  // every 2 can is one element. The order is correct from CONSTANTS.CAN
  return CONSTANTS.ELEMENTS[Math.floor(CONSTANTS.CAN.indexOf(can) / 2)];
};

Common.getElementByChi = (chi) => {
  for (const key of CONSTANTS.ELEMENTS_CHI) {
    if (key.chi.includes(chi)) {
      return key.element;
    }
  }
  return null;
};

Common.getChiByElement = (element) => {
  for (const key of CONSTANTS.ELEMENTS_CHI) {
    if (key.element === element) {
      return key.chi;
    }
  }
  return null;
};

Common.getElementRelationships = (element) => {
  const elements = ["Kim", "Thủy", "Mộc", "Hỏa", "Thổ"];
  const relationships = [
    ["Thổ", "Hỏa", "Mộc", "Kim", "Thủy"], // Kim
    ["Kim", "Thổ", "Hỏa", "Thủy", "Mộc"], // Thủy
    ["Thủy", "Kim", "Thổ", "Mộc", "Hỏa"], // Mộc
    ["Mộc", "Thủy", "Kim", "Hỏa", "Thổ"], // Hỏa
    ["Hỏa", "Mộc", "Thủy", "Thổ", "Kim"], // Thổ
  ];

  const labels = ["Ấn Tinh", "Quan Tinh", "Tài Tinh", "Tỷ Kiếp", "Thực Thương"];
  const index = elements.indexOf(element);

  if (index === -1) {
    return null;
  }

  const result = {};
  labels.forEach((label, i) => {
    result[label] = relationships[index][i];
  });

  return result;
};

Common.getCanAmDuong = (can) => {
  return CONSTANTS.CAN.indexOf(can) % 2 === 0 ? "Dương" : "Âm";
};

Common.getThienQuan = (nhatTru) => {
  const nguHanh = Common.getElementByCan(nhatTru);
  const relationship = Common.getElementRelationships(nguHanh);
  const cans = Common.getCanByElement(relationship["Quan Tinh"]);
  let thienQuan;
  for (const can of cans) {
    if (Common.getCanAmDuong(nhatTru) === Common.getCanAmDuong(can)) {
      thienQuan = can;
    }
  }
  return thienQuan;
};

Common.findCharacteristicsByElements = (data, searchElements) => {
  const results = [];
  if (!searchElements || searchElements.length === 0) {
    return results; // No search terms provided
  }

  data.forEach((row) => {
    const nguyenMauStr = row["Nguyên Mẫu Ngũ Hành"] || "";
    // Get the elements in the current row as an array
    const elementsInRowArray = nguyenMauStr
      .split("+")
      .map((elem) => elem.trim())
      .filter((elem) => elem !== "");

    let matchFound = false;

    if (searchElements.length === 1) {
      // STRICT MATCH for single search element:
      // Row must contain ONLY that one element.
      const singleSearchElement = searchElements[0];
      if (elementsInRowArray.length === 1 && elementsInRowArray[0] === singleSearchElement) {
        matchFound = true;
      }
    } else {
      // CONTAINS ALL for multiple search elements:
      // Row must contain all specified search elements (can have others too).
      const elementsInRowSet = new Set(elementsInRowArray); // Use Set for efficient 'has' check
      matchFound = searchElements.every((queryElem) => elementsInRowSet.has(queryElem));
    }

    if (matchFound) {
      // If a match is found based on the logic above, add it to results
      results.push({
        "Nguyên Mẫu Ngũ Hành": nguyenMauStr,
        "Tính cách trưởng thành": row["Tính cách trưởng thành"],
        "Mặt Tối (chưa hoàn thiện/cần cải thiện)": row["Mặt Tối (chưa hoàn thiện/cần cải thiện)"],
      });
    }
  });
  return results;
};

Common.getThapThan = (nhatTru) => {
  const result = {};
  const nhatTruAmDuong = Common.getCanAmDuong(nhatTru);
  const elemRelationships = Common.getElementRelationships(Common.getElementByCan(nhatTru));
  // console.log(elemRelationships);
  for (const key in elemRelationships) {
    switch (key) {
      case "Ấn Tinh": {
        const anKieu = Common.getCanByElement(elemRelationships[key]);
        for (const can of anKieu) {
          if (nhatTruAmDuong === Common.getCanAmDuong(can)) {
            result[can] = "Thiên Ấn";
          } else {
            result[can] = "Chính Ấn";
          }
        }
        break;
      }
      case "Quan Tinh": {
        const quanTinh = Common.getCanByElement(elemRelationships[key]);
        for (const can of quanTinh) {
          if (nhatTruAmDuong === Common.getCanAmDuong(can)) {
            result[can] = "Thiên Quan";
          } else {
            result[can] = "Chính Quan";
          }
        }
        break;
      }
      case "Tài Tinh": {
        const taiTinh = Common.getCanByElement(elemRelationships[key]);
        for (const can of taiTinh) {
          if (nhatTruAmDuong === Common.getCanAmDuong(can)) {
            result[can] = "Thiên Tài";
          } else {
            result[can] = "Chính Tài";
          }
        }
        break;
      }
      case "Thực Thương": {
        const thucThuong = Common.getCanByElement(elemRelationships[key]);
        for (const can of thucThuong) {
          if (nhatTruAmDuong === Common.getCanAmDuong(can)) {
            result[can] = "Thực Thần";
          } else {
            result[can] = "Thương Quan";
          }
        }
        break;
      }
      case "Tỷ Kiếp": {
        const tyKiep = Common.getCanByElement(elemRelationships[key]);
        for (const can of tyKiep) {
          if (nhatTruAmDuong === Common.getCanAmDuong(can)) {
            result[can] = "Tỷ Kiên";
          } else {
            result[can] = "Kiếp Tài";
          }
        }
        break;
      }
    }
  }
  return result;
};

Common.getHiddenCan = (diaChi) => {
  const mapping = {
    Tý: ["Quý"],
    Sửu: ["Kỷ", "Quý", "Tân"],
    Dần: ["Giáp", "Bính", "Mậu"],
    Mão: ["Ất"],
    Thìn: ["Mậu", "Ất", "Quý"],
    Tỵ: ["Bính", "Mậu", "Canh"],
    Ngọ: ["Đinh", "Kỷ"],
    Mùi: ["Kỷ", "Đinh", "Ất"],
    Thân: ["Canh", "Nhâm", "Mậu"],
    Dậu: ["Tân"],
    Tuất: ["Mậu", "Tân", "Đinh"],
    Hợi: ["Nhâm", "Giáp"],
  };

  return mapping[diaChi] || [];
};

Common.getHiddenCans = (personInfo) => {
  const hiddenCans = [];
  hiddenCans.personInfo = [];
  for (const key in personInfo) {
    const hiddenCan = Common.getHiddenCan(personInfo[key].split(" ")[1]);
    hiddenCans.push(hiddenCan);
    hiddenCans.personInfo.push(personInfo[key]);
  }
  return hiddenCans;
};

Common.calculateFiveElements = (hiddenCans) => {
  const result = {};
  const pointLookup = {
    1: [100],
    2: [70, 30],
    3: [60, 30, 10],
  };
  for (let i = 0; i < CONSTANTS.CAN.length; i++) {
    // find how many instances of CONSTANTS.CAN[i] in hiddenCans.personInfo
    const count = hiddenCans.personInfo.filter((can) => can.includes(CONSTANTS.CAN[i])).length;
    result[CONSTANTS.CAN[i]] = count * 100;
    for (const key in hiddenCans) {
      //check if hiddensCans[key] has CONSTANTS.CAN[i], then get the length and index to calculate: 1. If Length is 1, point is 100, if length is 2, point will be index0: 70, index1: 30, if length is 3, point will be index0: 60, index1: 30, index2: 10
      if (hiddenCans[key].includes(CONSTANTS.CAN[i]) && hiddenCans[key].length < 4) {
        const index = hiddenCans[key].indexOf(CONSTANTS.CAN[i]);
        const length = hiddenCans[key].length;
        result[CONSTANTS.CAN[i]] += pointLookup[length][index];
      }
    }
  }
  return result;
};

module.exports = Common;
