"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { StyledWarningIcon } from "@/components/styled-icons";

export default function ErrorPage() {
  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-8 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h1 className="app-title dark:text-zinc-50">Bát Tự Master V</h1>
          <h2 className="text-lg text-gray-700 dark:text-zinc-300 mb-4">Phán về số mệnh, cuộc đời, tình duyên, công việc</h2>

          <div className="flex items-center justify-center size-12 rounded-full bg-red-100 dark:bg-red-900 mb-4">
            <StyledWarningIcon className="size-6 text-red-600 dark:text-red-300" />
          </div>

          <h3 className="text-xl font-semibold dark:text-zinc-50">Authentication Error</h3>

          <p className="text-sm text-gray-500 dark:text-zinc-400 mt-1">We encountered an error processing your authentication request.</p>
        </div>

        <div className="flex flex-col gap-4 px-4 sm:px-16">
          <div className="bg-muted rounded-lg p-4">
            <h4 className="font-medium text-sm mb-2">Possible reasons:</h4>
            <ul className="text-sm text-gray-600 dark:text-zinc-400 space-y-2 list-disc pl-4">
              <li>The verification link has expired</li>
              <li>The link was already used</li>
              <li>There was a problem with the authentication service</li>
            </ul>
          </div>

          <div className="mt-4">
            <Button asChild className="w-full">
              <Link href="/login">Return to Sign In</Link>
            </Button>
          </div>

          <div className="mt-2">
            <Button asChild variant="outline" className="w-full">
              <Link href="/register">Create a New Account</Link>
            </Button>
          </div>

          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500 dark:text-zinc-500">If you continue to experience issues, please contact support.</p>
          </div>
        </div>
      </div>
    </div>
  );
}
