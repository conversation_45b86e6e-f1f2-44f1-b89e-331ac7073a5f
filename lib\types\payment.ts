/**
 * Payment system type definitions
 * Provides a provider-agnostic interface for payment processing
 */

// Core payment provider interface
export interface PaymentProvider {
  name: string;
  createPayment(request: CreatePaymentRequest): Promise<CreatePaymentResponse>;
  getPayment(paymentId: string): Promise<PaymentDetails>;
  cancelPayment(paymentId: string, reason?: string): Promise<CancelPaymentResponse>;
  processWebhook(payload: any, signature: string): Promise<WebhookResult>;
  validateWebhookSignature(payload: any, signature: string): boolean;
}

// Payment request types
export interface CreatePaymentRequest {
  orderCode: string;
  amount: number;
  currency: string;
  description: string;
  customer?: CustomerInfo;
  items?: PaymentItem[];
  returnUrl: string;
  cancelUrl: string;
  expiresAt?: Date;
  metadata?: Record<string, any>;
}

export interface CustomerInfo {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface PaymentItem {
  name: string;
  quantity: number;
  price: number;
}

// Payment response types
export interface CreatePaymentResponse {
  paymentId: string;
  checkoutUrl: string;
  qrCode?: string;
  status: PaymentStatus;
  expiresAt?: Date;
  providerData?: Record<string, any>;
}

export interface PaymentDetails {
  paymentId: string;
  orderCode: string;
  amount: number;
  amountPaid: number;
  amountRemaining: number;
  currency: string;
  status: PaymentStatus;
  createdAt: Date;
  paidAt?: Date;
  cancelledAt?: Date;
  expiresAt?: Date;
  transactions: PaymentTransaction[];
  cancellationReason?: string;
  providerData?: Record<string, any>;
}

export interface PaymentTransaction {
  reference: string;
  amount: number;
  accountNumber: string;
  description: string;
  transactionDateTime: Date;
  virtualAccountName?: string;
  virtualAccountNumber?: string;
  counterAccountBankId?: string;
  counterAccountBankName?: string;
  counterAccountName?: string;
  counterAccountNumber?: string;
}

export interface CancelPaymentResponse {
  paymentId: string;
  status: PaymentStatus;
  cancelledAt: Date;
  cancellationReason?: string;
}

// Webhook types
export interface WebhookResult {
  paymentId: string;
  orderCode: string;
  status: PaymentStatus;
  amount: number;
  amountPaid?: number;
  paidAt?: Date;
  reference?: string;
  description?: string;
  providerData?: Record<string, any>;
}

// Payment status enum
export enum PaymentStatus {
  PENDING = "PENDING",
  PAID = "PAID",
  CANCELLED = "CANCELLED",
  EXPIRED = "EXPIRED",
  FAILED = "FAILED",
}

// Database types for payment transactions
export interface PaymentTransactionRecord {
  id: string;
  userId: string;
  packageId?: string;
  orderCode: string;
  amount: number;
  currency: string;
  status: PaymentStatus;
  paymentProvider: string;
  providerPaymentId?: string;
  providerData?: Record<string, any>;
  checkoutUrl?: string;
  returnUrl?: string;
  cancelUrl?: string;
  expiresAt?: Date;
  paidAt?: Date;
  cancelledAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Webhook event record
export interface WebhookEventRecord {
  id: string;
  paymentTransactionId?: string;
  provider: string;
  eventType: string;
  payload: Record<string, any>;
  signature?: string;
  processed: boolean;
  processedAt?: Date;
  createdAt: Date;
}

// Provider configuration types
export interface PaymentProviderConfig {
  name: string;
  enabled: boolean;
  config: Record<string, any>;
}

export interface PayOSConfig {
  clientId: string;
  apiKey: string;
  checksumKey: string;
  partnerCode?: string;
}

// Error types
export class PaymentError extends Error {
  constructor(message: string, public code: string, public provider?: string, public originalError?: any) {
    super(message);
    this.name = "PaymentError";
  }
}

export class PaymentProviderError extends PaymentError {
  constructor(message: string, provider: string, originalError?: any) {
    super(message, "PROVIDER_ERROR", provider, originalError);
    this.name = "PaymentProviderError";
  }
}

export class PaymentValidationError extends PaymentError {
  constructor(message: string, field?: string) {
    super(message, "VALIDATION_ERROR");
    this.name = "PaymentValidationError";
  }
}

// Utility types
export type PaymentProviderName = "payos" | "stripe";

export interface PaymentServiceConfig {
  defaultProvider: PaymentProviderName;
  providers: Record<PaymentProviderName, PaymentProviderConfig>;
}

// Constants
export const PAYMENT_CONSTANTS = {
  DEFAULT_CURRENCY: "VND",
  DEFAULT_EXPIRY_HOURS: 24,
  MAX_DESCRIPTION_LENGTH: 255,
  MIN_AMOUNT: 1000, // 1,000 VND minimum
  MAX_AMOUNT: 500000000, // 500M VND maximum
} as const;

export const PAYMENT_PROVIDERS = {
  PAYOS: "payos",
  STRIPE: "stripe",
} as const;
