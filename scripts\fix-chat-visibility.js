/**
 * <PERSON><PERSON><PERSON> to check and fix chat visibility settings
 * 
 * This script:
 * 1. Checks for chats with null or invalid visibility settings
 * 2. Updates them to have the default 'private' visibility
 * 
 * Usage:
 * node scripts/fix-chat-visibility.js
 */

const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

// Initialize Postgres client
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL,
});

async function main() {
  const client = await pool.connect();
  
  try {
    // Start a transaction
    await client.query('BEGIN');
    
    console.log('Checking for chats with invalid visibility settings...');
    
    // Find chats with null visibility
    const nullVisibilityResult = await client.query(
      'SELECT COUNT(*) FROM "Chat" WHERE visibility IS NULL'
    );
    
    const nullVisibilityCount = Number.parseInt(nullVisibilityResult.rows[0].count);
    
    if (nullVisibilityCount > 0) {
      console.log(`Found ${nullVisibilityCount} chats with null visibility`);
      
      // Update chats with null visibility to 'private'
      await client.query(
        'UPDATE "Chat" SET visibility = $1 WHERE visibility IS NULL',
        ['private']
      );
      
      console.log(`Updated ${nullVisibilityCount} chats to have 'private' visibility`);
    } else {
      console.log('No chats with null visibility found');
    }
    
    // Find chats with invalid visibility values (not 'public' or 'private')
    const invalidVisibilityResult = await client.query(
      'SELECT COUNT(*) FROM "Chat" WHERE visibility IS NOT NULL AND visibility NOT IN ($1, $2)',
      ['public', 'private']
    );
    
    const invalidVisibilityCount = Number.parseInt(invalidVisibilityResult.rows[0].count);
    
    if (invalidVisibilityCount > 0) {
      console.log(`Found ${invalidVisibilityCount} chats with invalid visibility values`);
      
      // Update chats with invalid visibility to 'private'
      await client.query(
        'UPDATE "Chat" SET visibility = $1 WHERE visibility IS NOT NULL AND visibility NOT IN ($2, $3)',
        ['private', 'public', 'private']
      );
      
      console.log(`Updated ${invalidVisibilityCount} chats to have 'private' visibility`);
    } else {
      console.log('No chats with invalid visibility values found');
    }
    
    // Commit the transaction
    await client.query('COMMIT');
    
    console.log('Chat visibility check and fix completed successfully!');
  } catch (error) {
    // Rollback in case of error
    await client.query('ROLLBACK');
    console.error('Error during visibility check and fix:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

main();
