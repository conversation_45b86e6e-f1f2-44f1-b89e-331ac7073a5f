"use client";

import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";

// Define the BeforeInstallPromptEvent interface
interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: "accepted" | "dismissed" }>;
}

// Session storage key for tracking if the notification has been shown
const PWA_NOTIFICATION_SHOWN_KEY = "pwa-notification-shown";

interface PWAInstallerProps {
  isAuthenticated?: boolean;
}

export function PWAInstaller({ isAuthenticated = false }: PWAInstallerProps) {
  const [isInstallable, setIsInstallable] = useState(false);
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isIOS, setIsIOS] = useState(false);
  const [isStandalone, setIsStandalone] = useState(false);
  // Initialize to true (hidden) and then check in useEffect to avoid flashing
  const [notificationShown, setNotificationShown] = useState(true);
  const [shouldMarkAsShown, setShouldMarkAsShown] = useState(false);

  // First useEffect for initialization and event listeners
  useEffect(() => {
    // Register service worker
    if ("serviceWorker" in navigator) {
      // Register the service worker immediately instead of waiting for the load event
      // This ensures the service worker is registered as soon as possible
      navigator.serviceWorker
        .register("/sw.js", {
          // Ensure the service worker only controls pages under the same scope
          scope: "/",
          updateViaCache: "none", // Always check for updates, never cache sw.js
        })
        .then((registration) => {
          console.log("Service Worker registered with scope:", registration.scope);

          // Handle service worker updates more aggressively
          registration.addEventListener("updatefound", () => {
            const newWorker = registration.installing;
            if (newWorker) {
              newWorker.addEventListener("statechange", () => {
                if (newWorker.state === "installed" && navigator.serviceWorker.controller) {
                  console.log("New service worker installed, will activate on page reload");
                  // Auto-activate the new service worker
                  newWorker.postMessage({ type: "SKIP_WAITING" });
                }
              });
            }
          });

          // Check for updates immediately and periodically
          const checkForUpdates = () => {
            registration.update().catch((error) => {
              console.error("Service worker update check failed:", error);
            });
          };

          // Check for updates immediately
          checkForUpdates();

          // Check for updates when the page gains focus
          window.addEventListener("focus", checkForUpdates);

          // Check for updates every 30 seconds
          const updateInterval = setInterval(checkForUpdates, 30000);

          // Cleanup on unmount
          return () => {
            window.removeEventListener("focus", checkForUpdates);
            clearInterval(updateInterval);
          };
        })
        .catch((error) => {
          console.error("Service Worker registration failed:", error);
          // Don't try to re-register on error - this could cause an infinite loop of errors
        });

      // Handle service worker communication and updates
      navigator.serviceWorker.addEventListener("message", (event) => {
        if (event.data && event.data.type === "ERROR") {
          console.error("Service Worker error:", event.data.message);
        }
      });

      // Listen for service worker state changes
      navigator.serviceWorker.addEventListener("controllerchange", () => {
        // The new service worker has taken control
        console.log("New service worker activated");
        // Optionally reload the page to get the latest content
        // window.location.reload();
      });
    }

    // Check if the app is already installed (running in standalone or fullscreen mode)
    setIsStandalone(window.matchMedia("(display-mode: standalone)").matches || window.matchMedia("(display-mode: fullscreen)").matches);

    // Check if the device is iOS
    setIsIOS(/iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream);

    // Check if notification has already been shown in this session
    const hasBeenShown = sessionStorage.getItem(PWA_NOTIFICATION_SHOWN_KEY) === "true";
    setNotificationShown(hasBeenShown);

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      // Prevent the default browser install prompt
      e.preventDefault();
      // Store the event for later use
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      // Show our install button only if authenticated and not shown yet
      if (isAuthenticated && !hasBeenShown) {
        setIsInstallable(true);
        // Set flag to mark as shown when rendered
        setShouldMarkAsShown(true);
      }
    };

    window.addEventListener("beforeinstallprompt", handleBeforeInstallPrompt);

    // Listen for display mode changes (e.g., when the app is installed and launched)
    const displayModeMediaQuery = window.matchMedia("(display-mode: standalone), (display-mode: fullscreen)");
    const handleDisplayModeChange = (e: MediaQueryListEvent) => {
      setIsStandalone(e.matches);
    };
    displayModeMediaQuery.addEventListener("change", handleDisplayModeChange);

    // Clean up event listeners
    return () => {
      window.removeEventListener("beforeinstallprompt", handleBeforeInstallPrompt);
      displayModeMediaQuery.removeEventListener("change", handleDisplayModeChange);
    };
  }, [isAuthenticated]);

  // Second useEffect to mark notification as shown when it should be visible
  useEffect(() => {
    // Only mark as shown if the component would be visible (authenticated, not shown yet, not in standalone)
    if (shouldMarkAsShown && isAuthenticated && !notificationShown && !isStandalone) {
      sessionStorage.setItem(PWA_NOTIFICATION_SHOWN_KEY, "true");
    }
  }, [shouldMarkAsShown, isAuthenticated, notificationShown, isStandalone]);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    // Show the browser install prompt
    await deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    const choiceResult = await deferredPrompt.userChoice;

    // Reset the deferred prompt variable
    setDeferredPrompt(null);
    setIsInstallable(false);

    // Log the user's choice
    console.log("User choice:", choiceResult.outcome);

    // Mark notification as shown regardless of outcome
    sessionStorage.setItem(PWA_NOTIFICATION_SHOWN_KEY, "true");
    setNotificationShown(true);
  };

  const handleDismiss = () => {
    // Mark notification as shown when dismissed
    sessionStorage.setItem(PWA_NOTIFICATION_SHOWN_KEY, "true");
    setNotificationShown(true);
    setIsInstallable(false);
  };

  // Don't show anything if:
  // 1. The app is already installed
  // 2. User is not authenticated
  // 3. Notification has already been shown in this session
  if (isStandalone || !isAuthenticated || notificationShown) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {isInstallable && (
        <div className="bg-card p-4 rounded-lg shadow-lg">
          <Button onClick={handleInstallClick} className="bg-primary text-primary-foreground mb-2 w-full">
            Cài đặt ứng dụng toàn màn hình
          </Button>
          <Button variant="outline" size="sm" onClick={handleDismiss} className="w-full">
            Đóng
          </Button>
        </div>
      )}

      {isIOS && !isInstallable && (
        <div className="bg-card p-4 rounded-lg shadow-lg max-w-xs">
          <p className="text-sm mb-2 font-medium">Cài đặt ứng dụng toàn màn hình</p>
          <p className="text-sm mb-2">
            Để cài đặt ứng dụng này trên iOS, nhấn vào biểu tượng chia sẻ{" "}
            <span role="img" aria-label="share icon">
              ⎋
            </span>{" "}
            và chọn `Thêm vào màn hình chính`{" "}
            <span role="img" aria-label="plus icon">
              ➕
            </span>
          </p>
          <p className="text-xs mb-2 text-muted-foreground">Sau khi cài đặt, ứng dụng sẽ chạy ở chế độ toàn màn hình không có thanh trình duyệt</p>
          <Button variant="outline" size="sm" onClick={handleDismiss} className="w-full">
            Đã hiểu
          </Button>
        </div>
      )}
    </div>
  );
}
