/**
 * Payment Creation API Endpoint
 * Creates a new payment for a credit package
 */

import { type NextRequest, NextResponse } from 'next/server';
import { auth } from '@/app/(auth)/auth-server';
import { getPaymentService } from '@/lib/payment/payment-service';
import { validatePaymentConfig } from '@/lib/payment/payment-config';
import { PaymentError, type PaymentProviderName } from '@/lib/types/payment';
import { CREDIT_PACKAGES } from '@/lib/types/credit';

interface CreatePaymentRequest {
  packageId: string;
  provider?: PaymentProviderName;
}

export async function POST(request: NextRequest) {
  try {
    // Validate payment configuration
    validatePaymentConfig();

    // Get authenticated user
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body: CreatePaymentRequest = await request.json();
    const { packageId, provider = 'payos' } = body;

    // Validate package ID
    if (!packageId || typeof packageId !== 'string') {
      return NextResponse.json(
        { error: 'Package ID is required' },
        { status: 400 }
      );
    }

    // Check if package exists and is active
    const creditPackage = CREDIT_PACKAGES.find(pkg => pkg.id === packageId);
    if (!creditPackage || !creditPackage.isActive) {
      return NextResponse.json(
        { error: 'Invalid or inactive package' },
        { status: 400 }
      );
    }

    // Validate provider
    if (provider && !['payos', 'stripe'].includes(provider)) {
      return NextResponse.json(
        { error: 'Invalid payment provider' },
        { status: 400 }
      );
    }

    // Create payment
    const paymentService = getPaymentService();
    const paymentResponse = await paymentService.createPaymentForPackage(
      session.user.id,
      packageId,
      provider
    );

    return NextResponse.json({
      success: true,
      data: {
        paymentId: paymentResponse.paymentId,
        checkoutUrl: paymentResponse.checkoutUrl,
        qrCode: paymentResponse.qrCode,
        status: paymentResponse.status,
        expiresAt: paymentResponse.expiresAt,
        package: {
          id: creditPackage.id,
          name: creditPackage.name,
          credits: creditPackage.credits,
          price: creditPackage.priceVnd,
          currency: creditPackage.currency,
        },
      },
    });
  } catch (error) {
    console.error('Payment creation failed:', error);

    if (error instanceof PaymentError) {
      const statusCode = error.code === 'VALIDATION_ERROR' ? 400 : 500;
      return NextResponse.json(
        { error: error.message, code: error.code },
        { status: statusCode }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create payment' },
      { status: 500 }
    );
  }
}

// GET endpoint to retrieve payment details
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const orderCode = searchParams.get('orderCode');

    if (!orderCode) {
      return NextResponse.json(
        { error: 'Order code is required' },
        { status: 400 }
      );
    }

    const paymentService = getPaymentService();
    const paymentDetails = await paymentService.getPaymentDetails(orderCode);

    if (!paymentDetails) {
      return NextResponse.json(
        { error: 'Payment not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: paymentDetails,
    });
  } catch (error) {
    console.error('Failed to get payment details:', error);
    return NextResponse.json(
      { error: 'Failed to get payment details' },
      { status: 500 }
    );
  }
}
