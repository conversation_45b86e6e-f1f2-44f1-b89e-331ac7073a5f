/**
 * Client-side Credit Service
 * Used in browser components with real-time updates and optimistic UI
 * This file only contains client-side code to avoid server import issues
 */

import { createClient } from "@/lib/supabase/client";
import type { CreditTransaction, CreditDeductionResult, CreditAdditionResult } from "@/lib/types/credit";
import { CreditOperationError, CREDIT_CONSTANTS } from "@/lib/types/credit";
import { ChatSDKError } from "@/lib/errors";

/**
 * Client-side Credit Service Interface
 */
interface IClientCreditService {
  deductCredit(userId: string, amount: number, description: string): Promise<CreditDeductionResult>;
  getUserCredits(userId: string): Promise<number>;
  addCredits(userId: string, amount: number, description: string): Promise<CreditAdditionResult>;
  getCreditHistory(userId: string, limit?: number, offset?: number): Promise<CreditTransaction[]>;
  validateSufficientCredits(userId: string, amount: number): Promise<boolean>;
  subscribeToCredits(userId: string, callback: (credits: number) => void): () => void;
  clearCache(): void;
}

/**
 * Client-side Credit Service Implementation
 */
export class ClientCreditService implements IClientCreditService {
  private supabase;
  private creditCache = new Map<string, { credits: number; timestamp: number }>();
  private readonly CACHE_TTL = 30000; // 30 seconds for client-side cache

  constructor() {
    this.supabase = createClient();
  }

  /**
   * Get cached credits (client-side memory cache)
   */
  private getCachedCredits(userId: string): number | null {
    const cached = this.creditCache.get(userId);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.credits;
    }
    this.creditCache.delete(userId);
    return null;
  }

  /**
   * Set cached credits (client-side memory cache)
   */
  private setCachedCredits(userId: string, credits: number): void {
    this.creditCache.set(userId, {
      credits,
      timestamp: Date.now(),
    });
  }

  /**
   * Subscribe to real-time credit updates
   */
  subscribeToCredits(userId: string, callback: (credits: number) => void): () => void {
    console.log("[CREDIT SERVICE] Setting up real-time subscription for user:", userId);

    const channel = this.supabase
      .channel(`credit_updates_${userId}`)
      .on(
        "postgres_changes",
        {
          event: "UPDATE",
          schema: "public",
          table: "User",
          filter: `id=eq.${userId}`,
        },
        (payload) => {
          console.log("[CREDIT SERVICE] Received database update:", payload);
          if (payload.new && "credits" in payload.new) {
            const newCredits = payload.new.credits as number;
            console.log("[CREDIT SERVICE] Credit update detected:", newCredits);
            this.setCachedCredits(userId, newCredits);
            callback(newCredits);
          } else {
            console.log("[CREDIT SERVICE] No credits field in update payload");
          }
        }
      )
      .subscribe((status) => {
        console.log("[CREDIT SERVICE] Subscription status:", status);
      });

    return () => {
      console.log("[CREDIT SERVICE] Unsubscribing from credit updates");
      channel.unsubscribe();
    };
  }

  async deductCredit(userId: string, amount: number, description: string): Promise<CreditDeductionResult> {
    try {
      // Input validation
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      if (!amount || amount <= 0 || !Number.isInteger(amount)) {
        throw new ChatSDKError("bad_request:credit", "Amount must be a positive integer");
      }

      if (!description || typeof description !== "string" || description.trim().length === 0) {
        throw new ChatSDKError("bad_request:credit", "Description is required");
      }

      // Call RPC function
      const { data, error } = await this.supabase.rpc("deduct_credit", {
        p_user_id: userId,
        p_amount: amount,
        p_description: description.trim(),
      });

      if (error) {
        console.error("RPC deduct_credit failed:", error);
        throw new CreditOperationError(`Failed to deduct credits: ${error.message}`, error);
      }

      const result = data as CreditDeductionResult;

      if (!result.success) {
        if (result.error?.includes("Insufficient credits")) {
          throw new ChatSDKError("forbidden:credit", result.error);
        }
        throw new CreditOperationError(result.error || "Credit deduction failed");
      }

      // Invalidate cache after successful deduction
      this.creditCache.delete(userId);

      return result;
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Credit deduction failed:", error);
      throw new CreditOperationError("Credit deduction failed", error);
    }
  }

  async getUserCredits(userId: string): Promise<number> {
    try {
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      // Try cache first
      const cachedCredits = this.getCachedCredits(userId);
      if (cachedCredits !== null) {
        return cachedCredits;
      }

      // Fallback to database
      const { data, error } = await this.supabase.rpc("get_user_credits", {
        p_user_id: userId,
      });

      if (error) {
        console.error("RPC get_user_credits failed:", error);
        throw new CreditOperationError(`Failed to get user credits: ${error.message}`, error);
      }

      const credits = data as number;

      // Cache the result
      this.setCachedCredits(userId, credits);

      return credits;
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Failed to get user credits:", error);
      throw new CreditOperationError("Failed to get user credits", error);
    }
  }

  async addCredits(userId: string, amount: number, description: string): Promise<CreditAdditionResult> {
    try {
      // Input validation
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      if (!amount || amount <= 0 || !Number.isInteger(amount)) {
        throw new ChatSDKError("bad_request:credit", "Amount must be a positive integer");
      }

      if (!description || typeof description !== "string" || description.trim().length === 0) {
        throw new ChatSDKError("bad_request:credit", "Description is required");
      }

      // Call RPC function
      const { data, error } = await this.supabase.rpc("add_credits", {
        p_user_id: userId,
        p_amount: amount,
        p_description: description.trim(),
      });

      if (error) {
        console.error("RPC add_credits failed:", error);
        throw new CreditOperationError(`Failed to add credits: ${error.message}`, error);
      }

      const result = data as CreditAdditionResult;

      if (!result.success) {
        throw new CreditOperationError(result.error || "Credit addition failed");
      }

      // Invalidate cache after successful addition
      this.creditCache.delete(userId);

      return result;
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Credit addition failed:", error);
      throw new CreditOperationError("Credit addition failed", error);
    }
  }

  async getCreditHistory(userId: string, limit = 50, offset = 0): Promise<CreditTransaction[]> {
    try {
      if (!userId || typeof userId !== "string") {
        throw new ChatSDKError("bad_request:credit", "Invalid user ID");
      }

      // Validate and sanitize pagination parameters
      const sanitizedLimit = Math.min(Math.max(1, limit || 50), CREDIT_CONSTANTS.MAX_TRANSACTION_HISTORY);
      const sanitizedOffset = Math.max(0, offset || 0);

      const { data, error } = await this.supabase.rpc("get_credit_history", {
        p_user_id: userId,
        p_limit: sanitizedLimit,
        p_offset: sanitizedOffset,
      });

      if (error) {
        console.error("RPC get_credit_history failed:", error);
        throw new CreditOperationError(`Failed to get credit history: ${error.message}`, error);
      }

      return (data as CreditTransaction[]) || [];
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Failed to get credit history:", error);
      throw new CreditOperationError("Failed to get credit history", error);
    }
  }

  async validateSufficientCredits(userId: string, amount: number): Promise<boolean> {
    try {
      if (!userId || typeof userId !== "string") {
        return false;
      }

      if (!amount || amount <= 0) {
        return false;
      }

      // Try cache first for quick validation
      const cachedCredits = this.getCachedCredits(userId);
      if (cachedCredits !== null) {
        return cachedCredits >= amount;
      }

      // Fallback to RPC function
      const { data, error } = await this.supabase.rpc("validate_sufficient_credits", {
        p_user_id: userId,
        p_required_amount: amount,
      });

      if (error) {
        console.error("RPC validate_sufficient_credits failed:", error);
        return false;
      }

      return data as boolean;
    } catch (error) {
      console.error("Failed to validate credits:", error);
      return false;
    }
  }

  /**
   * Clear local cache
   */
  clearCache(): void {
    this.creditCache.clear();
  }
}

// Export singleton instance for client-side use
export const clientCreditService = new ClientCreditService();
