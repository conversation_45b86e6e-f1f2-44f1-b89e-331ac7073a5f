"use client";

import { useState } from "react";
import Link from "next/link";
import { ArrowLeft, Check, Coins, CreditCard, Star, ShoppingCart } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CreditDisplay } from "@/components/credit-display";
import { useAuth } from "@/lib/auth/auth-context";

// Credit packages configuration
const creditPackages = [
  {
    id: "basic",
    name: "C<PERSON> Bản",
    credits: 50,
    price: "120.000 VND",
    pricePerCredit: "2.400 VND",
    description: "<PERSON><PERSON> hợp cho người dùng không thường xuyên",
    features: ["50 tin nhắn chat", "Bao gồm sử dụng công cụ", "Sử dụng trong 30 ngày"],
    popular: false,
    color: "bg-gray-50 dark:bg-gray-900/50 border-gray-200 dark:border-gray-800",
  },
  {
    id: "pro",
    name: "VIP",
    credits: 250,
    price: "500.000 VND",
    pricePerCredit: "2.000 VND",
    description: "Gói tốt nhất cho người dùng thường xuyên",
    features: ["250 tin nhắn chat", "Bao gồm tất cả công cụ", "Sử dụng trong 30 ngày", "Hỗ trợ ưu tiên"],
    popular: true,
    color: "bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800",
  },
  {
    id: "enterprise",
    name: "VIP Pro",
    credits: 2000,
    price: "3.000.000 VND",
    pricePerCredit: "1.500 VND",
    description: "Dành cho người dùng chuyên sâu",
    features: ["2000 tin nhắn chat", "Tất cả tính năng cao cấp", "Sử dụng trong 90 ngày", "Được 1 lần đọc lá số trực tiếp từ founder"],
    popular: false,
    color: "bg-emerald-50 dark:bg-emerald-900/20 border-emerald-200 dark:border-emerald-800",
  },
];

export default function PaymentPage() {
  const { credits } = useAuth();
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleSelectPackage = (packageId: string) => {
    setSelectedPackage(packageId);
  };

  const handlePurchase = async (packageId: string) => {
    setIsProcessing(true);

    try {
      const response = await fetch("/api/payment/create", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          packageId,
          provider: "payos",
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create payment");
      }

      // Redirect to PayOS checkout
      if (data.data.checkoutUrl) {
        window.location.href = data.data.checkoutUrl;
      } else {
        throw new Error("No checkout URL received");
      }
    } catch (error) {
      console.error("Payment creation failed:", error);
      alert(`Payment failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background">
      {/* Header - following chat header pattern */}
      <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 safe-area-top border-b">
        <Button variant="ghost" size="sm" asChild className="shrink-0">
          <Link href="/" className="gap-2">
            <ArrowLeft className="size-4" />
            <span className="hidden sm:inline">Back to Chat</span>
            <span className="sm:hidden">Back</span>
          </Link>
        </Button>

        <div className="ml-auto">
          <CreditDisplay size="md" />
        </div>
      </header>

      {/* Main Content - scrollable area */}
      <main className="flex-1 overflow-y-auto px-3 sm:px-4 md:px-6 py-4 sm:py-6">
        <div className="max-w-6xl mx-auto">
          {/* Page Header */}
          <div className="flex items-center gap-3 mb-4 sm:mb-6">
            <div className="flex items-center justify-center size-8 sm:size-10 rounded-full bg-primary/10 shrink-0">
              <ShoppingCart className="size-4 sm:size-5 text-primary" />
            </div>
            <div className="min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Get More Credits</h1>
              <p className="text-sm sm:text-base text-muted-foreground">Choose a credit package to continue using Bát Tự Master V</p>
            </div>
          </div>

          {/* Current Balance Card */}
          <Card className="mb-4 sm:mb-6">
            <CardHeader className="pb-3 sm:pb-6">
              <CardTitle className="text-base sm:text-lg">Current Balance</CardTitle>
              <CardDescription className="text-sm">Your available credits</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-start gap-3">
                <CreditDisplay size="md" />
                <div className="text-xs sm:text-sm text-muted-foreground">
                  <p>Credits are used for chat messages and tool usage.</p>
                  <p>Each message costs 1 credit.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Credit Packages */}
          <div className="grid gap-6 md:grid-cols-3 mb-4 sm:mb-6">
            {creditPackages.map((pkg) => (
              <Card
                key={pkg.id}
                className={`relative cursor-pointer transition-all duration-200 hover:shadow-lg flex flex-col h-full ${
                  selectedPackage === pkg.id ? "ring-2 ring-primary" : ""
                } ${pkg.color}`}
                onClick={() => handleSelectPackage(pkg.id)}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 -translate-x-1/2">
                    <Badge className="bg-amber-600 text-white gap-1">
                      <Star className="size-3 fill-current" />
                      Most Popular
                    </Badge>
                  </div>
                )}

                <CardHeader className="text-center pb-4">
                  <CardTitle className="text-xl">{pkg.name}</CardTitle>
                  <CardDescription>{pkg.description}</CardDescription>
                  <div className="mt-4">
                    <div className="text-3xl font-bold">{pkg.price}</div>
                    <div className="text-sm text-muted-foreground">{pkg.pricePerCredit} per credit</div>
                  </div>
                </CardHeader>

                <CardContent className="flex flex-col grow">
                  <div className="text-center mb-4">
                    <div className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-primary/10 text-primary font-medium">
                      <Coins className="size-4" />
                      {pkg.credits} Credits
                    </div>
                  </div>

                  <ul className="space-y-2 mb-6 grow">
                    {pkg.features.map((feature, index) => (
                      <li key={`${pkg.id}-feature-${index}`} className="flex items-center gap-2 text-sm">
                        <Check className="size-4 text-green-600 shrink-0" />
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <Button
                    className="w-full gap-2 mt-auto"
                    variant={selectedPackage === pkg.id ? "default" : "outline"}
                    disabled={isProcessing}
                    onClick={(e) => {
                      e.stopPropagation();
                      handlePurchase(pkg.id);
                    }}
                  >
                    <CreditCard className="size-4" />
                    {isProcessing && selectedPackage === pkg.id ? "Processing..." : "Donate"}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Payment Info */}
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-center">Secure Payment</CardTitle>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h4 className="font-medium mb-2 text-blue-900 dark:text-blue-100">Cần hỗ trợ?</h4>
                <p className="text-sm text-blue-700 dark:text-blue-200">
                  Liên hệ qua email:{" "}
                  <a href="mailto:<EMAIL>" className="underline">
                    <EMAIL>
                  </a>{" "}
                  hoặc Facebook:{" "}
                  <a href="https://www.facebook.com/longmaba" className="underline">
                    Long Nhãn
                  </a>
                </p>
              </div>
              <Button variant="outline" asChild>
                <Link href="/">Return to Chat</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
