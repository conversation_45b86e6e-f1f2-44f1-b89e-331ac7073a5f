# API Keys (Required to enable respective provider)
ANTHROPIC_API_KEY="your_anthropic_api_key_here"       # Required: Format: sk-ant-api03-...
PERPLEXITY_API_KEY="your_perplexity_api_key_here"     # Optional: Format: pplx-...
OPENAI_API_KEY="your_openai_api_key_here"             # Optional, for OpenAI/OpenRouter models. Format: sk-proj-...
GOOGLE_API_KEY="your_google_api_key_here"             # Optional, for Google Gemini models.
MISTRAL_API_KEY="your_mistral_key_here"               # Optional, for Mistral AI models.
XAI_API_KEY="YOUR_XAI_KEY_HERE"                       # Optional, for xAI AI models.
AZURE_OPENAI_API_KEY="your_azure_key_here"            # Optional, for Azure OpenAI models (requires endpoint in .taskmasterconfig).
OLLAMA_API_KEY="your_ollama_api_key_here"             # Optional: For remote Ollama servers that require authentication.

# PayOS Configuration (Required for payment processing)
PAYOS_CLIENT_ID="your-payos-client-id"                # Required: PayOS Client ID from https://my.payos.vn
PAYOS_API_KEY="your-payos-api-key"                    # Required: PayOS API Key from https://my.payos.vn
PAYOS_CHECKSUM_KEY="your-payos-checksum-key"          # Required: PayOS Checksum Key from https://my.payos.vn
PAYOS_PARTNER_CODE="your-partner-code"                # Optional: Partner code for affiliate program

# Payment Configuration
PAYMENT_TEST_MODE="true"                              # Optional: Enable test mode for payments (default: true in development)

# Application URLs
NEXT_PUBLIC_SITE_URL="http://localhost:3000"           # Required: Your application URL for payment callbacks