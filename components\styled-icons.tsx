/**
 * Wrapper components for icons that accept className prop
 */
import { EnvelopeIcon as BaseEnvelopeIcon, LoaderIcon as BaseLoaderIcon, WarningIcon as BaseWarningIcon } from "./icons";
import { cn } from "@/lib/utils";

export function StyledEnvelopeIcon({ className }: { className?: string }) {
  return (
    <span className={cn(className)}>
      <BaseEnvelopeIcon size={16} />
    </span>
  );
}

export function StyledLoaderIcon({ className }: { className?: string }) {
  return (
    <span className={cn(className)}>
      <BaseLoaderIcon size={16} />
    </span>
  );
}

export function StyledWarningIcon({ className }: { className?: string }) {
  return (
    <span className={cn(className)}>
      <BaseWarningIcon size={16} />
    </span>
  );
}
