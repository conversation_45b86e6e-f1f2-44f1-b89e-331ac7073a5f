"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle, RefreshCw, Trash2 } from "lucide-react";
import { useAuth } from "@/lib/auth/auth-context";

interface AuthErrorRecoveryProps {
  className?: string;
}

export function AuthErrorRecovery({ className }: AuthErrorRecoveryProps) {
  const { authError, clearAuthError, refreshAuth } = useAuth();
  const [isRecovering, setIsRecovering] = useState(false);

  if (!authError) {
    return null;
  }

  const handleRetry = async () => {
    setIsRecovering(true);
    try {
      clearAuthError();
      await refreshAuth();
    } catch (error) {
      console.error("Recovery attempt failed:", error);
    } finally {
      setIsRecovering(false);
    }
  };

  const handleClearStorage = () => {
    if (typeof window === "undefined") return;

    try {
      // Clear all Supabase-related localStorage items
      const keysToRemove = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key?.startsWith("sb-")) {
          keysToRemove.push(key);
        }
      }

      keysToRemove.forEach((key) => {
        localStorage.removeItem(key);
      });

      // Also clear legacy token
      localStorage.removeItem("supabase.auth.token");

      // Clear the error and try to refresh
      clearAuthError();

      // Reload the page to start fresh
      window.location.reload();
    } catch (error) {
      console.error("Failed to clear storage:", error);
    }
  };

  return (
    <Alert variant="destructive" className={className}>
      <AlertTriangle className="size-4" />
      <AlertTitle>Authentication Error</AlertTitle>
      <AlertDescription className="mt-2">
        <p className="mb-3">
          {authError.includes("timeout")
            ? "Authentication session timed out. This may be due to corrupted browser data."
            : `Authentication failed: ${authError}`}
        </p>
        <div className="flex gap-2 flex-wrap">
          <Button variant="outline" size="sm" onClick={handleRetry} disabled={isRecovering} className="flex items-center gap-2">
            <RefreshCw className={`size-3 ${isRecovering ? "animate-spin" : ""}`} />
            {isRecovering ? "Retrying..." : "Retry"}
          </Button>
          <Button variant="outline" size="sm" onClick={handleClearStorage} className="flex items-center gap-2">
            <Trash2 className="size-3" />
            Clear & Reload
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  );
}
