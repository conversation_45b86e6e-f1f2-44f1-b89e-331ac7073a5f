/**
 * Admin bulk credit operations API endpoint
 * Allows admins to perform credit operations on multiple users simultaneously
 */

import { type NextRequest, NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth/admin-utils";
import { createClient } from "@/lib/supabase/server";
import { auth } from "@/app/(auth)/auth-server";
import { ChatSDKError } from "@/lib/errors";

interface BulkCreditRequest {
  user_ids: string[];
  amount: number;
  reason: string;
  operation_type: "add" | "deduct" | "set";
}

interface BulkOperationResult {
  success: boolean;
  processed: number;
  failed: number;
  results: Array<{
    user_id: string;
    email: string;
    success: boolean;
    previous_credits: number;
    new_credits: number;
    error?: string;
  }>;
  operation: {
    type: string;
    amount: number;
    reason: string;
    total_users: number;
  };
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Authenticate and verify admin access
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    requireAdmin(session.user, "bulk credit operations");

    // Parse and validate request body
    const body: BulkCreditRequest = await request.json();
    const { user_ids, amount, reason, operation_type } = body;

    // Input validation
    if (!Array.isArray(user_ids) || user_ids.length === 0) {
      return NextResponse.json({ error: "User IDs array is required and cannot be empty" }, { status: 400 });
    }

    if (user_ids.length > 100) {
      return NextResponse.json({ error: "Cannot process more than 100 users at once" }, { status: 400 });
    }

    if (!amount || amount <= 0 || !Number.isInteger(amount)) {
      return NextResponse.json({ error: "Amount must be a positive integer" }, { status: 400 });
    }

    if (amount > 10000) {
      return NextResponse.json({ error: "Amount cannot exceed 10,000 credits per operation" }, { status: 400 });
    }

    if (!reason || typeof reason !== "string" || reason.trim().length < 5) {
      return NextResponse.json({ error: "Reason must be at least 5 characters long" }, { status: 400 });
    }

    if (!["add", "deduct", "set"].includes(operation_type)) {
      return NextResponse.json({ error: "Invalid operation type" }, { status: 400 });
    }

    // Validate all user IDs exist
    const supabase = await createClient();
    const { data: users, error: usersError } = await supabase.from("User").select("id, email, credits").in("id", user_ids);

    if (usersError) {
      console.error("Failed to fetch users for bulk operation:", usersError);
      return NextResponse.json({ error: "Failed to validate users" }, { status: 500 });
    }

    if (!users || users.length !== user_ids.length) {
      const foundIds = users?.map((u) => u.id) || [];
      const missingIds = user_ids.filter((id) => !foundIds.includes(id));
      return NextResponse.json(
        {
          error: `Some users not found: ${missingIds.slice(0, 5).join(", ")}${missingIds.length > 5 ? "..." : ""}`,
        },
        { status: 400 }
      );
    }

    // Process bulk operations
    const results: BulkOperationResult["results"] = [];
    let processed = 0;
    let failed = 0;

    for (const user of users) {
      try {
        // Calculate new balance
        let newBalance: number;
        let actualAmount: number;

        switch (operation_type) {
          case "add":
            newBalance = user.credits + amount;
            actualAmount = amount;
            break;
          case "deduct":
            newBalance = Math.max(0, user.credits - amount);
            actualAmount = -Math.min(amount, user.credits);
            break;
          case "set":
            newBalance = amount;
            actualAmount = amount - user.credits;
            break;
          default:
            throw new Error("Invalid operation type");
        }

        // Update user credits
        const { error: updateError } = await supabase.from("User").update({ credits: newBalance }).eq("id", user.id);

        if (updateError) {
          throw new Error(`Failed to update credits: ${updateError.message}`);
        }

        // Record transaction
        const { error: transactionError } = await supabase.from("credit_transactions").insert({
          user_id: user.id,
          amount: actualAmount,
          description: `Bulk admin adjustment: ${reason.trim()}`,
          transaction_type: "admin_adjustment",
        });

        if (transactionError) {
          console.error(`Failed to record transaction for user ${user.id}:`, transactionError);
          // Continue processing other users even if transaction logging fails
        }

        results.push({
          user_id: user.id,
          email: user.email,
          success: true,
          previous_credits: user.credits,
          new_credits: newBalance,
        });

        processed++;
      } catch (error) {
        console.error(`Failed to process user ${user.id}:`, error);

        results.push({
          user_id: user.id,
          email: user.email,
          success: false,
          previous_credits: user.credits,
          new_credits: user.credits,
          error: error instanceof Error ? error.message : "Unknown error",
        });

        failed++;
      }
    }

    // Note: Admin audit logging is handled by individual user transactions
    // We don't create a separate admin audit entry in credit_transactions
    // as it would appear in users' credit history inappropriately.
    // Admin actions are tracked through the individual user transaction records.

    const result: BulkOperationResult = {
      success: failed === 0,
      processed,
      failed,
      results,
      operation: {
        type: operation_type,
        amount,
        reason: reason.trim(),
        total_users: user_ids.length,
      },
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error("Bulk credit operation error:", error);

    if (error instanceof ChatSDKError) {
      if (error.type === "unauthorized") {
        return NextResponse.json({ error: "Unauthorized access" }, { status: 401 });
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
