"use server";

import { createClient } from "@/lib/supabase/server";

/**
 * Resend verification email to a user
 * @param email Email address to resend verification to
 * @returns Result of the resend attempt
 */
export async function resendVerificationEmail(email: string) {
  try {
    const supabase = await createClient();
    const { error } = await supabase.auth.resend({
      type: "signup",
      email,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"}/auth/callback`,
      },
    });

    if (error) {
      console.error("Error resending verification email:", error.message);
      return { ok: false, error: error.message };
    }

    return { ok: true };
  } catch (error) {
    console.error("Error resending verification email:", error);
    return { ok: false, error: "Failed to resend verification email" };
  }
}
