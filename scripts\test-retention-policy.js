/**
 * <PERSON><PERSON><PERSON> to test the 60-day retention policy for chat messages
 *
 * This script:
 * 1. Creates a test chat with messages of different ages
 * 2. Runs the cleanup script
 * 3. Verifies that only messages newer than 60 days remain
 *
 * Usage:
 * node scripts/test-retention-policy.js
 */

const { Pool } = require("pg");
require("dotenv").config({ path: ".env.local" });
const { v4: uuidv4 } = require("uuid");

// Initialize Postgres client
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL,
});

async function main() {
  const client = await pool.connect();

  try {
    // Start a transaction
    await client.query("BEGIN");

    console.log("Creating test data...");

    // Create a test user
    const testUserId = uuidv4();
    await client.query('INSERT INTO "User" (id, email, password) VALUES ($1, $2, $3)', [testUserId, `test-${Date.now()}@example.com`, "password"]);

    // Create a test chat
    const testChatId = uuidv4();
    await client.query('INSERT INTO "Chat" (id, "createdAt", title, "userId", visibility) VALUES ($1, $2, $3, $4, $5)', [
      testChatId,
      new Date(),
      "Test Chat",
      testUserId,
      "private",
    ]);

    // Create messages with different ages
    const now = new Date();

    // Message from 70 days ago (should be deleted)
    const oldMessageId = uuidv4();
    const oldDate = new Date(now);
    oldDate.setDate(oldDate.getDate() - 70);

    await client.query('INSERT INTO "Message_v2" (id, "chatId", role, parts, attachments, "createdAt") VALUES ($1, $2, $3, $4, $5, $6)', [
      oldMessageId,
      testChatId,
      "user",
      JSON.stringify([{ type: "text", text: "Old message" }]),
      JSON.stringify([]),
      oldDate,
    ]);

    // Message from 40 days ago (should be kept)
    const recentMessageId = uuidv4();
    const recentDate = new Date(now);
    recentDate.setDate(recentDate.getDate() - 40);

    await client.query('INSERT INTO "Message_v2" (id, "chatId", role, parts, attachments, "createdAt") VALUES ($1, $2, $3, $4, $5, $6)', [
      recentMessageId,
      testChatId,
      "user",
      JSON.stringify([{ type: "text", text: "Recent message" }]),
      JSON.stringify([]),
      recentDate,
    ]);

    // Message from today (should be kept)
    const newMessageId = uuidv4();

    await client.query('INSERT INTO "Message_v2" (id, "chatId", role, parts, attachments, "createdAt") VALUES ($1, $2, $3, $4, $5, $6)', [
      newMessageId,
      testChatId,
      "user",
      JSON.stringify([{ type: "text", text: "New message" }]),
      JSON.stringify([]),
      now,
    ]);

    console.log("Test data created successfully");
    console.log(`Test chat ID: ${testChatId}`);
    console.log(`Old message ID (40 days ago): ${oldMessageId}`);
    console.log(`Recent message ID (20 days ago): ${recentMessageId}`);
    console.log(`New message ID (today): ${newMessageId}`);

    // Run the cleanup function
    console.log("\nRunning cleanup function...");
    await client.query("SELECT delete_old_messages()");

    // Check which messages remain
    const remainingMessages = await client.query('SELECT id, "createdAt" FROM "Message_v2" WHERE "chatId" = $1 ORDER BY "createdAt"', [testChatId]);

    console.log("\nRemaining messages after cleanup:");
    remainingMessages.rows.forEach((msg) => {
      console.log(`- Message ${msg.id} from ${msg.createdAt}`);
    });

    // Verify results
    const oldMessageExists = remainingMessages.rows.some((msg) => msg.id === oldMessageId);
    const recentMessageExists = remainingMessages.rows.some((msg) => msg.id === recentMessageId);
    const newMessageExists = remainingMessages.rows.some((msg) => msg.id === newMessageId);

    console.log("\nTest results:");
    console.log(`Old message (70 days ago) was deleted: ${!oldMessageExists ? "PASS" : "FAIL"}`);
    console.log(`Recent message (40 days ago) was kept: ${recentMessageExists ? "PASS" : "FAIL"}`);
    console.log(`New message (today) was kept: ${newMessageExists ? "PASS" : "FAIL"}`);

    // Clean up test data
    console.log("\nCleaning up test data...");
    await client.query('DELETE FROM "Message_v2" WHERE "chatId" = $1', [testChatId]);
    await client.query('DELETE FROM "Chat" WHERE id = $1', [testChatId]);
    await client.query('DELETE FROM "User" WHERE id = $1', [testUserId]);

    // Commit the transaction
    await client.query("COMMIT");

    console.log("Test completed successfully");
  } catch (error) {
    // Rollback the transaction in case of error
    await client.query("ROLLBACK");
    console.error("Error during test:", error);
  } finally {
    // Release the client
    client.release();

    // Close the pool
    pool.end();
  }
}

// Run the main function
main().catch((error) => {
  console.error("Unhandled error:", error);
  process.exit(1);
});
