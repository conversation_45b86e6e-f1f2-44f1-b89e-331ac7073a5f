/**
 * Bulk Operations Component
 * Allows admins to perform credit operations on multiple users simultaneously
 */

"use client";

import { useState, useCallback } from "react";
import { Users, Plus, Minus, RotateCcw, AlertTriangle, X, CheckCircle, XCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import type { AdminUserData } from "@/lib/services/admin-service";

interface BulkOperationsProps {
  selectedUsers: AdminUserData[];
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

type OperationType = "add" | "deduct" | "set";

interface BulkOperationData {
  amount: number;
  reason: string;
  operationType: OperationType;
}

interface BulkOperationResult {
  success: boolean;
  processed: number;
  failed: number;
  results: Array<{
    user_id: string;
    email: string;
    success: boolean;
    previous_credits: number;
    new_credits: number;
    error?: string;
  }>;
  operation: {
    type: string;
    amount: number;
    reason: string;
    total_users: number;
  };
}

export function BulkOperations({ selectedUsers, isOpen, onClose, onSuccess }: BulkOperationsProps) {
  const [operationData, setOperationData] = useState<BulkOperationData>({
    amount: 0,
    reason: "",
    operationType: "add",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [operationResult, setOperationResult] = useState<BulkOperationResult | null>(null);
  const [showResults, setShowResults] = useState(false);

  // Validate the operation
  const validateOperation = useCallback(() => {
    const { amount, reason } = operationData;

    if (selectedUsers.length === 0) {
      return "No users selected for bulk operation";
    }

    if (selectedUsers.length > 100) {
      return "Cannot process more than 100 users at once";
    }

    if (amount <= 0) {
      return "Amount must be greater than 0";
    }

    if (amount > 10000) {
      return "Amount cannot exceed 10,000 credits per operation";
    }

    if (!reason.trim()) {
      return "Reason is required for audit trail";
    }

    if (reason.trim().length < 5) {
      return "Reason must be at least 5 characters";
    }

    return null;
  }, [operationData, selectedUsers]);

  // Calculate preview statistics
  const calculatePreview = useCallback(() => {
    const { amount, operationType } = operationData;
    let totalCreditsChange = 0;
    const usersAffected = selectedUsers.length;

    selectedUsers.forEach((user) => {
      switch (operationType) {
        case "add":
          totalCreditsChange += amount;
          break;
        case "deduct":
          totalCreditsChange -= Math.min(amount, user.credits);
          break;
        case "set":
          totalCreditsChange += amount - user.credits;
          break;
      }
    });

    return {
      usersAffected,
      totalCreditsChange,
      averageCreditsPerUser: Math.round(totalCreditsChange / usersAffected),
    };
  }, [operationData, selectedUsers]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    const validationError = validateOperation();
    if (validationError) {
      toast.error(validationError);
      return;
    }

    setShowConfirmation(true);
  }, [validateOperation]);

  // Handle confirmed submission
  const handleConfirmedSubmit = useCallback(async () => {
    setIsSubmitting(true);
    setShowConfirmation(false);

    try {
      const response = await fetch("/api/admin/credits/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_ids: selectedUsers.map((user) => user.id),
          amount: operationData.amount,
          reason: operationData.reason.trim(),
          operation_type: operationData.operationType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to perform bulk operation");
      }

      const result: BulkOperationResult = await response.json();
      setOperationResult(result);
      setShowResults(true);

      if (result.success) {
        toast.success(`Successfully processed ${result.processed} users`);
      } else {
        toast.warning(`Processed ${result.processed} users, ${result.failed} failed`);
      }

      // Don't call onSuccess() here - wait for user to close results popup
    } catch (error) {
      console.error("Bulk operation error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to perform bulk operation");
    } finally {
      setIsSubmitting(false);
    }
  }, [operationData, selectedUsers]);

  // Handle operation type change
  const handleOperationTypeChange = useCallback((type: OperationType) => {
    setOperationData((prev) => ({ ...prev, operationType: type }));
  }, []);

  // Handle amount change
  const handleAmountChange = useCallback((value: string) => {
    const amount = Number.parseInt(value, 10) || 0;
    setOperationData((prev) => ({ ...prev, amount: Math.max(0, amount) }));
  }, []);

  // Handle reason change
  const handleReasonChange = useCallback((value: string) => {
    setOperationData((prev) => ({ ...prev, reason: value }));
  }, []);

  // Handle results close and trigger refresh
  const handleResultsClose = useCallback(() => {
    setShowResults(false);
    onSuccess(); // Trigger refresh after user closes results
  }, [onSuccess]);

  // Reset form
  const handleReset = useCallback(() => {
    setOperationData({
      amount: 0,
      reason: "",
      operationType: "add",
    });
    setOperationResult(null);
    setShowResults(false);
  }, []);

  const validationError = validateOperation();
  const preview = calculatePreview();

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-50"
        onClick={onClose}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => e.key === "Escape" && onClose()}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Users className="size-5" />
                <CardTitle>Bulk Credit Operations</CardTitle>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="size-4" />
              </Button>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Selected Users Summary */}
            <Card>
              <CardContent className="pt-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Selected Users</p>
                    <p className="text-sm text-muted-foreground">
                      {selectedUsers.length} user{selectedUsers.length !== 1 ? "s" : ""} selected
                    </p>
                  </div>
                  <Badge variant="outline" className="text-lg px-3 py-1">
                    {selectedUsers.reduce((sum, user) => sum + user.credits, 0)} total credits
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Operation Type Selection */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">Operation Type</Label>
              <div className="flex gap-2">
                <Button
                  variant={operationData.operationType === "add" ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleOperationTypeChange("add")}
                  className="flex items-center gap-2"
                >
                  <Plus className="size-4" />
                  Add Credits
                </Button>
                <Button
                  variant={operationData.operationType === "deduct" ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleOperationTypeChange("deduct")}
                  className="flex items-center gap-2"
                >
                  <Minus className="size-4" />
                  Deduct Credits
                </Button>
                <Button
                  variant={operationData.operationType === "set" ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleOperationTypeChange("set")}
                  className="flex items-center gap-2"
                >
                  <RotateCcw className="size-4" />
                  Set Balance
                </Button>
              </div>
            </div>

            {/* Amount Input */}
            <div className="space-y-2">
              <Label htmlFor="amount">{operationData.operationType === "set" ? "New Balance (per user)" : "Amount (per user)"}</Label>
              <Input
                id="amount"
                type="number"
                min="1"
                max="10000"
                value={operationData.amount || ""}
                onChange={(e) => handleAmountChange(e.target.value)}
                placeholder={operationData.operationType === "set" ? "Enter new balance" : "Enter amount"}
              />
            </div>

            {/* Reason Input */}
            <div className="space-y-2">
              <Label htmlFor="reason">Reason (Required for audit trail)</Label>
              <Textarea
                id="reason"
                value={operationData.reason}
                onChange={(e) => handleReasonChange(e.target.value)}
                placeholder="Enter reason for bulk credit operation..."
                rows={3}
              />
              <p className="text-xs text-muted-foreground">Minimum 5 characters. This will be logged for audit purposes.</p>
            </div>

            {/* Preview */}
            {operationData.amount > 0 && selectedUsers.length > 0 && (
              <Card className="bg-muted/50">
                <CardContent className="pt-4">
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Operation Preview:</p>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Users affected:</span>
                        <span className="ml-2 font-medium">{preview.usersAffected}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Total credits change:</span>
                        <span className={`ml-2 font-medium ${preview.totalCreditsChange >= 0 ? "text-green-600" : "text-red-600"}`}>
                          {preview.totalCreditsChange >= 0 ? "+" : ""}
                          {preview.totalCreditsChange}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Validation Error */}
            {validationError && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{validationError}</p>
              </div>
            )}

            {/* Footer */}
            <div className="flex gap-2 pt-4 border-t">
              <Button variant="outline" onClick={handleReset} disabled={isSubmitting}>
                Reset
              </Button>
              <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={isSubmitting || !!validationError}>
                {isSubmitting ? "Processing..." : "Perform Bulk Operation"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <>
          <div
            className="fixed inset-0 bg-black/50 z-[60]"
            onClick={() => setShowConfirmation(false)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => e.key === "Escape" && setShowConfirmation(false)}
          />
          <div className="fixed inset-0 z-[60] flex items-center justify-center p-4">
            <Card className="w-full max-w-[500px]">
              <CardContent className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <AlertTriangle className="size-5 text-orange-500" />
                  <h3 className="text-lg font-semibold">Confirm Bulk Operation</h3>
                </div>

                <p className="text-sm text-muted-foreground mb-4">Please confirm this bulk credit operation. This action cannot be undone.</p>

                <div className="space-y-2 mb-4">
                  <p>
                    <strong>Users:</strong> {selectedUsers.length} selected
                  </p>
                  <p>
                    <strong>Operation:</strong> {operationData.operationType} {operationData.amount} credits per user
                  </p>
                  <p>
                    <strong>Total Credits Change:</strong> {preview.totalCreditsChange >= 0 ? "+" : ""}
                    {preview.totalCreditsChange}
                  </p>
                  <p>
                    <strong>Reason:</strong> {operationData.reason}
                  </p>
                </div>

                <div className="border-t pt-4 mb-4">
                  <p className="text-sm text-muted-foreground">This action will be logged in the audit trail and cannot be undone.</p>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => setShowConfirmation(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleConfirmedSubmit} disabled={isSubmitting}>
                    {isSubmitting ? "Processing..." : "Confirm Operation"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}

      {/* Results Modal */}
      {showResults && operationResult && (
        <>
          <div
            className="fixed inset-0 bg-black/50 z-[60]"
            onClick={handleResultsClose}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => e.key === "Escape" && handleResultsClose()}
          />
          <div className="fixed inset-0 z-[60] flex items-center justify-center p-4">
            <Card className="w-full max-w-2xl max-h-[80vh] overflow-y-auto">
              <CardHeader>
                <div className="flex items-center gap-2">
                  {operationResult.success ? <CheckCircle className="size-5 text-green-500" /> : <XCircle className="size-5 text-orange-500" />}
                  <CardTitle>Operation Results</CardTitle>
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* Summary */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <p className="text-2xl font-bold text-green-600">{operationResult.processed}</p>
                    <p className="text-sm text-green-700">Successful</p>
                  </div>
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <p className="text-2xl font-bold text-red-600">{operationResult.failed}</p>
                    <p className="text-sm text-red-700">Failed</p>
                  </div>
                </div>

                {/* Progress */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Progress</span>
                    <span>
                      {operationResult.processed + operationResult.failed} / {operationResult.operation.total_users}
                    </span>
                  </div>
                  <Progress
                    value={((operationResult.processed + operationResult.failed) / operationResult.operation.total_users) * 100}
                    className="h-2"
                  />
                </div>

                {/* Failed Operations */}
                {operationResult.failed > 0 && (
                  <div>
                    <h4 className="font-medium mb-2">Failed Operations:</h4>
                    <div className="space-y-2 max-h-40 overflow-y-auto">
                      {operationResult.results
                        .filter((result) => !result.success)
                        .map((result) => (
                          <div key={result.user_id} className="p-2 bg-red-50 rounded text-sm">
                            <p className="font-medium">{result.email}</p>
                            <p className="text-red-600">{result.error}</p>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

                <div className="flex gap-2 pt-4 border-t">
                  <Button variant="outline" onClick={handleResultsClose}>
                    Close
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </>
  );
}
