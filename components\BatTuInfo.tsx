"use client";

import { useState, useEffect } from "react";
import Image from "next/image";

interface BatTuResponse {
  response: [
    {
      "Tứ trụ": {
        month: string;
        day: string;
        year: string;
        hour: string;
      };
      "Thiên can ẩn tàng": string[][];
      "Lực lượng ngũ hành":
        | {
            // New format: Five Elements
            Mộc: number;
            Hỏa: number;
            Thổ: number;
            Kim: number;
            Thủy: number;
          }
        | {
            // Old format: Individual Can names
            Giáp: number;
            Ất: number;
            Bính: number;
            Đinh: number;
            Mậu: number;
            Kỷ: number;
            Canh: number;
            Tân: number;
            Nhâm: number;
            Quý: number;
          };
      "Thần sinh phù": {
        "Ấn Tinh": string;
        "Quan Tinh": string;
        "Tài Tinh": string;
        "Tỷ Kiếp": string;
        "Thực Thương": string;
        "Thiên Quan": string;
      };
      "Dụng Thần"?: string[];
      "Kỵ Thần"?: string[];
      "Hỷ Thần"?: string[];
    }
  ];
}

// Element to color mapping
const ELEMENT_COLORS = {
  // Wood (Mộc): Green
  Giáp: "#4CAF50",
  Ất: "#4CAF50",

  // Fire (Hỏa): Red
  Bính: "#F44336",
  Đinh: "#F44336",

  // Earth (Thổ): Yellow/Brown
  Mậu: "#FFC107",
  Kỷ: "#FFC107",

  // Metal (Kim): White/Silver
  Canh: "#9E9E9E",
  Tân: "#9E9E9E",

  // Water (Thủy): Blue/Black
  Nhâm: "#2196F3",
  Quý: "#2196F3",
};

// Element grouping for legend (kept for reference but not used with new format)
const ELEMENT_GROUPS = {
  Mộc: ["Giáp", "Ất"],
  Hỏa: ["Bính", "Đinh"],
  Thổ: ["Mậu", "Kỷ"],
  Kim: ["Canh", "Tân"],
  Thủy: ["Nhâm", "Quý"],
};

// Element to Tailwind gradient class mapping
const ELEMENT_GRADIENTS = {
  Mộc: "via-green-200 dark:via-green-700",
  Hỏa: "via-red-200 dark:via-red-700",
  Thổ: "via-amber-200 dark:via-amber-700",
  Kim: "via-slate-200 dark:via-slate-700",
  Thủy: "via-blue-200 dark:via-blue-700",
};

// Element to Tailwind bar color class mapping
const ELEMENT_BAR_COLORS = {
  Mộc: "bg-green-500 dark:bg-green-600",
  Hỏa: "bg-red-500 dark:bg-red-600",
  Thổ: "bg-amber-500 dark:bg-amber-600",
  Kim: "bg-slate-500 dark:bg-slate-600",
  Thủy: "bg-blue-500 dark:bg-blue-600",
};

// Map Chinese stems to their elements
const STEM_TO_ELEMENT = {
  Giáp: "Mộc",
  Ất: "Mộc",
  Bính: "Hỏa",
  Đinh: "Hỏa",
  Mậu: "Thổ",
  Kỷ: "Thổ",
  Canh: "Kim",
  Tân: "Kim",
  Nhâm: "Thủy",
  Quý: "Thủy",
};

// Map Chinese branches to their elements
const BRANCH_TO_ELEMENT = {
  Tý: "Thủy",
  Sửu: "Thổ",
  Dần: "Mộc",
  Mão: "Mộc",
  Thìn: "Thổ",
  Tỵ: "Hỏa",
  Ngọ: "Hỏa",
  Mùi: "Thổ",
  Thân: "Kim",
  Dậu: "Kim",
  Tuất: "Thổ",
  Hợi: "Thủy",
};

// Pillar names
const PILLAR_NAMES = {
  year: "Năm",
  month: "Tháng",
  day: "Ngày",
  hour: "Giờ",
};

// Helper function to get element gradient class
const getElementGradient = (text: string): string => {
  // First check if it's a stem
  if (text in STEM_TO_ELEMENT) {
    const element = STEM_TO_ELEMENT[text as keyof typeof STEM_TO_ELEMENT];
    return ELEMENT_GRADIENTS[element as keyof typeof ELEMENT_GRADIENTS];
  }

  // Then check if it's a branch
  if (text in BRANCH_TO_ELEMENT) {
    const element = BRANCH_TO_ELEMENT[text as keyof typeof BRANCH_TO_ELEMENT];
    return ELEMENT_GRADIENTS[element as keyof typeof ELEMENT_GRADIENTS];
  }

  return "via-gray-200 dark:via-gray-700"; // Default gradient if not found
};

// Helper function to get element bar color class
const getElementBarColor = (text: string): string => {
  // First check if it's a stem
  if (text in STEM_TO_ELEMENT) {
    const element = STEM_TO_ELEMENT[text as keyof typeof STEM_TO_ELEMENT];
    return ELEMENT_BAR_COLORS[element as keyof typeof ELEMENT_BAR_COLORS];
  }

  // Then check if it's a branch
  if (text in BRANCH_TO_ELEMENT) {
    const element = BRANCH_TO_ELEMENT[text as keyof typeof BRANCH_TO_ELEMENT];
    return ELEMENT_BAR_COLORS[element as keyof typeof ELEMENT_BAR_COLORS];
  }

  return "bg-gray-500 dark:bg-gray-600"; // Default color if not found
};

// Helper function to get element styling for gods (now elements)
const getElementGodStyling = (element: string) => {
  // Map elements to their representative stems from ELEMENT_COLORS
  const elementToStem = {
    Mộc: "Giáp", // Wood -> Green
    Hỏa: "Bính", // Fire -> Red
    Thổ: "Mậu", // Earth -> Yellow/Amber
    Kim: "Canh", // Metal -> Gray/Silver
    Thủy: "Nhâm", // Water -> Blue
  };

  const stem = elementToStem[element as keyof typeof elementToStem];
  const baseColor = stem ? ELEMENT_COLORS[stem as keyof typeof ELEMENT_COLORS] : "#888888";

  // Convert hex color to CSS custom properties for consistent styling
  const getColorVariants = (hexColor: string) => {
    // Extract RGB values from hex
    const r = Number.parseInt(hexColor.slice(1, 3), 16);
    const g = Number.parseInt(hexColor.slice(3, 5), 16);
    const b = Number.parseInt(hexColor.slice(5, 7), 16);

    return {
      textColor: `rgb(${Math.max(0, r - 40)}, ${Math.max(0, g - 40)}, ${Math.max(0, b - 40)})`, // Darker for text
      borderColor: hexColor, // Original color for border
      bgColor: `rgba(${r}, ${g}, ${b}, 0.1)`, // Light background
      bgColorDark: `rgba(${r}, ${g}, ${b}, 0.2)`, // Slightly more opaque for dark mode
    };
  };

  const colors = getColorVariants(baseColor);

  return {
    textColor: "dark:text-white",
    borderColor: "",
    bgColor: "",
    style: {
      color: colors.textColor,
      borderColor: colors.borderColor,
      backgroundColor: colors.bgColor,
    },
    darkStyle: {
      backgroundColor: colors.bgColorDark,
    },
  };
};

// Helper function to normalize elemental forces data to Five Elements format
const normalizeElementalForces = (lucLuongNguHanh: any) => {
  // Check if it's already in the new format (has Five Elements keys)
  if (lucLuongNguHanh && typeof lucLuongNguHanh === "object") {
    const keys = Object.keys(lucLuongNguHanh);
    const hasNewFormat = keys.some((key) => ["Mộc", "Hỏa", "Thổ", "Kim", "Thủy"].includes(key));
    const hasOldFormat = keys.some((key) => ["Giáp", "Ất", "Bính", "Đinh", "Mậu", "Kỷ", "Canh", "Tân", "Nhâm", "Quý"].includes(key));

    if (hasNewFormat && !hasOldFormat) {
      // Already in new format, return as is
      return {
        Mộc: Number(lucLuongNguHanh.Mộc) || 0,
        Hỏa: Number(lucLuongNguHanh.Hỏa) || 0,
        Thổ: Number(lucLuongNguHanh.Thổ) || 0,
        Kim: Number(lucLuongNguHanh.Kim) || 0,
        Thủy: Number(lucLuongNguHanh.Thủy) || 0,
      };
    } else if (hasOldFormat) {
      // Convert from old format to new format
      const elementTotals = {
        Mộc: 0,
        Hỏa: 0,
        Thổ: 0,
        Kim: 0,
        Thủy: 0,
      };

      // Sum up the Can values by their corresponding elements
      Object.entries(ELEMENT_GROUPS).forEach(([element, stems]) => {
        const total = stems.reduce((sum, stem) => {
          return sum + (Number(lucLuongNguHanh[stem]) || 0);
        }, 0);
        elementTotals[element as keyof typeof elementTotals] = total;
      });

      return elementTotals;
    }
  }

  // Fallback to default values
  return {
    Mộc: 0,
    Hỏa: 0,
    Thổ: 0,
    Kim: 0,
    Thủy: 0,
  };
};

export function BatTuInfo({ batTuData, personName }: { batTuData: any; personName?: string }) {
  const [screenSize, setScreenSize] = useState({
    isMobile: false,
    isSmallMobile: false,
  });

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        isMobile: window.innerWidth < 768,
        isSmallMobile: window.innerWidth < 480,
      });
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Safely extract data from the response with fallbacks
  const responseData = batTuData?.response?.[0] || batTuData;

  const tuTru = responseData?.["Tứ trụ"] || {
    month: "-",
    day: "-",
    year: "-",
    hour: "-",
  };

  // Helper function to get element color for a stem or branch
  const getElementColor = (text: string) => {
    // First check if it's a stem
    if (text in STEM_TO_ELEMENT) {
      const element = STEM_TO_ELEMENT[text as keyof typeof STEM_TO_ELEMENT];
      const stemInElement = ELEMENT_GROUPS[element as keyof typeof ELEMENT_GROUPS][0];
      return ELEMENT_COLORS[stemInElement as keyof typeof ELEMENT_COLORS];
    }

    // Then check if it's a branch
    if (text in BRANCH_TO_ELEMENT) {
      const element = BRANCH_TO_ELEMENT[text as keyof typeof BRANCH_TO_ELEMENT];
      const stemInElement = ELEMENT_GROUPS[element as keyof typeof ELEMENT_GROUPS][0];
      return ELEMENT_COLORS[stemInElement as keyof typeof ELEMENT_COLORS];
    }

    return "#888888"; // Default color if not found
  };

  // Parse the Tứ Trụ values to extract stems and branches
  const parsePillar = (value: string) => {
    if (!value || value === "-") return { stem: "-", branch: "-" };

    // Assuming the format is "Stem Branch" (e.g., "Tân Mùi")
    const parts = value.split(" ");
    if (parts.length >= 2) {
      return { stem: parts[0], branch: parts[1] };
    }

    return { stem: value, branch: "" };
  };

  // Parse each pillar
  const parsedPillars = {
    year: parsePillar(tuTru.year),
    month: parsePillar(tuTru.month),
    day: parsePillar(tuTru.day),
    hour: parsePillar(tuTru.hour),
  };

  // We're not using this data yet, but it's available in the response
  // const thienCanAnTang = responseData?.["Thiên can ẩn tàng"] || [];

  // Normalize the elemental forces data to handle both old and new formats
  const lucLuongNguHanh = normalizeElementalForces(responseData?.["Lực lượng ngũ hành"]);

  const thanSinhPhu = responseData?.["Thần sinh phù"] || {
    "Ấn Tinh": "-",
    "Quan Tinh": "-",
    "Tài Tinh": "-",
    "Tỷ Kiếp": "-",
    "Thực Thương": "-",
    "Thiên Quan": "-",
  };

  // Calculate the dominant element based on the highest total value
  const elementTotals = Object.entries(lucLuongNguHanh).map(([element, total]) => ({
    element,
    total: Number(total) || 0,
  }));

  const dominantElement = elementTotals.reduce((max, current) => (current.total > max.total ? current : max), { element: "Thủy", total: 0 }).element;

  const beneficialGods: string[] = responseData?.["Dụng Thần"] || [];
  const unfavorableGods: string[] = responseData?.["Kỵ Thần"] || [];
  const favorableGods: string[] = responseData?.["Hỷ Thần"] || [];

  // Group forces by element for the stat bars
  const elementForces = Object.entries(lucLuongNguHanh)
    .map(([element, total]) => ({
      element,
      total: Number(total) || 0,
      barColor: ELEMENT_BAR_COLORS[element as keyof typeof ELEMENT_BAR_COLORS],
    }))
    .sort((a, b) => b.total - a.total); // Sort by total in descending order

  // Calculate the maximum element value for relative bar sizing
  const maxElementValue = Math.max(...elementForces.map((force) => force.total));

  // Add percentage calculation based on the highest element value
  const elementForcesWithPercentage = elementForces.map((force) => ({
    ...force,
    percentage: maxElementValue > 0 ? (force.total / maxElementValue) * 100 : 0,
  }));

  return (
    <div className="flex justify-center w-full">
      <style jsx>{`
        .card {
          --primary: ${dominantElement === "Thủy"
            ? "#2196F3"
            : dominantElement === "Hỏa"
            ? "#F44336"
            : dominantElement === "Mộc"
            ? "#4CAF50"
            : dominantElement === "Kim"
            ? "#9E9E9E"
            : "#FFC107"};
          --primary-hover: ${dominantElement === "Thủy"
            ? "#1976D2"
            : dominantElement === "Hỏa"
            ? "#D32F2F"
            : dominantElement === "Mộc"
            ? "#388E3C"
            : dominantElement === "Kim"
            ? "#757575"
            : "#F57C00"};
          --secondary: #458588;
          --secondary-hover: #83a598;
          --accent: #fabd2f;
          --text: #3c3836;
          --bg: #fbf1c7;
          --shadow-color: #282828;
          --pattern-color: #d5c4a1;

          position: relative;
          width: 100%;
          max-width: 450px;
          background: var(--bg);
          border: 0.35em solid var(--text);
          border-radius: 0.6em;
          box-shadow: 0.7em 0.7em 0 var(--shadow-color), inset 0 0 0 0.15em rgba(0, 0, 0, 0.05);
          transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
          overflow: hidden;
          font-family: ui-sans-serif, system-ui, sans-serif;
          transform-origin: center;
        }

        .dark .card {
          --primary: ${dominantElement === "Thủy"
            ? "#2196F3"
            : dominantElement === "Hỏa"
            ? "#F44336"
            : dominantElement === "Mộc"
            ? "#4CAF50"
            : dominantElement === "Kim"
            ? "#9E9E9E"
            : "#FFC107"};
          --primary-hover: ${dominantElement === "Thủy"
            ? "#1976D2"
            : dominantElement === "Hỏa"
            ? "#D32F2F"
            : dominantElement === "Mộc"
            ? "#388E3C"
            : dominantElement === "Kim"
            ? "#757575"
            : "#F57C00"};
          --secondary: #83a598;
          --secondary-hover: #458588;
          --accent: #fabd2f;
          --text: #fbf1c7;
          --bg: #3c3836;
          --shadow-color: #1d2021;
          --pattern-color: #504945;
        }

        .card:hover {
          transform: translate(-0.4em, -0.4em) scale(1.02);
          box-shadow: 1em 1em 0 var(--shadow-color);
        }

        .card:hover .card-pattern-grid,
        .card:hover .card-overlay-dots {
          opacity: 1;
        }

        .card::before {
          content: "";
          position: absolute;
          top: -1em;
          right: -1em;
          width: 4em;
          height: 4em;
          background: var(--accent);
          transform: rotate(45deg);
          z-index: 1;
        }

        .card::after {
          content: "命";
          position: absolute;
          top: 0.4em;
          right: 0.4em;
          color: var(--text);
          font-size: 1.2em;
          font-weight: bold;
          z-index: 2;
        }

        .card-pattern-grid {
          position: absolute;
          inset: 0;
          background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
          background-size: 0.5em 0.5em;
          pointer-events: none;
          opacity: 0.5;
          transition: opacity 0.4s ease;
          z-index: 1;
        }

        .card-overlay-dots {
          position: absolute;
          inset: 0;
          background-image: radial-gradient(var(--pattern-color) 1px, transparent 1px);
          background-size: 1em 1em;
          background-position: -0.5em -0.5em;
          pointer-events: none;
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: 1;
        }

        .card-title-area {
          position: relative;
          padding: 1.4em;
          background: var(--primary);
          color: var(--bg);
          font-weight: 800;
          font-size: 1.2em;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 0.35em solid var(--text);
          text-transform: uppercase;
          letter-spacing: 0.05em;
          z-index: 2;
          overflow: hidden;
        }

        .card-title-area::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: repeating-linear-gradient(45deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1) 0.5em, transparent 0.5em, transparent 1em);
          pointer-events: none;
          opacity: 0.3;
        }

        .card-tag {
          background: var(--bg);
          color: var(--text);
          font-size: 0.6em;
          font-weight: 800;
          padding: 0.4em 0.8em;
          border: 0.15em solid var(--text);
          border-radius: 0.3em;
          box-shadow: 0.2em 0.2em 0 var(--shadow-color);
          text-transform: uppercase;
          letter-spacing: 0.1em;
          transform: rotate(3deg);
          transition: all 0.3s ease;
        }

        .card:hover .card-tag {
          transform: rotate(-2deg) scale(1.1);
          box-shadow: 0.25em 0.25em 0 var(--shadow-color);
        }

        .card-body {
          position: relative;
          padding: 1.5em;
          z-index: 2;
        }

        .element-badge {
          width: 3em;
          height: 3em;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: bold;
          font-size: 1.2em;
          color: var(--bg);
          border: 0.15em solid var(--text);
          box-shadow: 0.2em 0.2em 0 var(--shadow-color);
        }

        .pillar-container {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 1em;
          margin: 1.5em 0;
        }

        @media (max-width: 480px) {
          .pillar-container {
            grid-template-columns: repeat(2, 1fr);
            gap: 0.8em;
          }
        }

        .pillar-item {
          background: rgba(255, 255, 255, 0.3);
          border: 0.15em solid var(--text);
          border-radius: 0.5em;
          padding: 1em;
          text-align: center;
          transition: transform 0.2s ease;
          box-shadow: 0.2em 0.2em 0 rgba(0, 0, 0, 0.1);
        }

        .pillar-item:hover {
          transform: translateY(-0.2em);
          box-shadow: 0.3em 0.3em 0 rgba(0, 0, 0, 0.2);
        }

        .pillar-label {
          display: block;
          font-size: 0.8em;
          font-weight: 600;
          color: var(--text);
          opacity: 0.8;
          margin-bottom: 0.5em;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }

        .element-stats {
          margin: 1.5em 0;
        }

        .element-bar-container {
          display: flex;
          align-items: center;
          margin-bottom: 0.8em;
          gap: 0.8em;
        }

        .element-label {
          width: 3em;
          font-weight: 600;
          color: var(--text);
        }

        .element-bar {
          flex: 1;
          height: 1.2em;
          background: rgba(0, 0, 0, 0.1);
          border: 0.1em solid var(--text);
          border-radius: 0.3em;
          overflow: hidden;
          position: relative;
        }

        .element-bar-fill {
          height: 100%;
          transition: width 0.6s ease;
          position: relative;
        }

        .element-bar-fill::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: repeating-linear-gradient(
            45deg,
            transparent,
            transparent 0.2em,
            rgba(255, 255, 255, 0.1) 0.2em,
            rgba(255, 255, 255, 0.1) 0.4em
          );
        }

        .element-value {
          width: 2.5em;
          text-align: right;
          font-weight: 700;
          color: var(--text);
        }

        .gods-section {
          display: grid;
          grid-template-columns: ${favorableGods.length > 0 ? "repeat(3, 1fr)" : "repeat(2, 1fr)"};
          gap: 1em;
          margin-top: 1.5em;
        }

        @media (max-width: 400px) {
          .gods-section {
            grid-template-columns: 1fr;
            gap: 0.8em;
          }
        }

        .gods-column {
          text-align: center;
        }

        .gods-title {
          font-size: 0.9em;
          font-weight: 700;
          color: var(--primary);
          margin-bottom: 0.8em;
          padding-bottom: 0.3em;
          border-bottom: 0.1em solid var(--text);
          text-transform: uppercase;
          letter-spacing: 0.05em;
          white-space: nowrap;
        }

        @media (max-width: 480px) {
          .gods-title {
            font-size: 0.8em;
            white-space: normal;
            word-break: keep-all;
            overflow-wrap: break-word;
          }
        }

        .god-item {
          background: rgba(255, 255, 255, 0.2);
          border: 0.1em solid var(--text);
          border-radius: 0.3em;
          padding: 0.5em;
          margin-bottom: 0.5em;
          font-weight: 600;
          font-size: 0.9em;
          transition: all 0.2s ease;
          box-shadow: 0.1em 0.1em 0 rgba(0, 0, 0, 0.1);
        }

        .god-item:hover {
          transform: translateY(-0.1em);
          box-shadow: 0.2em 0.2em 0 rgba(0, 0, 0, 0.2);
        }

        .card-footer {
          text-align: center;
          padding: 1em;
          border-top: 0.15em dashed rgba(0, 0, 0, 0.2);
          margin-top: 1.5em;
          position: relative;
          z-index: 2;
        }

        .card-footer::before {
          content: "✂";
          position: absolute;
          top: -0.8em;
          left: 50%;
          transform: translateX(-50%) rotate(90deg);
          background: var(--bg);
          padding: 0 0.5em;
          font-size: 1em;
          color: rgba(0, 0, 0, 0.4);
        }

        .footer-text {
          font-size: 0.7em;
          color: var(--text);
          opacity: 0.7;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      `}</style>

      <div className="card">
        <div className="card-pattern-grid" />
        <div className="card-overlay-dots" />

        {/* Card Header */}
        <div className="card-title-area">
          <span style={{ fontSize: screenSize.isSmallMobile ? "0.9em" : "1em" }}>{personName ? `${personName} - BÁT TỰ` : "TỨ TRỤ BÁT TỰ"}</span>
          <div
            className="element-badge"
            style={{
              background:
                dominantElement === "Thủy"
                  ? "#2196F3"
                  : dominantElement === "Hỏa"
                  ? "#F44336"
                  : dominantElement === "Mộc"
                  ? "#4CAF50"
                  : dominantElement === "Kim"
                  ? "#9E9E9E"
                  : "#FFC107",
            }}
          >
            {dominantElement === "Thủy"
              ? "水"
              : dominantElement === "Hỏa"
              ? "火"
              : dominantElement === "Mộc"
              ? "木"
              : dominantElement === "Kim"
              ? "金"
              : "土"}
          </div>
        </div>

        <div className="card-body">
          {/* Element image */}

          {/* Pillars Section */}
          <div className="pillar-container">
            {Object.entries(parsedPillars).map(([pillarType, pillar]) => (
              <div key={pillarType} className="pillar-item">
                <span className="pillar-label">{PILLAR_NAMES[pillarType as keyof typeof PILLAR_NAMES]}</span>
                {screenSize.isMobile ? (
                  <div style={{ fontSize: "1em", fontWeight: "bold" }}>
                    <span style={{ color: getElementColor(pillar.stem) }}>{pillar.stem}</span>{" "}
                    <span style={{ color: getElementColor(pillar.branch) }}>{pillar.branch}</span>
                  </div>
                ) : (
                  <div style={{ display: "flex", flexDirection: "column", gap: "0.3em" }}>
                    <span style={{ fontSize: "1.1em", fontWeight: "bold", color: getElementColor(pillar.stem) }}>{pillar.stem}</span>
                    <span style={{ fontSize: "1.1em", fontWeight: "bold", color: getElementColor(pillar.branch) }}>{pillar.branch}</span>
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* Element Stats */}
          <div className="element-stats">
            <h2
              style={{
                fontSize: "1.1em",
                fontWeight: "700",
                color: "var(--primary)",
                textAlign: "center",
                marginBottom: "1em",
                textTransform: "uppercase",
                letterSpacing: "0.05em",
              }}
            >
              Lực Lượng Ngũ Hành
            </h2>

            {elementForcesWithPercentage.map((force) => (
              <div key={force.element} className="element-bar-container">
                <span className="element-label">{force.element}</span>
                <div className="element-bar">
                  <div
                    className="element-bar-fill"
                    style={{
                      width: `${force.percentage}%`,
                      background:
                        force.element === "Mộc"
                          ? "#4CAF50"
                          : force.element === "Hỏa"
                          ? "#F44336"
                          : force.element === "Thổ"
                          ? "#FFC107"
                          : force.element === "Kim"
                          ? "#9E9E9E"
                          : "#2196F3",
                    }}
                  />
                </div>
                <span className="element-value">{force.total}</span>
              </div>
            ))}
          </div>

          {/* Gods Section */}
          <div className="gods-section">
            {/* Beneficial Gods */}
            <div className="gods-column">
              <h2 className="gods-title">Dụng Thần</h2>
              {beneficialGods.length > 0 ? (
                beneficialGods.map((god) => {
                  const styling = getElementGodStyling(god);
                  return (
                    <div key={god} className="god-item" style={styling.style}>
                      {god}
                    </div>
                  );
                })
              ) : (
                <div className="god-item" style={{ opacity: 0.6 }}>
                  Không có
                </div>
              )}
            </div>

            {/* Favorable Gods - Only show if there are favorable gods */}
            {favorableGods.length > 0 && (
              <div className="gods-column">
                <h2 className="gods-title">Hỷ Thần</h2>
                {favorableGods.map((god) => {
                  const styling = getElementGodStyling(god);
                  return (
                    <div key={god} className="god-item" style={{ ...styling.style, opacity: 0.9 }}>
                      {god}
                    </div>
                  );
                })}
              </div>
            )}

            {/* Unfavorable Gods */}
            <div className="gods-column">
              <h2 className="gods-title">Kỵ Thần</h2>
              {unfavorableGods.length > 0 ? (
                unfavorableGods.map((god) => {
                  const styling = getElementGodStyling(god);
                  return (
                    <div key={god} className="god-item" style={{ ...styling.style, opacity: 0.75 }}>
                      {god}
                    </div>
                  );
                })
              ) : (
                <div className="god-item" style={{ opacity: 0.6 }}>
                  Không có
                </div>
              )}
            </div>
          </div>

          {/* Card Footer */}
          <div className="card-footer">
            <p className="footer-text">Bát Tự Destiny Card - Series I</p>
          </div>
        </div>
      </div>
    </div>
  );
}
