/**
 * API route for handling PostHog analytics events
 * This provides a server-side proxy for PostHog to avoid CSP issues
 */
import { type NextRequest, NextResponse } from "next/server";

// PostHog API endpoint
const POSTHOG_API = "https://us.i.posthog.com";

/**
 * Handle GET requests (for decide, config, etc.)
 */
export async function GET(request: NextRequest) {
  try {
    // Get the path from the request
    const url = new URL(request.url);
    const path = url.pathname.replace("/api/analytics", "");

    // If it's a root request or decide request, return a simple config
    if (path === "" || path === "/" || path.includes("decide")) {
      return NextResponse.json({
        config: {
          disable_session_recording: true,
          autocapture: false,
        },
        featureFlags: {},
        sessionRecording: { endpoint: "" },
      });
    }

    // For other GET requests, forward to PostHog
    const response = await fetch(`${POSTHOG_API}${path}${url.search}`, {
      method: "GET",
      headers: request.headers,
    });

    // If it's JSON, return as JSON
    const contentType = response.headers.get("content-type");
    if (contentType?.includes("application/json")) {
      const data = await response.json();
      return NextResponse.json(data);
    }

    // Otherwise return the raw response
    return new NextResponse(await response.text(), {
      status: response.status,
      headers: {
        "Content-Type": contentType || "text/plain",
      },
    });
  } catch (error) {
    console.error("Error handling GET request:", error);
    return NextResponse.json({ error: "Failed to handle request" }, { status: 500 });
  }
}

/**
 * Handle POST requests for analytics events
 */
export async function POST(request: NextRequest) {
  try {
    // Get the path from the request
    const url = new URL(request.url);
    const path = url.pathname.replace("/api/analytics", "");

    // Default endpoint is capture if none specified
    const endpoint = path === "" || path === "/" ? "/capture/" : path;

    // Get the request body
    const body = await request.json();

    // Forward the request to PostHog
    const response = await fetch(`${POSTHOG_API}${endpoint}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    // Return the response from PostHog
    try {
      const data = await response.json();
      return NextResponse.json(data);
    } catch (e) {
      // If not JSON, return text
      return new NextResponse(await response.text(), {
        status: response.status,
      });
    }
  } catch (error) {
    console.error("Error forwarding analytics event:", error);
    return NextResponse.json({ error: "Failed to forward analytics event" }, { status: 500 });
  }
}

/**
 * Handle OPTIONS requests (for CORS)
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  });
}
