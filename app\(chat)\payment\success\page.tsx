"use client";

import { useEffect, useState, useCallback } from "react";
import { useSearch<PERSON>ara<PERSON>, useRouter } from "next/navigation";
import Link from "next/link";
import { CheckCircle, ArrowLeft, Coins, CreditCard } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CreditDisplay } from "@/components/credit-display";
import { useAuth } from "@/lib/auth/auth-context";

interface PaymentDetails {
  paymentId: string;
  orderCode: string;
  amount: number;
  currency: string;
  status: string;
  createdAt: string;
  paidAt?: string;
}

export default function PaymentSuccessPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { refreshCredits } = useAuth();
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const orderCode = searchParams.get("orderCode");

  const fetchPaymentDetails = useCallback(async () => {
    try {
      console.log("Fetching payment details for order:", orderCode);
      const response = await fetch(`/api/payment/create?orderCode=${orderCode}`);
      const data = await response.json();

      console.log("Payment details response:", data);

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch payment details");
      }

      setPaymentDetails(data.data);

      // Refresh user credits if payment was successful
      if (data.data.status === "PAID") {
        console.log("Payment confirmed as PAID, refreshing credits");
        await refreshCredits();
        console.log("Credits refreshed successfully");
      } else {
        console.log("Payment status:", data.data.status, "- not refreshing credits yet");
      }
    } catch (err) {
      console.error("Failed to fetch payment details:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch payment details");
    } finally {
      setLoading(false);
    }
  }, [orderCode, refreshCredits]);

  useEffect(() => {
    if (!orderCode) {
      setError("No order code provided");
      setLoading(false);
      return;
    }

    fetchPaymentDetails();
  }, [orderCode, fetchPaymentDetails]);

  if (loading) {
    return (
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 safe-area-top border-b">
          <Button variant="ghost" size="sm" asChild className="shrink-0">
            <Link href="/payment" className="gap-2">
              <ArrowLeft className="size-4" />
              <span className="hidden sm:inline">Back to Payment</span>
              <span className="sm:hidden">Back</span>
            </Link>
          </Button>
          <div className="ml-auto">
            <CreditDisplay size="md" />
          </div>
        </header>

        <main className="flex-1 overflow-y-auto px-3 sm:px-4 md:px-6 py-4 sm:py-6">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full size-8 border-b-2 border-primary" />
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (error || !paymentDetails) {
    return (
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 safe-area-top border-b">
          <Button variant="ghost" size="sm" asChild className="shrink-0">
            <Link href="/payment" className="gap-2">
              <ArrowLeft className="size-4" />
              <span className="hidden sm:inline">Back to Payment</span>
              <span className="sm:hidden">Back</span>
            </Link>
          </Button>
          <div className="ml-auto">
            <CreditDisplay size="md" />
          </div>
        </header>

        <main className="flex-1 overflow-y-auto px-3 sm:px-4 md:px-6 py-4 sm:py-6">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-destructive">Payment Error</CardTitle>
                <CardDescription>{error || "Payment details not found"}</CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button asChild>
                  <Link href="/payment">Return to Payment</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    );
  }

  const isSuccess = paymentDetails.status === "PAID";

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background">
      <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 safe-area-top border-b">
        <Button variant="ghost" size="sm" asChild className="shrink-0">
          <Link href="/" className="gap-2">
            <ArrowLeft className="size-4" />
            <span className="hidden sm:inline">Back to Chat</span>
            <span className="sm:hidden">Back</span>
          </Link>
        </Button>
        <div className="ml-auto">
          <CreditDisplay size="md" />
        </div>
      </header>

      <main className="flex-1 overflow-y-auto px-3 sm:px-4 md:px-6 py-4 sm:py-6">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                {isSuccess ? <CheckCircle className="size-16 text-green-600" /> : <CreditCard className="size-16 text-muted-foreground" />}
              </div>
              <CardTitle className={isSuccess ? "text-green-600" : ""}>{isSuccess ? "Payment Successful!" : "Payment Processing"}</CardTitle>
              <CardDescription>{isSuccess ? "Your credits have been added to your account" : "Your payment is being processed"}</CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="bg-muted rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Order Code:</span>
                  <Badge variant="outline">{paymentDetails.orderCode}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Amount:</span>
                  <span className="font-medium">
                    {paymentDetails.amount.toLocaleString()} {paymentDetails.currency}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant={isSuccess ? "default" : "secondary"}>{paymentDetails.status}</Badge>
                </div>
                {paymentDetails.paidAt && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Paid At:</span>
                    <span className="text-sm">{new Date(paymentDetails.paidAt).toLocaleString()}</span>
                  </div>
                )}
              </div>

              {isSuccess && (
                <div className="text-center space-y-4">
                  <div className="flex items-center justify-center gap-2 text-green-600">
                    <Coins className="size-5" />
                    <span className="font-medium">Credits added to your account!</span>
                  </div>
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-3">
                <Button asChild className="flex-1">
                  <Link href="/">Start Chatting</Link>
                </Button>
                <Button variant="outline" asChild className="flex-1">
                  <Link href="/payment">Buy More Credits</Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
