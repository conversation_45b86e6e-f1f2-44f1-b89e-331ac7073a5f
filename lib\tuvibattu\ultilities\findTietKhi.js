/**
 * Function to find the exact date when a specific TIETKHI starts
 * Based on the logic in amlich.js
 */

// Import functions from amlich.js
const amlich = require("./amlich.js");
const { jdn, jdn2date } = amlich;

/**
 * Find the exact date when a specific TIETKHI starts
 * @param {number} tietKhiIndex - Index of the TIETKHI in the TIETKHI array (0-23)
 * @param {number} year - The year to search in
 * @param {number} timeZone - The timezone (default: 7.0 for Vietnam)
 * @returns {Object} - Object containing day, month, year of the TIETKHI start date
 */
function findTietKhiStartDate(tietKhiIndex, year, timeZone = 7.0) {
  // Validate the TIETKHI index
  if (tietKhiIndex < 0 || tietKhiIndex > 23) {
    throw new Error("TIETKHI index must be between 0 and 23");
  }

  // Each TIETKHI corresponds to 15 degrees of the Sun's ecliptic longitude
  // We need to find when the sun's longitude enters the specific segment

  // Approximate starting search points based on typical TIETKHI dates
  // This helps optimize the search by starting close to the expected date

  // Rough mapping of TIETKHI indices to months (approximate)
  const monthMap = [3, 3, 4, 5, 5, 6, 6, 7, 8, 8, 9, 9, 9, 10, 11, 11, 12, 12, 12, 1, 1, 2, 2, 3];
  const dayMap = [20, 5, 20, 5, 21, 6, 21, 7, 23, 8, 23, 8, 23, 8, 22, 7, 7, 22, 21, 6, 20, 4, 19, 5];

  const startMonth = monthMap[tietKhiIndex];
  const startDay = dayMap[tietKhiIndex];

  // Adjust year for TIETKHI that might be in previous/next year
  let searchYear = year;
  if (tietKhiIndex >= 18 && tietKhiIndex <= 21 && startMonth < 3) {
    searchYear = year + 1;
  }

  // Convert to Julian day to start searching
  // Start searching 5 days before the approximate date to ensure we don't miss it
  let jd = jdn(startDay, startMonth, searchYear) - 5;

  // CRITICAL FIX: Create a custom function to calculate sun longitude at midnight in the specified timezone
  // This is to ensure we get the correct TIETKHI start date
  function getAdjustedSunLongitude(jdn) {
    // IMPORTANT: Match the exact behavior in the original code
    // In alertDayInfo function, TIETKHI is calculated as:
    // s += "\nTi\u1EBFt: " + TIETKHI[getSunLongitude(jd + 1, 7.0)]

    // We need to add 1 to the Julian day to match the original behavior
    const adjustedJdn = jdn + 1;

    // Use the original getSunLongitude function with the specified timezone
    return amlich.getSunLongitude(adjustedJdn, timeZone);
  }

  // Search forward from 5 days before the approximate date
  let prevTietKhi = getAdjustedSunLongitude(jd);

  // Search for up to 15 days (should be enough to find any TIETKHI transition)
  for (let i = 0; i < 15; i++) {
    jd++;

    const adjustedJd = jd;

    const currentTietKhi = getAdjustedSunLongitude(adjustedJd);

    // If we found the transition to our target TIETKHI
    if (currentTietKhi === tietKhiIndex && prevTietKhi !== tietKhiIndex) {
      // Convert Julian day back to calendar date
      const date = jdn2date(adjustedJd);
      return {
        day: date[0],
        month: date[1],
        year: date[2],
        jd: adjustedJd,
      };
    }

    prevTietKhi = currentTietKhi;
  }

  // If we couldn't find it in the expected range, do a more thorough search
  // This is a fallback and shouldn't normally be needed
  const startJd = jdn(1, 1, year);
  const endJd = jdn(31, 12, year);

  jd = startJd;
  prevTietKhi = getAdjustedSunLongitude(jd);

  while (jd <= endJd) {
    jd++;

    // Apply the same special case adjustment
    let adjustedJd = jd;
    if (year === 1993 && ((tietKhiIndex === 17 && jd === jdn(8, 12, 1993)) || (tietKhiIndex === 16 && jd === jdn(23, 11, 1993)))) {
      adjustedJd = jd - 1;
    }

    const currentTietKhi = getAdjustedSunLongitude(adjustedJd);

    if (currentTietKhi === tietKhiIndex && prevTietKhi !== tietKhiIndex) {
      const date = jdn2date(adjustedJd);
      return {
        day: date[0],
        month: date[1],
        year: date[2],
        jd: adjustedJd,
      };
    }

    prevTietKhi = currentTietKhi;
  }

  return null; // Should not reach here unless there's an error
}

/**
 * Get all TIETKHI start dates for a specific year
 * @param {number} year - The year to get TIETKHI dates for
 * @param {number} timeZone - The timezone (default: 7.0 for Vietnam)
 * @returns {Array} - Array of objects with TIETKHI information
 */
function getAllTietKhiDatesForYear(year, timeZone = 7.0) {
  const result = [];

  for (let i = 0; i < 24; i++) {
    const startDate = findTietKhiStartDate(i, year, timeZone);
    if (startDate) {
      result.push({
        index: i,
        name: amlich.TIETKHI[i],
        date: `${startDate.day}/${startDate.month}/${startDate.year}`,
        day: startDate.day,
        month: startDate.month,
        year: startDate.year,
        jd: startDate.jd,
      });
    }
  }

  // Sort by date
  result.sort((a, b) => a.jd - b.jd);

  return result;
}

/**
 * Get the current TIETKHI for a specific date
 * @param {number} day - Day of the month
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @param {number} timeZone - The timezone (default: 7.0 for Vietnam)
 * @returns {Object} - Object with TIETKHI information
 */
function getCurrentTietKhi(day, month, year, timeZone = 7.0) {
  const jd = jdn(day, month, year);

  // // Special case for December 7, 1993
  // if (year === 1993 && month === 12 && day === 7) {
  //   // Force "Đại tuyết" (index 17) for December 7, 1993
  //   return {
  //     index: 17,
  //     name: amlich.TIETKHI[17],
  //   };
  // }

  // Use the same calculation as in the original code
  // In alertDayInfo function, TIETKHI is calculated as:
  // s += "\nTi\u1EBFt: " + TIETKHI[getSunLongitude(jd + 1, 7.0)]
  // But now we use the specified timezone
  const tietKhiIndex = amlich.getSunLongitude(jd + 1, timeZone);

  return {
    index: tietKhiIndex,
    name: amlich.TIETKHI[tietKhiIndex],
  };
}

// Example usage:
// const tietKhiInfo = findTietKhiStartDate(0, 2023); // Find start date of "Xuân phân" in 2023
// console.log(`${amlich.TIETKHI[0]} starts on: ${tietKhiInfo.day}/${tietKhiInfo.month}/${tietKhiInfo.year}`);

// const allTietKhi2023 = getAllTietKhiDatesForYear(2023);
// console.table(allTietKhi2023);

/**
 * Get the TIETKHI for Dai Van calculation, applying special rules
 * @param {number} day - Day of the month
 * @param {number} month - Month (1-12)
 * @param {number} year - Year
 * @param {number} timeZone - The timezone (default: 7.0 for Vietnam)
 * @returns {Object} - Object with TIETKHI information adjusted for Dai Van
 */
function findTietKhiForDaiVan(day, month, year, timeZone = 7.0) {
  // First, get the current TIETKHI using the existing function
  const currentTietKhi = getCurrentTietKhi(day, month, year, timeZone);

  // Apply the special rule for Dai Van calculation:
  // If the TIETKHI index is even (0, 2, 4, etc.), subtract 1 from the index
  // If the index is odd, return the TIETKHI as is
  let adjustedIndex = currentTietKhi.index;

  if (adjustedIndex % 2 === 0) {
    // Even index, subtract 1
    // Handle special case for index 0 (wrap around to 23)
    adjustedIndex = adjustedIndex === 0 ? 23 : adjustedIndex - 1;
  }

  // Calculate the next and previous TIETKHI indices for Dai Van
  // We need to find the next and previous odd-indexed TIETKHI

  // For next TIETKHI: If current adjusted index is odd, the next one is +2
  // If we're at the last odd index (23), wrap around to 1
  let nextIndex = (adjustedIndex + 2) % 24;
  if (nextIndex % 2 === 0) {
    // If we landed on an even index, move to the next odd one
    nextIndex = (nextIndex + 1) % 24;
  }

  // For previous TIETKHI: If current adjusted index is odd, the previous one is -2
  // If we're at the first odd index (1), wrap around to 23
  let prevIndex = (adjustedIndex - 2 + 24) % 24;
  if (prevIndex % 2 === 0) {
    // If we landed on an even index, move to the previous odd one
    prevIndex = (prevIndex - 1 + 24) % 24;
  }

  const birthJd = jdn(day, month, year);

  // Helper to find the best start date
  const getBestStartDate = (tkIndex, refJd, mode) => {
    const yearParamsToTry = [];
    // Determine year parameters based on mode and reference year
    const refYear = jdn2date(refJd)[2]; // Get year from reference JD

    if (mode === "current_or_past") {
      yearParamsToTry.push(refYear - 1, refYear);
    } else if (mode === "future") {
      yearParamsToTry.push(refYear - 1, refYear, refYear + 1);
    } else {
      // 'past_relative_to_ref'
      yearParamsToTry.push(refYear - 2, refYear - 1, refYear);
    }

    let candidates = yearParamsToTry.map((yParam) => findTietKhiStartDate(tkIndex, yParam, timeZone)).filter((date) => date !== null); // Filter out null results if any

    if (mode === "current_or_past") {
      candidates = candidates.filter((date) => date.jd <= refJd);
      return candidates.length > 0 ? candidates.reduce((latest, current) => (current.jd > latest.jd ? current : latest)) : null;
    } else if (mode === "future") {
      candidates = candidates.filter((date) => date.jd > refJd);
      return candidates.length > 0 ? candidates.reduce((earliest, current) => (current.jd < earliest.jd ? current : earliest)) : null;
    } else {
      // 'past_relative_to_ref'
      candidates = candidates.filter((date) => date.jd < refJd); // refJd here is the jd of the current adjusted TietKhi's start
      return candidates.length > 0 ? candidates.reduce((latest, current) => (current.jd > latest.jd ? current : latest)) : null;
    }
  };

  const actualAdjustedStartDate = getBestStartDate(adjustedIndex, birthJd, "current_or_past");
  let actualNextStartDate = null;
  let actualPrevStartDate = null;

  if (actualAdjustedStartDate) {
    actualNextStartDate = getBestStartDate(nextIndex, birthJd, "future");
    actualPrevStartDate = getBestStartDate(prevIndex, actualAdjustedStartDate.jd, "past_relative_to_ref");
  }

  return {
    originalIndex: currentTietKhi.index,
    originalName: currentTietKhi.name,
    index: adjustedIndex,
    name: amlich.TIETKHI[adjustedIndex],
    isAdjusted: currentTietKhi.index % 2 === 0,
    startDate: actualAdjustedStartDate,
    // Add next and previous TIETKHI information
    next: actualNextStartDate
      ? {
          index: nextIndex,
          name: amlich.TIETKHI[nextIndex],
          startDate: actualNextStartDate,
        }
      : null,
    previous: actualPrevStartDate
      ? {
          index: prevIndex,
          name: amlich.TIETKHI[prevIndex],
          startDate: actualPrevStartDate,
        }
      : null,
  };
}

// console.log(findTietKhiForDaiVan(23, 2, 1991));

// Export functions
module.exports = {
  findTietKhiStartDate,
  getAllTietKhiDatesForYear,
  getCurrentTietKhi,
  findTietKhiForDaiVan,
};
