const CONSTANTS = require("../constants");
const Common = require("./common");
const { findTietKhiForDaiVan } = require("./findTietKhi");

const DaiVanUtil = {};

const getOffset = (timeZone = "UTC", date = new Date()) => {
  const utcDate = new Date(date.toLocaleString("en-US", { timeZone: "UTC" }));
  const tzDate = new Date(date.toLocaleString("en-US", { timeZone }));
  return (tzDate.getTime() - utcDate.getTime()) / 6e4;
};

DaiVanUtil.getDaiVan = (birthDate, hour, gender, timezone = "Asia/Ho_Chi_Minh") => {
  // get timezone offset from timezone
  const timezoneOffset = getOffset(timezone) / 60;
  // Đảm bảo birthDate được chuyển đổi sang múi giờ chính xác
  const birthDateToCompare = new Date(birthDate.year, birthDate.month - 1, birthDate.day, hour);
  const personInfo = Common.getLunarDateInfo(birthDate, hour, timezone);
  const seasons = findTietKhiForDaiVan(birthDate.day, birthDate.month, birthDate.year, timezoneOffset);
  // console.log(seasons);
  const thienCan = Common.getCanAmDuong(personInfo.year.split(" ")[0]);
  let direction;
  if (gender.toLowerCase() === "nam") {
    direction = thienCan === "Dương" ? "Thuận" : "Nghịch";
  } else {
    direction = thienCan === "Âm" ? "Thuận" : "Nghịch";
  }
  let daysDifferent;
  const currentSeasonDate = new Date(seasons.startDate.year, seasons.startDate.month - 1, seasons.startDate.day);
  const nextSeasonDate = new Date(seasons.next.startDate.year, seasons.next.startDate.month - 1, seasons.next.startDate.day);
  const prevSeasonDate = new Date(seasons.previous.startDate.year, seasons.previous.startDate.month - 1, seasons.previous.startDate.day);
  const magicNumber = (nextSeasonDate - currentSeasonDate) / 1000 / 3600 / 24 / 10;
  if (direction === "Thuận") {
    if (birthDateToCompare < currentSeasonDate) {
      daysDifferent = (currentSeasonDate - birthDateToCompare) / 1000 / 3600 / 24;
    } else {
      daysDifferent = (nextSeasonDate - birthDateToCompare) / 1000 / 3600 / 24;
    }
  } else {
    if (birthDateToCompare > currentSeasonDate) {
      daysDifferent = (birthDateToCompare - currentSeasonDate) / 1000 / 3600 / 24;
    } else {
      daysDifferent = (birthDateToCompare - prevSeasonDate) / 1000 / 3600 / 24;
    }
  }
  const yearStart = Math.round(daysDifferent / magicNumber) + birthDate.year;
  const listYears = generateListYears(yearStart, direction, personInfo.month);
  // console.log(listYears);
  return listYears;
};

const generateListYears = (startYear, direction, nguyetTru) => {
  const result = {};
  const startThienCan = nguyetTru.split(" ")[0];
  const startChi = nguyetTru.split(" ")[1];
  if (direction === "Thuận") {
    for (let i = 0; i < 16; i++) {
      if (i % 2 === 0) {
        result[startYear + i * 5] = CONSTANTS.CAN[(CONSTANTS.CAN.indexOf(startThienCan) + Math.floor(i / 2) + 1) % 10];
      } else {
        result[startYear + i * 5] = CONSTANTS.CHI[(CONSTANTS.CHI.indexOf(startChi) + Math.floor(i / 2) + 1) % 12];
      }
    }
  } else {
    for (let i = 0; i < 16; i++) {
      if (i % 2 === 0) {
        result[startYear + i * 5] = CONSTANTS.CAN[(CONSTANTS.CAN.indexOf(startThienCan) - Math.floor(i / 2) + 9) % 10];
      } else {
        result[startYear + i * 5] = CONSTANTS.CHI[(CONSTANTS.CHI.indexOf(startChi) - Math.floor(i / 2) + 11) % 12];
      }
    }
  }
  return result;
};

// DaiVanUtil.getDaiVan({ day: 19, month: 8, year: 1946 }, 8, "Nam", "America/Chicago");

module.exports = DaiVanUtil;
