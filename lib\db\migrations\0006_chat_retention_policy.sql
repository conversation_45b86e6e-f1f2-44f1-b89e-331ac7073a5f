-- Migration to add a 60-day retention policy for chat messages
-- NOTE: This migration is prepared but NOT TO BE APPLIED YET.
-- The retention policy will be implemented at a later date.

-- Create a function to delete messages older than 60 days
CREATE OR REPLACE FUNCTION delete_old_messages()
RETURNS TRIGGER AS $$
BEGIN
  -- Delete votes for messages older than 60 days
  DELETE FROM "Vote_v2"
  WHERE "messageId" IN (
    SELECT "id" FROM "Message_v2"
    WHERE "createdAt" < NOW() - INTERVAL '60 days'
  );

  -- Delete messages older than 60 days
  DELETE FROM "Message_v2"
  WHERE "createdAt" < NOW() - INTERVAL '60 days';

  -- Delete empty chats (chats with no messages)
  DELETE FROM "Chat"
  WHERE "id" NOT IN (
    SELECT DISTINCT "chatId" FROM "Message_v2"
  );

  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to run the function daily
CREATE OR REPLACE FUNCTION create_message_retention_trigger()
RETURNS VOID AS $$
BEGIN
  -- Drop the trigger if it already exists
  DROP TRIGGER IF EXISTS trigger_delete_old_messages ON "Message_v2";

  -- Create the trigger
  CREATE TRIGGER trigger_delete_old_messages
  AFTER INSERT ON "Message_v2"
  EXECUTE FUNCTION delete_old_messages();
END;
$$ LANGUAGE plpgsql;

-- Execute the function to create the trigger
SELECT create_message_retention_trigger();
