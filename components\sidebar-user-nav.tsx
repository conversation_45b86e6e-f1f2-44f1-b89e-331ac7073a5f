"use client";

import { ChevronUp, History } from "lucide-react";
import Image from "next/image";
import { useTheme } from "next-themes";
import { createClient } from "@/lib/supabase/client";
import { signOut } from "@/app/(auth)/auth";
import { useEffect, useState } from "react";
import type { SupabaseUser } from "@/app/(auth)/auth";

import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import Link from "next/link";
import { SidebarMenu, SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar";
import { useRouter } from "next/navigation";
import { toast } from "./toast";
import { LoaderIcon } from "./icons";
import { guestRegex } from "@/lib/constants";

export function SidebarUserNav({ user }: { user: SupabaseUser }) {
  const router = useRouter();
  const { setTheme, theme } = useTheme();
  const supabase = createClient();
  const [loading, setLoading] = useState(true);
  const [isGuest, setIsGuest] = useState(false);

  useEffect(() => {
    const checkSession = async () => {
      setLoading(true);
      try {
        // Use getUser following Supabase best practices
        const {
          data: { user },
        } = await supabase.auth.getUser();

        setIsGuest(user?.email ? guestRegex.test(user.email) : false);
      } catch (error) {
        console.error("Failed to check session in SidebarUserNav:", error);
        setIsGuest(false);
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // Set up auth state change listener
    const { data: authListener } = supabase.auth.onAuthStateChange(() => {
      checkSession();
    });

    return () => {
      authListener.subscription.unsubscribe();
    };
  }, [supabase]);

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            {loading ? (
              <SidebarMenuButton className="data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground h-10 justify-between">
                <div className="flex flex-row gap-2">
                  <div className="size-6 bg-zinc-500/30 rounded-full animate-pulse" />
                  <span className="bg-zinc-500/30 text-transparent rounded-md animate-pulse">Loading auth status</span>
                </div>
                <div className="animate-spin text-zinc-500">
                  <LoaderIcon />
                </div>
              </SidebarMenuButton>
            ) : (
              <SidebarMenuButton
                data-testid="user-nav-button"
                className="data-[state=open]:bg-sidebar-accent bg-background data-[state=open]:text-sidebar-accent-foreground h-10"
              >
                <Image
                  src={`https://avatar.vercel.sh/${user.email}`}
                  alt={user.email ?? "User Avatar"}
                  width={24}
                  height={24}
                  className="rounded-full"
                />
                <span data-testid="user-email" className="truncate">
                  {isGuest ? "Guest" : user?.email}
                </span>
                <ChevronUp className="ml-auto" />
              </SidebarMenuButton>
            )}
          </DropdownMenuTrigger>
          <DropdownMenuContent data-testid="user-nav-menu" side="top" className="w-[--radix-popper-anchor-width]">
            <DropdownMenuItem asChild>
              <Link href="/credit-history" className="flex items-center gap-2 cursor-pointer">
                <History className="size-4" />
                Credit History
              </Link>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              data-testid="user-nav-item-theme"
              className="cursor-pointer"
              onSelect={() => setTheme(theme === "dark" ? "light" : "dark")}
            >
              {`Toggle ${theme === "light" ? "dark" : "light"} mode`}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem asChild data-testid="user-nav-item-auth">
              <button
                type="button"
                className="w-full cursor-pointer"
                onClick={async () => {
                  if (loading) {
                    toast({
                      type: "error",
                      description: "Checking authentication status, please try again!",
                    });

                    return;
                  }

                  if (isGuest) {
                    router.push("/login");
                  } else {
                    try {
                      // Track logout event before signing out
                      // Note: We don't need to call trackLogout() here anymore
                      // as it's now handled inside the signOut function

                      // Sign out from Supabase
                      const result = await signOut();

                      if (!result.ok) {
                        throw new Error(result.error || "Sign out failed");
                      }

                      // Use router.push for navigation
                      router.push("/login");

                      // Force a clean reload after a short delay
                      // This ensures all state is cleared properly
                      setTimeout(() => {
                        // Clear any localStorage items that might be causing issues
                        try {
                          localStorage.removeItem("posthog_distinct_id");
                          localStorage.removeItem("ph_loaded");
                          localStorage.removeItem("ph_init_called");
                          localStorage.removeItem("ph_date_created");
                        } catch (e) {
                          console.warn("Error clearing localStorage:", e);
                        }

                        // Use replace instead of href to avoid adding to history
                        window.location.replace("/login");
                      }, 100);
                    } catch (error) {
                      console.error("Error signing out:", error);
                      toast({
                        type: "error",
                        description: "There was a problem signing out. Please try again.",
                      });
                      // Still try to redirect to login
                      router.push("/login");
                    }
                  }
                }}
              >
                {isGuest ? "Login to your account" : "Sign out"}
              </button>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}
