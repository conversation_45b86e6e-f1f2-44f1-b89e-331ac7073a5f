import { Toaster } from "sonner";
import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON>, <PERSON><PERSON> } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { PWAInstallerWrapper } from "@/components/pwa-installer-wrapper";
import { PostHogProvider } from "@/components/PostHogProvider";
import { UpdateNotifier } from "@/components/update-notifier";

import "./globals.css";
// No longer need SessionProvider with Supabase

export const metadata: Metadata = {
  metadataBase: new URL("https://chat.vercel.ai"),
  title: "<PERSON><PERSON><PERSON> Master V - Phán về số mệnh, cuộc đời, tình duyên, công việc",
  description: "Ứng dụng phân tích Bát Tự và tư vấn về số mệnh, cuộc đời, tình duyên, công việc.",
  applicationName: "<PERSON><PERSON><PERSON> Tự Master V",
  appleWebApp: {
    capable: true,
    title: "<PERSON><PERSON><PERSON> Tự Master V",
    statusBarStyle: "black-translucent",
  },
  formatDetection: {
    telephone: false,
  },
};

export const viewport = {
  maximumScale: 1, // Disable auto-zoom on mobile Safari
  width: "device-width",
  initialScale: 1,
  viewportFit: "cover", // Enable safe area insets
  userScalable: false, // Prevent pinch zooming
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#FDF0CF" },
    { media: "(prefers-color-scheme: dark)", color: "#9D0D11" },
  ],
};

const geist = Geist({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-geist",
});

const geistMono = Geist_Mono({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-geist-mono",
});

const pacifico = Pacifico({
  subsets: ["latin"],
  display: "swap",
  weight: "400",
  variable: "--font-pacifico",
});

export default function RootLayout({ children }: { children: React.ReactNode }) {
  // No longer need to get the session on the server side
  const isProduction = process.env.NODE_ENV !== "development";

  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`font-sans antialiased ${geist.variable} ${geistMono.variable} ${pacifico.variable}`}>
        {isProduction ? (
          <PostHogProvider>
            <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
              {children}
              <Toaster position="bottom-right" />
              <PWAInstallerWrapper />
              <UpdateNotifier showVersionInfo={false} />
            </ThemeProvider>
          </PostHogProvider>
        ) : (
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
            {children}
            <Toaster position="bottom-right" />
            <PWAInstallerWrapper />
            <UpdateNotifier showVersionInfo={true} />
          </ThemeProvider>
        )}
      </body>
    </html>
  );
}
