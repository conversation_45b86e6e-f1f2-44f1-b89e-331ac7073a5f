/**
 * Auth callback route handler for Supabase
 * This handles the code exchange after OAuth or email link authentication
 */
import { createServerClient } from "@supabase/ssr";
import { cookies } from "next/headers";
import { NextResponse, type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const code = requestUrl.searchParams.get("code");
  const next = requestUrl.searchParams.get("next") || "/";
  const errorParam = requestUrl.searchParams.get("error");
  const errorCode = requestUrl.searchParams.get("error_code");
  const errorDescription = requestUrl.searchParams.get("error_description");

  // Extract the origin from the request URL
  const origin = requestUrl.origin;

  // Handle error case from OAuth provider
  if (errorParam) {
    console.error("Auth callback received error:", {
      error: errorParam,
      errorCode,
      errorDescription,
    });

    // Check if we're in development mode
    const isLocalEnv = process.env.NODE_ENV === "development";
    const redirectBase = isLocalEnv ? process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3003" : origin;

    console.log("Error redirect to:", `${redirectBase}/login`);

    // Redirect to login with error information
    return NextResponse.redirect(`${redirectBase}/login?error=${encodeURIComponent(errorDescription || errorParam)}`);
  }

  if (code) {
    // Log the code exchange attempt for debugging
    console.log("Auth callback processing code exchange, URL:", request.url);

    const cookieStore = await cookies();
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error("Missing Supabase environment variables");
    }

    // Log available cookies for debugging flow state issues
    const allCookies = cookieStore.getAll();
    console.log("Auth callback cookies count:", allCookies.length);

    // Check for flow state cookie specifically
    const flowStateCookie = allCookies.find((cookie) => cookie.name.includes("flow-state"));
    if (!flowStateCookie) {
      console.warn("No flow state cookie found in the request");
    }

    const supabase = createServerClient(supabaseUrl, supabaseAnonKey, {
      cookies: {
        getAll() {
          return cookieStore.getAll();
        },
        setAll(cookiesToSet) {
          cookiesToSet.forEach(({ name, value, options }) => cookieStore.set(name, value, options));
        },
      },
    });

    // Exchange the code for a session
    const { error } = await supabase.auth.exchangeCodeForSession(code);

    if (error) {
      console.error("Auth callback error:", error.message);

      // Check if we're in development mode
      const isLocalEnv = process.env.NODE_ENV === "development";
      const redirectBase = isLocalEnv ? process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3003" : origin;

      console.log("Code exchange error redirect to:", `${redirectBase}/login`);

      // Redirect to login page with error message
      return NextResponse.redirect(`${redirectBase}/login?error=${encodeURIComponent(error.message)}`);
    }

    // Success case - no error
    const forwardedHost = request.headers.get("x-forwarded-host"); // original origin before load balancer
    const isLocalEnv = process.env.NODE_ENV === "development";

    if (isLocalEnv) {
      // For local development, use the NEXT_PUBLIC_SITE_URL from environment
      // This ensures we use http://localhost:3003 instead of https://localhost
      const localSiteUrl = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3003";
      console.log("Local redirect to:", `${localSiteUrl}${next}`);
      return NextResponse.redirect(`${localSiteUrl}${next}`);
    } else if (forwardedHost) {
      return NextResponse.redirect(`https://${forwardedHost}${next}`);
    } else {
      return NextResponse.redirect(`${origin}${next}`);
    }
  }

  // Redirect to the next URL or home page
  // Check if we're in development mode
  const isLocalEnv = process.env.NODE_ENV === "development";

  if (isLocalEnv) {
    // For local development, use the NEXT_PUBLIC_SITE_URL from environment
    const localSiteUrl = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3003";
    console.log("Local redirect (no code) to:", `${localSiteUrl}${next}`);
    return NextResponse.redirect(`${localSiteUrl}${next}`);
  } else {
    // In production, use the origin from the request
    return NextResponse.redirect(`${origin}${next}`);
  }
}
