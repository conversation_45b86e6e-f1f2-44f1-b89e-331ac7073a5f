"use client";

import { useState, useEffect } from "react";

// Element to color mapping - updated to match BatTuInfo.tsx
const ELEMENT_COLORS = {
  // Wood (Mộc): Green
  Giáp: "#4CAF50",
  Ất: "#4CAF50",

  // Fire (Hỏa): Red
  Bính: "#F44336",
  Đinh: "#F44336",

  // Earth (Thổ): Yellow/Brown
  Mậu: "#FFC107",
  Kỷ: "#FFC107",

  // Metal (Kim): White/Silver
  Canh: "#9E9E9E",
  Tân: "#9e9e9e",

  // Water (Thủy): Blue/Black
  Nhâm: "#2196F3",
  Quý: "#2196F3",
};

// Element-based glow colors for current period highlighting
const ELEMENT_GLOW_COLORS = {
  Mộc: "rgba(76, 175, 80, 0.4)", // Green glow
  Hỏa: "rgba(244, 67, 54, 0.4)", // Red glow
  Thổ: "rgba(255, 193, 7, 0.4)", // Amber glow
  Kim: "rgba(158, 158, 158, 0.4)", // Gray glow
  Thủy: "rgba(33, 150, 243, 0.4)", // Blue glow
};

// Element-based border colors for current period
const ELEMENT_BORDER_COLORS = {
  Mộc: "border-green-500 dark:border-green-400",
  Hỏa: "border-red-500 dark:border-red-400",
  Thổ: "border-amber-500 dark:border-amber-400",
  Kim: "border-gray-500 dark:border-gray-400",
  Thủy: "border-blue-500 dark:border-blue-400",
};

// Element-based background colors for current period
const ELEMENT_BG_COLORS = {
  Mộc: "bg-green-500/10 dark:bg-green-400/10",
  Hỏa: "bg-red-500/10 dark:bg-red-400/10",
  Thổ: "bg-amber-500/10 dark:bg-amber-400/10",
  Kim: "bg-gray-500/10 dark:bg-gray-400/10",
  Thủy: "bg-blue-500/10 dark:bg-blue-400/10",
};

// Map Chinese stems to their elements
const STEM_TO_ELEMENT = {
  Giáp: "Mộc",
  Ất: "Mộc",
  Bính: "Hỏa",
  Đinh: "Hỏa",
  Mậu: "Thổ",
  Kỷ: "Thổ",
  Canh: "Kim",
  Tân: "Kim",
  Nhâm: "Thủy",
  Quý: "Thủy",
};

// Map Chinese branches to their elements
const BRANCH_TO_ELEMENT = {
  Tý: "Thủy",
  Sửu: "Thổ",
  Dần: "Mộc",
  Mão: "Mộc",
  Thìn: "Thổ",
  Tỵ: "Hỏa",
  Ngọ: "Hỏa",
  Mùi: "Thổ",
  Thân: "Kim",
  Dậu: "Kim",
  Tuất: "Thổ",
  Hợi: "Thủy",
};

// Element grouping for legend (used for branch color lookup)
const ELEMENT_GROUPS = {
  Mộc: ["Giáp", "Ất"],
  Hỏa: ["Bính", "Đinh"],
  Thổ: ["Mậu", "Kỷ"],
  Kim: ["Canh", "Tân"],
  Thủy: ["Nhâm", "Quý"],
};

// Element to Tailwind gradient class mapping
const ELEMENT_GRADIENTS = {
  Mộc: "from-green-200 via-green-300 to-green-200 dark:from-green-900 dark:via-green-800 dark:to-green-900",
  Hỏa: "from-red-200 via-red-300 to-red-200 dark:from-red-900 dark:via-red-800 dark:to-red-900",
  Thổ: "from-amber-200 via-amber-300 to-amber-200 dark:from-amber-900 dark:via-amber-800 dark:to-amber-900",
  Kim: "from-slate-200 via-slate-300 to-slate-200 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900",
  Thủy: "from-blue-200 via-blue-300 to-blue-200 dark:from-blue-900 dark:via-blue-800 dark:to-blue-900",
};

interface DaiVanData {
  [year: string]: string;
}

interface DaiVanResponse {
  daiVan: DaiVanData;
  batTu?: any;
}

interface DaiVanGroup {
  startYear: number;
  endYear: number;
  stem: string;
  branch: string;
  isCurrentPeriod: boolean;
  element?: string; // Element for the stem
}

export function DaiVanInfo({ daiVanData, personName }: { daiVanData: DaiVanResponse | DaiVanData; personName?: string }) {
  // Create a stable reference to the data that won't change on re-renders
  // This is important for maintaining data consistency across renders
  const processedData = (() => {
    // Safely extract data from the response with fallbacks
    let actualDaiVanData: DaiVanData = {};

    try {
      // Handle case where data is null or undefined
      if (!daiVanData) {
        console.log("DaiVanInfo: No data provided");
        return { data: {}, valid: false };
      }

      // Handle case where data is an object
      if (typeof daiVanData === "object") {
        // Case 1: Data has a daiVan property (from tool call)
        if ("daiVan" in daiVanData && daiVanData.daiVan && typeof daiVanData.daiVan === "object") {
          actualDaiVanData = daiVanData.daiVan as DaiVanData;
          // console.log("DaiVanInfo: Using daiVan property");
        }
        // Case 2: Data is directly a DaiVanData object (from test page)
        else if (!("daiVan" in daiVanData)) {
          actualDaiVanData = daiVanData as DaiVanData;
          // console.log("DaiVanInfo: Using direct data");
        }
      }

      // Validate the data structure
      const isValid = Object.keys(actualDaiVanData).length > 0;
      return { data: actualDaiVanData, valid: isValid };
    } catch (error) {
      console.error("DaiVanInfo: Error processing data:", error);
      return { data: {}, valid: false };
    }
  })();

  const [screenSize, setScreenSize] = useState({
    isMobile: false,
    isSmallMobile: false,
  });

  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        isMobile: window.innerWidth < 768,
        isSmallMobile: window.innerWidth < 480,
      });
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Helper function to get element color for a stem or branch
  const getElementColor = (text: string) => {
    // First check if it's a stem
    if (text in STEM_TO_ELEMENT) {
      return ELEMENT_COLORS[text as keyof typeof ELEMENT_COLORS];
    }

    // Then check if it's a branch
    if (text in BRANCH_TO_ELEMENT) {
      const element = BRANCH_TO_ELEMENT[text as keyof typeof BRANCH_TO_ELEMENT];
      const stemInElement = ELEMENT_GROUPS[element as keyof typeof ELEMENT_GROUPS][0];
      return ELEMENT_COLORS[stemInElement as keyof typeof ELEMENT_COLORS];
    }

    return "#888888"; // Default color if not found
  };

  const currentYear = new Date().getFullYear();

  // Process years from the validated data
  const sortedYears = (() => {
    try {
      if (!processedData.valid) {
        // console.log("DaiVanInfo: No valid data to process");
        return [];
      }

      const years = Object.keys(processedData.data);
      // console.log("DaiVanInfo: Raw years from data:", years);

      return years
        .filter((year) => {
          const num = Number(year);
          const isValid = !Number.isNaN(num) && Number.isFinite(num);
          // if (!isValid) {
          // console.log("DaiVanInfo: Filtering out invalid year:", year);
          // }
          return isValid;
        })
        .map((year) => {
          const num = Number(year);
          return num;
        })
        .sort((a, b) => a - b);
    } catch (error) {
      console.error("DaiVanInfo: Error processing years:", error);
      return [];
    }
  })();

  // Process the years into groups
  const groupedDaiVan = (() => {
    const groups: DaiVanGroup[] = [];

    // Only process if we have at least 2 years (needed for a period)
    if (sortedYears.length < 2) {
      // console.log("DaiVanInfo: Not enough years to create groups");
      return groups;
    }

    try {
      // console.log("DaiVanInfo: Processing years into groups");

      for (let i = 0; i < sortedYears.length; i += 2) {
        if (i + 1 < sortedYears.length) {
          const startYear = sortedYears[i];
          // Calculate endYear as startYear + 9 as per requirement
          const endYear = startYear + 9;

          // Ensure we have valid years
          if (Number.isNaN(startYear) || Number.isNaN(endYear) || !Number.isFinite(startYear) || !Number.isFinite(endYear)) {
            // console.log(`DaiVanInfo: Skipping invalid year pair: ${startYear} - ${endYear}`);
            continue;
          }

          const stem = processedData.data[startYear.toString()] || "-";
          const branch = processedData.data[sortedYears[i + 1].toString()] || "-";
          const isCurrentPeriod = currentYear >= startYear && currentYear <= endYear;

          // Get element for the stem (for gradient styling)
          const element = stem in STEM_TO_ELEMENT ? STEM_TO_ELEMENT[stem as keyof typeof STEM_TO_ELEMENT] : undefined;

          groups.push({
            startYear,
            endYear,
            stem,
            branch,
            isCurrentPeriod,
            element,
          });
        }
      }

      return groups;
    } catch (error) {
      console.error("DaiVanInfo: Error creating groups:", error);
      return [];
    }
  })();

  // Find the current period's element for header styling
  const currentPeriod = groupedDaiVan.find((group) => group.isCurrentPeriod);
  const dominantElement = currentPeriod?.element || "Thổ";

  return (
    <div className="flex justify-center w-full">
      <style jsx>{`
        .card {
          --primary: ${dominantElement === "Thủy"
            ? "#2196F3"
            : dominantElement === "Hỏa"
            ? "#F44336"
            : dominantElement === "Mộc"
            ? "#4CAF50"
            : dominantElement === "Kim"
            ? "#9E9E9E"
            : "#FFC107"};
          --primary-hover: ${dominantElement === "Thủy"
            ? "#1976D2"
            : dominantElement === "Hỏa"
            ? "#D32F2F"
            : dominantElement === "Mộc"
            ? "#388E3C"
            : dominantElement === "Kim"
            ? "#757575"
            : "#F57C00"};
          --secondary: #458588;
          --secondary-hover: #83a598;
          --accent: #fabd2f;
          --text: #3c3836;
          --bg: #fbf1c7;
          --shadow-color: #282828;
          --pattern-color: #d5c4a1;

          position: relative;
          width: 100%;
          max-width: 450px;
          background: var(--bg);
          border: 0.35em solid var(--text);
          border-radius: 0.6em;
          box-shadow: 0.7em 0.7em 0 var(--shadow-color), inset 0 0 0 0.15em rgba(0, 0, 0, 0.05);
          transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
          overflow: hidden;
          font-family: ui-sans-serif, system-ui, sans-serif;
          transform-origin: center;
        }

        .dark .card {
          --primary: ${dominantElement === "Thủy"
            ? "#2196F3"
            : dominantElement === "Hỏa"
            ? "#F44336"
            : dominantElement === "Mộc"
            ? "#4CAF50"
            : dominantElement === "Kim"
            ? "#9E9E9E"
            : "#FFC107"};
          --primary-hover: ${dominantElement === "Thủy"
            ? "#1976D2"
            : dominantElement === "Hỏa"
            ? "#D32F2F"
            : dominantElement === "Mộc"
            ? "#388E3C"
            : dominantElement === "Kim"
            ? "#757575"
            : "#F57C00"};
          --secondary: #83a598;
          --secondary-hover: #458588;
          --accent: #fabd2f;
          --text: #fbf1c7;
          --bg: #3c3836;
          --shadow-color: #1d2021;
          --pattern-color: #504945;
        }

        .card:hover {
          transform: translate(-0.4em, -0.4em) scale(1.02);
          box-shadow: 1em 1em 0 var(--shadow-color);
        }

        .card:hover .card-pattern-grid,
        .card:hover .card-overlay-dots {
          opacity: 1;
        }

        .card::before {
          content: "";
          position: absolute;
          top: -1em;
          right: -1em;
          width: 4em;
          height: 4em;
          background: var(--accent);
          transform: rotate(45deg);
          z-index: 1;
        }

        .card::after {
          content: "運";
          position: absolute;
          top: 0.4em;
          right: 0.4em;
          color: var(--text);
          font-size: 1.2em;
          font-weight: bold;
          z-index: 2;
        }

        .card-pattern-grid {
          position: absolute;
          inset: 0;
          background-image: linear-gradient(to right, rgba(0, 0, 0, 0.05) 1px, transparent 1px),
            linear-gradient(to bottom, rgba(0, 0, 0, 0.05) 1px, transparent 1px);
          background-size: 0.5em 0.5em;
          pointer-events: none;
          opacity: 0.5;
          transition: opacity 0.4s ease;
          z-index: 1;
        }

        .card-overlay-dots {
          position: absolute;
          inset: 0;
          background-image: radial-gradient(var(--pattern-color) 1px, transparent 1px);
          background-size: 1em 1em;
          background-position: -0.5em -0.5em;
          pointer-events: none;
          opacity: 0;
          transition: opacity 0.4s ease;
          z-index: 1;
        }

        .card-title-area {
          position: relative;
          padding: 1.4em;
          background: var(--primary);
          color: var(--bg);
          font-weight: 800;
          font-size: 1.2em;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 0.35em solid var(--text);
          text-transform: uppercase;
          letter-spacing: 0.05em;
          z-index: 2;
          overflow: hidden;
        }

        .card-title-area::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: repeating-linear-gradient(45deg, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1) 0.5em, transparent 0.5em, transparent 1em);
          pointer-events: none;
          opacity: 0.3;
        }

        .card-body {
          position: relative;
          padding: 1.5em;
          z-index: 2;
        }

        .dai-van-grid {
          display: grid;
          gap: 1em;
          margin: 1.5em 0;
        }

        @media (max-width: 480px) {
          .dai-van-grid {
            grid-template-columns: 1fr;
            gap: 0.8em;
          }
        }

        .dai-van-item {
          background: rgba(255, 255, 255, 0.3);
          border: 0.15em solid var(--text);
          border-radius: 0.5em;
          padding: 1em;
          text-align: center;
          transition: all 0.3s ease;
          box-shadow: 0.2em 0.2em 0 rgba(0, 0, 0, 0.1);
          position: relative;
          overflow: hidden;
        }

        .dai-van-item:hover {
          transform: translateY(-0.2em);
          box-shadow: 0.3em 0.3em 0 rgba(0, 0, 0, 0.2);
        }

        .current-dai-van {
          transform: scale(1.03);
          box-shadow: 0.3em 0.3em 0 rgba(0, 0, 0, 0.2);
        }

        .current-dai-van:hover {
          transform: scale(1.03) translateY(-0.2em);
          box-shadow: 0.4em 0.4em 0 rgba(0, 0, 0, 0.3);
        }

        .dai-van-period-years {
          font-size: 0.8em;
          font-weight: 600;
          color: var(--text);
          opacity: 0.8;
          margin-bottom: 0.5em;
          text-transform: uppercase;
          letter-spacing: 0.05em;
          background: rgba(0, 0, 0, 0.05);
          padding: 0.3em 0.6em;
          border-radius: 0.3em;
        }

        .dai-van-period-stem-top {
          font-size: 1.4em;
          font-weight: 700;
          margin-bottom: 0.3em;
        }

        .dai-van-period-combined {
          font-size: 1.1em;
          font-weight: 600;
        }

        .no-data-message {
          text-align: center;
          padding: 2em;
          background: rgba(255, 255, 255, 0.3);
          border: 0.15em solid var(--text);
          border-radius: 0.5em;
          color: var(--text);
          opacity: 0.7;
          margin: 1.5em 0;
        }

        .card-footer {
          text-align: center;
          padding: 1em;
          border-top: 0.15em dashed rgba(0, 0, 0, 0.2);
          margin-top: 1.5em;
          position: relative;
          z-index: 2;
        }

        .card-footer::before {
          content: "✂";
          position: absolute;
          top: -0.8em;
          left: 50%;
          transform: translateX(-50%) rotate(90deg);
          background: var(--bg);
          padding: 0 0.5em;
          font-size: 1em;
          color: var(--text);
          opacity: 0.4;
        }

        .footer-text {
          font-size: 0.7em;
          color: var(--text);
          opacity: 0.7;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      `}</style>

      <div className="card">
        <div className="card-pattern-grid" />
        <div className="card-overlay-dots" />

        {/* Card Header */}
        <div className="card-title-area">
          <span style={{ fontSize: screenSize.isSmallMobile ? "0.9em" : "1em" }}>{personName ? `${personName} - ĐẠI VẬN` : "ĐẠI VẬN"}</span>
        </div>

        <div className="card-body">
          {/* No data message */}
          {groupedDaiVan.length === 0 ? (
            <div className="no-data-message">
              <p>Không có dữ liệu Đại Vận</p>
            </div>
          ) : (
            <>
              {/* Đại Vận periods grid */}
              <div className={`dai-van-grid ${screenSize.isSmallMobile ? "grid-cols-1" : "grid-cols-2"}`}>
                {groupedDaiVan.map((group) => {
                  // Get element for styling
                  const element = group.element || "Thổ";
                  const glowColor = ELEMENT_GLOW_COLORS[element as keyof typeof ELEMENT_GLOW_COLORS];

                  return (
                    <div
                      key={group.startYear}
                      className={`dai-van-item ${group.isCurrentPeriod ? "current-dai-van" : ""}`}
                      style={
                        group.isCurrentPeriod
                          ? {
                              boxShadow: `0 0 20px ${glowColor}, 0 0 40px ${glowColor}80, 0.3em 0.3em 0 rgba(0,0,0,0.2)`,
                              borderColor: getElementColor(group.stem),
                              backgroundColor: `${glowColor}20`,
                            }
                          : undefined
                      }
                    >
                      {/* Year range display */}
                      <div
                        className="dai-van-period-years"
                        style={
                          group.isCurrentPeriod
                            ? {
                                color: getElementColor(group.stem),
                                fontWeight: "700",
                              }
                            : undefined
                        }
                      >
                        {group.startYear} - {group.endYear}
                      </div>
                      {/* Combined stem-branch display */}
                      <div className="dai-van-period-combined">
                        <span style={{ color: getElementColor(group.stem) }}>{group.stem}</span>{" "}
                        <span style={{ color: getElementColor(group.branch) }}>{group.branch}</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </>
          )}

          {/* Card Footer */}
          <div className="card-footer">
            <p className="footer-text">Bát Tự Destiny Card - Đại Vận</p>
          </div>
        </div>
      </div>
    </div>
  );
}
