/**
 * Admin role checking utilities for server-side operations
 * Provides secure admin validation and status checking
 */

import { isEmailAdmin, isUserAdmin } from "./admin-config";
import type { SupabaseUser } from "@/app/(auth)/auth-server";

/**
 * Server-side admin status checking result
 */
export interface AdminStatusResult {
  isAdmin: boolean;
  email: string | null;
  error?: string;
}

/**
 * Check if an email address has admin privileges
 * Server-side utility function for admin validation
 * 
 * @param email - Email address to check
 * @returns boolean indicating admin status
 */
export function isAdmin(email: string | null | undefined): boolean {
  try {
    return isEmailAdmin(email);
  } catch (error) {
    console.error("Error checking admin status for email:", email, error);
    return false;
  }
}

/**
 * Get admin status for a user object
 * Handles various user object formats and provides detailed result
 * 
 * @param user - User object with email property
 * @returns AdminStatusResult with detailed admin status information
 */
export function getAdminStatus(
  user: { email?: string | null } | null | undefined
): AdminStatusResult {
  if (!user) {
    return {
      isAdmin: false,
      email: null,
      error: "No user provided",
    };
  }

  const email = user.email;
  
  if (!email) {
    return {
      isAdmin: false,
      email: null,
      error: "User has no email address",
    };
  }

  try {
    const isAdminUser = isEmailAdmin(email);
    
    return {
      isAdmin: isAdminUser,
      email: email,
    };
  } catch (error) {
    console.error("Error getting admin status for user:", user, error);
    return {
      isAdmin: false,
      email: email,
      error: error instanceof Error ? error.message : "Unknown error",
    };
  }
}

/**
 * Check if a SupabaseUser has admin privileges
 * Type-safe admin checking for Supabase user objects
 * 
 * @param user - SupabaseUser object
 * @returns boolean indicating admin status
 */
export function isSupabaseUserAdmin(user: SupabaseUser | null | undefined): boolean {
  if (!user) {
    return false;
  }
  
  return isUserAdmin(user);
}

/**
 * Validate admin access and throw error if not admin
 * Utility for protecting admin-only operations
 * 
 * @param user - User object to validate
 * @param operation - Description of the operation being attempted (for error messages)
 * @throws Error if user is not an admin
 */
export function requireAdmin(
  user: { email?: string | null } | null | undefined,
  operation = "admin operation"
): void {
  const adminStatus = getAdminStatus(user);
  
  if (!adminStatus.isAdmin) {
    const errorMessage = adminStatus.error 
      ? `Access denied for ${operation}: ${adminStatus.error}`
      : `Access denied for ${operation}: User is not an administrator`;
    
    throw new Error(errorMessage);
  }
}

/**
 * Safe admin check that never throws
 * Returns false for any error condition
 * 
 * @param user - User object to check
 * @returns boolean indicating admin status (false on any error)
 */
export function safeIsAdmin(user: { email?: string | null } | null | undefined): boolean {
  try {
    return getAdminStatus(user).isAdmin;
  } catch (error) {
    console.error("Safe admin check failed:", error);
    return false;
  }
}

/**
 * Get admin status with caching for performance
 * Simple in-memory cache for admin status checks
 */
class AdminStatusCache {
  private cache = new Map<string, { isAdmin: boolean; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get admin status with caching
   * @param email - Email to check
   * @returns boolean indicating admin status
   */
  getAdminStatus(email: string | null | undefined): boolean {
    if (!email) {
      return false;
    }

    const cached = this.cache.get(email);
    const now = Date.now();

    // Return cached result if still valid
    if (cached && (now - cached.timestamp) < this.CACHE_TTL) {
      return cached.isAdmin;
    }

    // Calculate fresh result
    const isAdminResult = isEmailAdmin(email);
    
    // Cache the result
    this.cache.set(email, {
      isAdmin: isAdminResult,
      timestamp: now,
    });

    // Clean up old cache entries periodically
    if (this.cache.size > 100) {
      this.cleanupCache();
    }

    return isAdminResult;
  }

  /**
   * Clear cache for a specific email
   * @param email - Email to clear from cache
   */
  clearCache(email?: string): void {
    if (email) {
      this.cache.delete(email);
    } else {
      this.cache.clear();
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupCache(): void {
    const now = Date.now();
    for (const [email, entry] of this.cache.entries()) {
      if ((now - entry.timestamp) >= this.CACHE_TTL) {
        this.cache.delete(email);
      }
    }
  }
}

/**
 * Singleton admin status cache instance
 */
export const adminStatusCache = new AdminStatusCache();

/**
 * Cached admin status check
 * Uses in-memory cache for better performance
 * 
 * @param email - Email address to check
 * @returns boolean indicating admin status
 */
export function isAdminCached(email: string | null | undefined): boolean {
  return adminStatusCache.getAdminStatus(email);
}
