-- Test script to verify add_credits RPC function works correctly
-- Run this in your Supabase SQL editor or psql

-- First, check if the add_credits function exists
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_name = 'add_credits' 
AND routine_schema = 'public';

-- Check current user credits (replace with actual user ID)
-- SELECT id, email, credits FROM "User" WHERE email = '<EMAIL>';

-- Test the add_credits function (replace with actual user ID)
-- SELECT add_credits('your-user-id-here', 50, 'Test credit addition');

-- Verify the credit was added
-- SELECT id, email, credits FROM "User" WHERE email = '<EMAIL>';

-- Check the credit transaction was recorded
-- SELECT * FROM credit_transactions WHERE user_id = 'your-user-id-here' ORDER BY created_at DESC LIMIT 5;

-- Check if the function definition looks correct
SELECT pg_get_functiondef(oid) 
FROM pg_proc 
WHERE proname = 'add_credits' 
AND pronamespace = (SELECT oid FROM pg_namespace WHERE nspname = 'public');
