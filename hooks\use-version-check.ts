import { useEffect, useState, useCallback } from "react";
import { checkForUpdate, forceAppUpdate, VERSION_CHECK_INTERVAL, type VersionInfo } from "@/lib/version";

interface UseVersionCheckReturn {
  isUpdateAvailable: boolean;
  versionInfo: VersionInfo | null;
  isChecking: boolean;
  checkNow: () => Promise<void>;
  updateNow: () => void;
}

export function useVersionCheck(autoCheck = true): UseVersionCheckReturn {
  const [isUpdateAvailable, setIsUpdateAvailable] = useState(false);
  const [versionInfo, setVersionInfo] = useState<VersionInfo | null>(null);
  const [isChecking, setIsChecking] = useState(false);

  const checkNow = useCallback(async () => {
    setIsChecking(true);
    try {
      const info = await checkForUpdate();
      if (info) {
        setVersionInfo(info);
        setIsUpdateAvailable(!!info.forceReload);
      }
    } catch (error) {
      console.error("Version check failed:", error);
    } finally {
      setIsChecking(false);
    }
  }, []);

  const updateNow = useCallback(() => {
    forceAppUpdate();
  }, []);

  useEffect(() => {
    if (!autoCheck) return;

    // Check immediately on mount
    checkNow();

    // Set up interval for periodic checks
    const interval = setInterval(checkNow, VERSION_CHECK_INTERVAL);

    return () => clearInterval(interval);
  }, [autoCheck, checkNow]);

  // Listen for focus events to check for updates when user returns
  useEffect(() => {
    if (!autoCheck) return;

    const handleFocus = () => {
      checkNow();
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [autoCheck, checkNow]);

  return {
    isUpdateAvailable,
    versionInfo,
    isChecking,
    checkNow,
    updateNow,
  };
}
