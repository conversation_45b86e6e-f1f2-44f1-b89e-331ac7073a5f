/**
 * Utility for tracking timing information for analytics
 */

// Store start times for tool calls
const toolCallStartTimes = new Map<string, number>();

/**
 * Generate a unique key for a tool call
 * @param chatId The chat ID
 * @param toolName The tool name
 * @returns A unique key for the tool call
 */
export function getToolCallKey(chatId: string, toolName: string): string {
  return `${chatId}:${toolName}:${Date.now()}`;
}

/**
 * Start timing a tool call
 * @param key The unique key for the tool call
 * @returns The key that was used
 */
export function startToolCallTiming(key: string): string {
  toolCallStartTimes.set(key, Date.now());
  return key;
}

/**
 * End timing a tool call and get the duration
 * @param key The unique key for the tool call
 * @returns The duration in milliseconds, or 0 if the key was not found
 */
export function endToolCallTiming(key: string): number {
  const startTime = toolCallStartTimes.get(key);
  
  if (!startTime) {
    return 0;
  }
  
  const duration = Date.now() - startTime;
  
  // Clean up
  toolCallStartTimes.delete(key);
  
  return duration;
}
