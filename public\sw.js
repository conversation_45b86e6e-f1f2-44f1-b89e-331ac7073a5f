// This is the service worker for the PWA

const CACHE_NAME = "bat-tu-master-v-cache-v5";
const urlsToCache = ["/", "/images/avatar.png", "/images/icon-192x192.png", "/images/icon-512x512.png", "/manifest.webmanifest"];

// Helper function to send error messages to the client
const sendErrorMessage = (message) => {
  self.clients.matchAll().then((clients) => {
    clients.forEach((client) => {
      client.postMessage({
        type: "ERROR",
        message: message,
      });
    });
  });
};

// Global error handler
self.addEventListener("error", (event) => {
  console.error("Service Worker error:", event.message);
  sendErrorMessage(`Service Worker error: ${event.message}`);
});

// Unhandled promise rejection handler
self.addEventListener("unhandledrejection", (event) => {
  console.error("Unhandled promise rejection:", event.reason);
  sendErrorMessage(`Unhandled promise rejection: ${event.reason}`);
});

// Install event - cache assets
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches
      .open(CACHE_NAME)
      .then((cache) => {
        console.log("Opened cache");

        // Use a more resilient caching approach - cache each resource individually
        // so a single failure doesn't prevent the service worker from installing
        const cachePromises = urlsToCache.map((url) => {
          // Attempt to cache each URL individually and catch errors
          return cache.add(url).catch((error) => {
            console.error(`Failed to cache ${url}:`, error);
            // Don't fail the whole installation if one resource fails
            return Promise.resolve();
          });
        });

        return Promise.all(cachePromises);
      })
      .catch((error) => {
        console.error("Service worker installation failed:", error);
        // Allow the service worker to install even if caching fails
        return Promise.resolve();
      })
  );
  // Activate the new service worker immediately
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
  const cacheWhitelist = [CACHE_NAME];
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  // Claim clients so the SW is in control immediately
  self.clients.claim();
});

// Fetch event - serve from cache, fallback to network
self.addEventListener("fetch", (event) => {
  // Only handle GET requests with http/https schemes
  if (event.request.method !== "GET" || !event.request.url.startsWith("http") || event.request.url.startsWith("chrome-extension:")) {
    return; // Let the browser handle it
  }

  try {
    const url = new URL(event.request.url);

    // Skip handling requests to external domains
    const externalDomains = ["cdn.jsdelivr.net", "static.cloudflareinsights.com", "supabase.co", "posthog.com"];

    if (externalDomains.some((domain) => url.hostname.includes(domain))) {
      return; // Let the browser handle external requests directly
    }

    // Handle navigation requests (page loads)
    if (event.request.mode === "navigate") {
      event.respondWith(
        fetch(event.request).catch(() => {
          // If network fetch fails, try to return the cached homepage
          return caches.match("/").catch((err) => {
            console.error("Failed to fetch from cache:", err);
            // If all else fails, return a simple offline page
            return new Response("You are offline. Please check your connection.", {
              status: 503,
              headers: { "Content-Type": "text/plain" },
            });
          });
        })
      );
      return;
    }

    // For non-navigation requests (assets, API calls, etc.)
    event.respondWith(
      // Try cache first
      caches
        .match(event.request)
        .then((cachedResponse) => {
          if (cachedResponse) {
            return cachedResponse; // Return cached response if available
          }

          // Otherwise fetch from network
          return fetch(event.request)
            .then((networkResponse) => {
              // Don't cache if not a successful response or not a basic type
              if (!networkResponse || networkResponse.status !== 200 || networkResponse.type !== "basic") {
                return networkResponse;
              }

              // Clone the response before using it
              const responseToCache = networkResponse.clone();

              // Don't cache API requests or other dynamic content
              if (!event.request.url.includes("/api/")) {
                // Cache the response asynchronously (don't block the response)
                caches
                  .open(CACHE_NAME)
                  .then((cache) => {
                    try {
                      cache.put(event.request, responseToCache);
                    } catch (error) {
                      console.error("Failed to cache response:", error);
                    }
                  })
                  .catch((error) => {
                    console.error("Failed to open cache:", error);
                  });
              }

              return networkResponse;
            })
            .catch((error) => {
              console.error("Network fetch failed:", error);
              // For image requests, try to return a fallback image
              if (event.request.destination === "image") {
                return caches.match("/images/avatar.png");
              }
              throw error; // Re-throw for other resource types
            });
        })
        .catch((error) => {
          console.error("Cache match failed:", error);
          return fetch(event.request); // Last resort: try network again
        })
    );
  } catch (error) {
    console.error("Service worker fetch handler error:", error);
    // Don't prevent the request from being handled by the browser
    return;
  }
});
