/**
 * Admin Users API Route
 * Provides user list and search functionality for admin dashboard
 */

import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getAdminService } from "@/lib/services/admin-service";
import { ChatSDKError } from "@/lib/errors";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    // Parse search parameters
    const search = searchParams.get("search") || undefined;
    const minCreditsParam = searchParams.get("minCredits");
    const maxCreditsParam = searchParams.get("maxCredits");
    const minCredits = minCreditsParam ? Number.parseInt(minCreditsParam, 10) : undefined;
    const maxCredits = maxCreditsParam ? Number.parseInt(maxCreditsParam, 10) : undefined;
    const page = Number.parseInt(searchParams.get("page") || "1", 10);
    const limit = Number.parseInt(searchParams.get("limit") || "20", 10);

    // Validate parameters
    if (page < 1) {
      return NextResponse.json({ error: "Page must be greater than 0" }, { status: 400 });
    }

    if (limit < 1 || limit > 100) {
      return NextResponse.json({ error: "Limit must be between 1 and 100" }, { status: 400 });
    }

    if (minCredits !== undefined && minCredits < 0) {
      return NextResponse.json({ error: "Min credits must be non-negative" }, { status: 400 });
    }

    if (maxCredits !== undefined && maxCredits < 0) {
      return NextResponse.json({ error: "Max credits must be non-negative" }, { status: 400 });
    }

    if (minCredits !== undefined && maxCredits !== undefined && minCredits > maxCredits) {
      return NextResponse.json({ error: "Min credits cannot be greater than max credits" }, { status: 400 });
    }

    // Get admin service and fetch users
    const adminService = await getAdminService();
    const result = await adminService.getUserList({
      search,
      minCredits,
      maxCredits,
      page,
      limit,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Admin users API error:", error);

    if (error instanceof ChatSDKError) {
      if (error.type === "unauthorized") {
        return NextResponse.json({ error: "Unauthorized access" }, { status: 401 });
      }

      if (error.type === "bad_request") {
        return NextResponse.json({ error: "Database error" }, { status: 500 });
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
