@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 157, 13, 17;
  --background-start-rgb: 253, 237, 207;
  --background-end-rgb: 226, 194, 134;
  --font-segoe: "Segoe UI", "Segoe UI Variable", "ui-sans-serif", "system-ui", "-apple-system", "BlinkMacSystemFont", "sans-serif";
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 253, 237, 207;
    --background-start-rgb: 157, 13, 17;
    --background-end-rgb: 157, 13, 17;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

@layer base {
  :root {
    --background: 42 94% 90%;
    --foreground: 358 84% 33%;
    --card: 42 94% 90%;
    --card-foreground: 358 84% 33%;
    --popover: 42 94% 90%;
    --popover-foreground: 358 84% 33%;
    --primary: 358 84% 33%;
    --primary-foreground: 42 94% 90%;
    --secondary: 39 65% 70%;
    --secondary-foreground: 358 84% 33%;
    --muted: 39 65% 70%;
    --muted-foreground: 358 84% 33%;
    --accent: 17 43% 57%;
    --accent-foreground: 42 94% 90%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 30 75% 44%;
    --input: 39 65% 70%;
    --ring: 358 84% 33%;
    --chart-1: 358 84% 33%;
    --chart-2: 17 43% 57%;
    --chart-3: 30 75% 44%;
    --chart-4: 42 94% 90%;
    --chart-5: 39 65% 70%;
    --radius: 0.5rem;
    --sidebar-background: 39 65% 70%;
    --sidebar-foreground: 358 84% 33%;
    --sidebar-primary: 358 84% 33%;
    --sidebar-primary-foreground: 42 94% 90%;
    --sidebar-accent: 17 43% 57%;
    --sidebar-accent-foreground: 42 94% 90%;
    --sidebar-border: 30 75% 44%;
    --sidebar-ring: 358 84% 33%;
  }
  .dark {
    --background: 358 84% 23%;
    --foreground: 42 94% 90%;
    --card: 358 84% 23%;
    --card-foreground: 42 94% 90%;
    --popover: 358 84% 23%;
    --popover-foreground: 42 94% 90%;
    --primary: 42 94% 90%;
    --primary-foreground: 358 84% 33%;
    --secondary: 17 43% 47%;
    --secondary-foreground: 42 94% 90%;
    --muted: 17 43% 47%;
    --muted-foreground: 42 94% 80%;
    --accent: 30 75% 44%;
    --accent-foreground: 42 94% 90%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 17 43% 47%;
    --input: 17 43% 47%;
    --ring: 42 94% 80%;
    --chart-1: 358 84% 43%;
    --chart-2: 17 43% 67%;
    --chart-3: 30 75% 54%;
    --chart-4: 42 94% 90%;
    --chart-5: 39 65% 80%;
    --sidebar-background: 358 84% 23%;
    --sidebar-foreground: 42 94% 90%;
    --sidebar-primary: 30 75% 44%;
    --sidebar-primary-foreground: 42 94% 90%;
    --sidebar-accent: 17 43% 47%;
    --sidebar-accent-foreground: 42 94% 90%;
    --sidebar-border: 17 43% 47%;
    --sidebar-ring: 30 75% 44%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* PWA fullscreen safe area styles - simplified for top only */
/* Base safe area class that preserves existing padding */
.safe-area-top {
  padding-top: max(env(safe-area-inset-top), 0.2rem); /* Ensure minimum padding of 0.5rem (p-2) */
}

/* For elements with py-1.5 padding like the chat header */
header.safe-area-top {
  padding-top: max(env(safe-area-inset-top), 0.375rem); /* Ensure minimum padding of 0.375rem (py-1.5) */
}

.skeleton {
  * {
    pointer-events: none !important;
  }

  *[class^="text-"] {
    color: transparent;
    @apply rounded-md bg-foreground/20 select-none animate-pulse;
  }

  .skeleton-bg {
    @apply bg-foreground/10;
  }

  .skeleton-div {
    @apply bg-foreground/20 animate-pulse;
  }
}

.ProseMirror {
  outline: none;
}

.cm-editor,
.cm-gutters {
  @apply bg-background dark:bg-red-950 outline-none selection:bg-amber-200 !important;
}

.ͼo.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground,
.ͼo.cm-selectionBackground,
.ͼo.cm-content::selection {
  @apply bg-amber-200 dark:bg-amber-900 !important;
}

.cm-activeLine,
.cm-activeLineGutter {
  @apply bg-transparent !important;
}

.cm-activeLine {
  @apply rounded-r-sm !important;
}

.cm-lineNumbers {
  @apply min-w-7;
}

.cm-foldGutter {
  @apply min-w-3;
}

.cm-lineNumbers .cm-activeLineGutter {
  @apply rounded-l-sm !important;
}

.suggestion-highlight {
  @apply bg-amber-200 hover:bg-amber-300 dark:hover:bg-amber-400/50 dark:text-amber-50 dark:bg-amber-500/40;
}

/* Chat text styling with Segoe UI font family */
.chat-text {
  font-family: var(--font-segoe);
}

.message-content {
  font-family: var(--font-segoe);
}

/* Apply Segoe UI to all message bubbles and chat text elements */
[data-testid^="message-"] .text-sm,
[data-testid^="message-"] p,
[data-testid="message-content"],
[data-testid="multimodal-input"],
.chat-input {
  font-family: var(--font-segoe);
}

/* Title styling with Pacifico font */
.app-title {
  font-family: var(--font-pacifico);
  font-size: 4.5rem;
  font-weight: 400;
  line-height: 1.2;
}
