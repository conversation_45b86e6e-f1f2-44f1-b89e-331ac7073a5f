// Simple script to check environment variables
require('dotenv').config({ path: '.env.local' });

console.log('Checking environment variables:');
console.log('NODE_ENV:', process.env.NODE_ENV || 'Not set');
console.log('NEXT_PUBLIC_POSTHOG_KEY:', process.env.NEXT_PUBLIC_POSTHOG_KEY ? 'Set (value hidden)' : 'Not set');
console.log('NEXT_PUBLIC_POSTHOG_HOST:', process.env.NEXT_PUBLIC_POSTHOG_HOST || 'Not set');
