import { google } from "@ai-sdk/google";
import { openai } from "@ai-sdk/openai";
import { wrapLanguageModel, extractReasoningMiddleware } from "ai";
import type { LanguageModelV1 } from "ai";

// Model configuration types
export interface ModelConfig {
  primary: LanguageModelV1;
  fallback: LanguageModelV1;
  name: string;
}

// Error types that should trigger fallback
const GEMINI_FALLBACK_ERRORS = [
  "thought",
  "thinking",
  "type validation",
  "streaming",
  "unsupported response format",
  "invalid response structure",
  "error chunks",
  "stream error",
  "consecutive errors",
];

/**
 * Checks if an error chunk should be filtered out (thought-related validation errors)
 */
export function shouldFilterErrorChunk(errorChunk: any): boolean {
  if (!errorChunk || errorChunk.type !== "error") return false;

  const error = errorChunk.error;
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || "";
  const errorName = error.name?.toLowerCase() || "";
  const errorString = error.toString?.()?.toLowerCase() || "";

  // Filter AI_TypeValidationError related to thought properties
  const isTypeValidationError =
    errorName.includes("ai_typevalidationerror") || errorName.includes("typevalidationerror") || errorMessage.includes("validation");

  const isThoughtRelated =
    errorMessage.includes("thought") ||
    errorMessage.includes("thoughtsignature") ||
    errorMessage.includes("thinking") ||
    errorString.includes("thought") ||
    errorString.includes("thinking");

  // Filter if it's a type validation error related to thought properties
  const shouldFilter = isTypeValidationError && isThoughtRelated;

  if (shouldFilter) {
    console.log(`🧹 [Error Filter] Filtering thought-related validation error:`, {
      name: error.name,
      message: error.message?.slice(0, 100) + "...",
      isTypeValidation: isTypeValidationError,
      isThoughtRelated: isThoughtRelated,
    });
  }

  return shouldFilter;
}

/**
 * Checks if an error should trigger a fallback to OpenAI
 * Excludes thought-related validation errors since Gemini recovers from these
 */
export function shouldFallbackToOpenAI(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || "";
  const errorString = error.toString?.()?.toLowerCase() || "";
  const errorName = error.name?.toLowerCase() || "";

  // Don't fallback for thought-related validation errors
  const isThoughtValidationError =
    (errorName.includes("typevalidationerror") || errorMessage.includes("validation")) &&
    (errorMessage.includes("thought") || errorMessage.includes("thinking"));

  if (isThoughtValidationError) {
    console.log(`🔄 [Fallback Skip] Skipping fallback for thought validation error - Gemini will recover`);
    return false;
  }

  return GEMINI_FALLBACK_ERRORS.some((keyword) => errorMessage.includes(keyword) || errorString.includes(keyword));
}

/**
 * Creates Gemini models with proper thinking configuration
 */
export function createGeminiModels() {
  const geminiChatModel = google("gemini-2.5-flash-preview-04-17", {
    // Enable structured outputs for better compatibility
    structuredOutputs: true,
    // Configure safety settings to be less restrictive
    safetySettings: [
      { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_ONLY_HIGH" },
      { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_ONLY_HIGH" },
      { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_ONLY_HIGH" },
      { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_ONLY_HIGH" },
    ],
  });

  const geminiReasoningModel = wrapLanguageModel({
    model: google("gemini-2.5-flash-preview-04-17", {
      structuredOutputs: true,
      safetySettings: [
        { category: "HARM_CATEGORY_HATE_SPEECH", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_DANGEROUS_CONTENT", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_HARASSMENT", threshold: "BLOCK_ONLY_HIGH" },
        { category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold: "BLOCK_ONLY_HIGH" },
      ],
    }),
    middleware: extractReasoningMiddleware({ tagName: "think" }),
  });

  return {
    chat: geminiChatModel,
    reasoning: geminiReasoningModel,
    title: geminiChatModel,
    artifact: geminiChatModel,
  };
}

/**
 * Creates OpenAI models as fallback
 */
export function createOpenAIModels() {
  const openaiChatModel = openai("gpt-4o-mini");

  const openaiReasoningModel = wrapLanguageModel({
    model: openai("gpt-4o-mini"),
    middleware: extractReasoningMiddleware({ tagName: "think" }),
  });

  return {
    chat: openaiChatModel,
    reasoning: openaiReasoningModel,
    title: openaiChatModel,
    artifact: openaiChatModel,
  };
}

/**
 * Creates model configurations with primary (Gemini) and fallback (OpenAI) models
 */
export function createModelConfigs(): Record<string, ModelConfig> {
  const geminiModels = createGeminiModels();
  const openaiModels = createOpenAIModels();

  return {
    "chat-model": {
      primary: geminiModels.chat,
      fallback: openaiModels.chat,
      name: "chat-model",
    },
    "chat-model-reasoning": {
      primary: geminiModels.reasoning,
      fallback: openaiModels.reasoning,
      name: "chat-model-reasoning",
    },
    "title-model": {
      primary: geminiModels.title,
      fallback: openaiModels.title,
      name: "title-model",
    },
    "artifact-model": {
      primary: geminiModels.artifact,
      fallback: openaiModels.artifact,
      name: "artifact-model",
    },
  };
}

/**
 * Wrapper that provides automatic fallback from Gemini to OpenAI
 */
export class FallbackLanguageModel implements LanguageModelV1 {
  constructor(private config: ModelConfig, private onFallback?: (modelName: string, error: any) => void) {}

  get specificationVersion() {
    return this.config.primary.specificationVersion;
  }

  get defaultObjectGenerationMode() {
    return this.config.primary.defaultObjectGenerationMode;
  }

  get modelId() {
    return this.config.primary.modelId;
  }

  get provider() {
    return this.config.primary.provider;
  }

  get maxTokensInContext() {
    return (this.config.primary as any).maxTokensInContext;
  }

  get maxTokensPerGeneration() {
    return (this.config.primary as any).maxTokensPerGeneration;
  }

  get supportsImageInput() {
    return (this.config.primary as any).supportsImageInput;
  }

  get supportsUrl() {
    return (this.config.primary as any).supportsUrl;
  }

  async doGenerate(options: any) {
    const startTime = Date.now();
    try {
      console.log(`✨ [Gemini] Using ${this.config.name} with Gemini 2.5 Flash Preview`);

      const result = await this.config.primary.doGenerate(options);
      const duration = Date.now() - startTime;

      // Log completion with basic stats
      console.log(`📊 [Gemini Response] Generation completed in ${duration}ms`);

      // Check for thought properties in the response (keep for monitoring thinking capabilities)
      if (result.rawCall) {
        const rawResponse = (result.rawCall as any).rawResponse || (result as any).rawResponse;
        if (rawResponse && (rawResponse.thought || rawResponse.thinking)) {
          console.log(`🧠 [Gemini Thought Found] Thought properties detected in generation response`);
        }
        if ((result as any).thought || (result as any).thinking) {
          console.log(`🧠 [Gemini Thought in Result] Thought properties found in main result`);
        }
      }

      console.log(`✅ [Gemini] ${this.config.name} generation completed successfully`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorInfo =
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
              stack: error.stack?.split("\n").slice(0, 3),
              cause: (error as any).cause,
            }
          : { error: String(error) };

      console.error(`❌ [Gemini Error] Generation failed after ${duration}ms:`, errorInfo);

      if (shouldFallbackToOpenAI(error)) {
        console.warn(`🔄 [Fallback] ${this.config.name} falling back to OpenAI due to error:`, error);
        this.onFallback?.(this.config.name, error);
        const fallbackStartTime = Date.now();
        const result = await this.config.fallback.doGenerate(options);
        const fallbackDuration = Date.now() - fallbackStartTime;
        console.log(`✅ [OpenAI] ${this.config.name} generation completed with fallback in ${fallbackDuration}ms`);
        return result;
      }
      throw error;
    }
  }

  async doStream(options: any) {
    const startTime = Date.now();
    try {
      console.log(`✨ [Gemini] Streaming ${this.config.name} with Gemini 2.5 Flash Preview`);

      const result = await this.config.primary.doStream(options);
      const duration = Date.now() - startTime;

      console.log(`📊 [Gemini Stream] Stream created in ${duration}ms`);

      // Wrap the stream to log chunks and detect thought properties
      if (result.stream) {
        const originalStream = result.stream;
        let chunkCount = 0;
        let thoughtDetected = false;
        let errorChunkCount = 0;
        let filteredChunkCount = 0;

        const wrappedStream = new ReadableStream({
          start(_controller) {
            console.log(`🌊 [Gemini Stream] Stream started with intelligent error filtering...`);
          },
          async pull(controller) {
            try {
              const reader = originalStream.getReader();

              // Keep reading until we get a non-filtered chunk or the stream ends
              while (true) {
                const { done, value } = await reader.read();

                if (done) {
                  console.log(`🏁 [Gemini Stream] Stream completed after ${chunkCount} chunks`);
                  console.log(`📊 [Gemini Stream Stats] Total: ${chunkCount}, Errors: ${errorChunkCount}, Filtered: ${filteredChunkCount}`);
                  if (thoughtDetected) {
                    console.log(`🧠 [Gemini Stream] Thought properties were detected during streaming`);
                  }
                  if (filteredChunkCount > 0) {
                    console.log(`🧹 [Gemini Stream] Successfully filtered ${filteredChunkCount} error chunks to improve UI rendering`);
                  }
                  controller.close();
                  reader.releaseLock();
                  return;
                }

                chunkCount++;

                // Check if this error chunk should be filtered
                if (shouldFilterErrorChunk(value)) {
                  filteredChunkCount++;
                  // Continue to next chunk without enqueuing this one
                  continue;
                }

                // This is a valid chunk - process it

                // Track error chunks that we're NOT filtering (genuine errors)
                if (value?.type === "error") {
                  errorChunkCount++;
                  console.warn(`⚠️ [Genuine Error Chunk ${chunkCount}] Passing through non-thought error:`, {
                    error: (value as any).error?.name,
                    message: (value as any).error?.message?.slice(0, 100),
                  });
                }

                // Removed verbose chunk logging to reduce console noise

                // Check for thought properties in valid chunks
                const valueAny = value as any;
                if (value && (valueAny.thought || valueAny.thinking)) {
                  thoughtDetected = true;
                  console.log(`🧠 [Gemini Thought Chunk] Thought detected in valid chunk ${chunkCount}:`, {
                    thought: !!valueAny.thought,
                    thinking: !!valueAny.thinking,
                    thoughtContent: valueAny.thought,
                    thinkingContent: valueAny.thinking,
                  });
                }

                // Enqueue the valid chunk and break the loop
                controller.enqueue(value);
                reader.releaseLock();
                break;
              }
            } catch (streamError) {
              console.error(`❌ [Gemini Stream Error] Error in chunk ${chunkCount}:`, streamError);
              controller.error(streamError);
            }
          },
        });

        // Return the result with the wrapped stream
        return {
          ...result,
          stream: wrappedStream,
        };
      }

      console.log(`✅ [Gemini] ${this.config.name} streaming started successfully`);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorInfo =
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
              stack: error.stack?.split("\n").slice(0, 3),
            }
          : { error: String(error) };

      console.error(`❌ [Gemini Stream Error] Streaming failed after ${duration}ms:`, errorInfo);

      if (shouldFallbackToOpenAI(error)) {
        console.warn(`🔄 [Fallback] ${this.config.name} streaming falling back to OpenAI due to error:`, error);
        this.onFallback?.(this.config.name, error);
        const fallbackStartTime = Date.now();
        const result = await this.config.fallback.doStream(options);
        const fallbackDuration = Date.now() - fallbackStartTime;
        console.log(`✅ [OpenAI] ${this.config.name} streaming started with fallback in ${fallbackDuration}ms`);
        return result;
      }
      throw error;
    }
  }
}

/**
 * Logs fallback events for monitoring
 */
export function logFallbackEvent(modelName: string, error: any) {
  const errorMessage = error.message || error.toString();
  console.warn(`🔄 [Model Fallback] ${modelName} → OpenAI: ${errorMessage}`);

  // Log additional details for debugging
  if (error.stack) {
    console.debug(`[Model Fallback Debug] ${modelName}:`, error.stack);
  }

  // In production, you might want to send this to your monitoring service
  // Example: analytics.track('model_fallback', { model: modelName, error: errorMessage });
}
