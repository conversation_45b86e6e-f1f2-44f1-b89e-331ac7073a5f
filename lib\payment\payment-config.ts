/**
 * Payment Provider Configuration
 * Manages payment provider settings and factory creation
 */

import { PayOSProvider } from "./providers/payos-provider";
import {
  type PaymentProvider,
  type PaymentProviderName,
  type PaymentServiceConfig,
  type PayOSConfig,
  PaymentProviderError,
} from "@/lib/types/payment";

// Environment configuration
const getPayOSConfig = (): PayOSConfig => {
  const config = {
    clientId: process.env.PAYOS_CLIENT_ID,
    apiKey: process.env.PAYOS_API_KEY,
    checksumKey: process.env.PAYOS_CHECKSUM_KEY,
    partnerCode: process.env.PAYOS_PARTNER_CODE,
  };

  // Validate required fields
  if (!config.clientId || !config.apiKey || !config.checksumKey) {
    throw new Error("PayOS configuration missing required fields: PAYOS_CLIENT_ID, PAYOS_API_KEY, PAYOS_CHECKSUM_KEY");
  }

  return config as PayOSConfig;
};

// Payment service configuration
export const paymentServiceConfig: PaymentServiceConfig = {
  defaultProvider: "payos",
  providers: {
    payos: {
      name: "payos",
      enabled: true,
      config: getPayOSConfig(),
    },
    stripe: {
      name: "stripe",
      enabled: false, // Will be enabled when Stripe provider is implemented
      config: {
        // Stripe configuration will go here
      },
    },
  },
};

// Payment Provider Factory - Function-based approach
const providerCache = new Map<PaymentProviderName, PaymentProvider>();

export function createPaymentProvider(providerName: PaymentProviderName): PaymentProvider {
  // Return cached provider if exists
  if (providerCache.has(providerName)) {
    const provider = providerCache.get(providerName);
    if (provider) {
      return provider;
    }
  }

  const providerConfig = paymentServiceConfig.providers[providerName];

  if (!providerConfig || !providerConfig.enabled) {
    throw new PaymentProviderError(`Payment provider '${providerName}' is not enabled or configured`, providerName);
  }

  let provider: PaymentProvider;

  switch (providerName) {
    case "payos":
      provider = new PayOSProvider(providerConfig.config as PayOSConfig);
      break;
    case "stripe":
      throw new PaymentProviderError("Stripe provider not yet implemented", providerName);
    default:
      throw new PaymentProviderError(`Unknown payment provider: ${providerName}`, providerName);
  }

  // Cache the provider
  providerCache.set(providerName, provider);
  return provider;
}

export function getDefaultPaymentProvider(): PaymentProvider {
  return createPaymentProvider(paymentServiceConfig.defaultProvider);
}

export function getAvailablePaymentProviders(): PaymentProviderName[] {
  return Object.entries(paymentServiceConfig.providers)
    .filter(([_, config]) => config.enabled)
    .map(([name]) => name as PaymentProviderName);
}

export function isPaymentProviderEnabled(providerName: PaymentProviderName): boolean {
  const config = paymentServiceConfig.providers[providerName];
  return config?.enabled || false;
}

// Clear cached providers (useful for testing)
export function clearPaymentProviderCache(): void {
  providerCache.clear();
}

// Configuration validation
export function validatePaymentConfig(): void {
  const errors: string[] = [];

  // Check if at least one provider is enabled
  const enabledProviders = getAvailablePaymentProviders();
  if (enabledProviders.length === 0) {
    errors.push("No payment providers are enabled");
  }

  // Validate default provider is enabled
  if (!isPaymentProviderEnabled(paymentServiceConfig.defaultProvider)) {
    errors.push(`Default payment provider '${paymentServiceConfig.defaultProvider}' is not enabled`);
  }

  // Validate PayOS configuration if enabled
  if (isPaymentProviderEnabled("payos")) {
    try {
      getPayOSConfig();
    } catch (error) {
      errors.push(`PayOS configuration error: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  if (errors.length > 0) {
    throw new Error(`Payment configuration validation failed:\n${errors.join("\n")}`);
  }
}

// Environment-specific configurations
export const getPaymentEnvironment = () => {
  return process.env.NODE_ENV === "production" ? "production" : "sandbox";
};

export const isPaymentTestMode = () => {
  return process.env.NODE_ENV !== "production" || process.env.PAYMENT_TEST_MODE === "true";
};

// Payment URLs configuration
export const getPaymentUrls = () => {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000";

  return {
    success: `${baseUrl}/payment/success`,
    cancel: `${baseUrl}/payment/cancel`,
    webhook: `${baseUrl}/api/webhooks/payment`,
  };
};

// Rate limiting configuration for payments
export const PAYMENT_RATE_LIMITS = {
  maxPaymentsPerUser: 10, // Max payments per user per hour
  maxPaymentsPerIP: 20, // Max payments per IP per hour
  cooldownMinutes: 5, // Cooldown between failed payments
} as const;

// Payment retry configuration
export const PAYMENT_RETRY_CONFIG = {
  maxRetries: 3,
  retryDelayMs: 1000,
  backoffMultiplier: 2,
} as const;
