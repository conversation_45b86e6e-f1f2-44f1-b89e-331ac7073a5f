/**
 * Audit Log Viewer Component
 * Displays comprehensive audit trail for admin actions with filtering and search
 */

"use client";

import { useState, useEffect, useCallback } from "react";
import { Search, Filter, Calendar, User, Activity, ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

interface AuditLogEntry {
  id: string;
  user_id: string;
  amount: number;
  description: string;
  transaction_type: string;
  created_at: string;
  user_email?: string;
}

interface AuditLogResponse {
  logs: AuditLogEntry[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  filters: {
    user_id?: string;
    start_date?: string;
    end_date?: string;
    transaction_type?: string;
  };
}

interface AuditLogViewerProps {
  isOpen: boolean;
  onClose: () => void;
}

export function AuditLogViewer({ isOpen, onClose }: AuditLogViewerProps) {
  const [auditData, setAuditData] = useState<AuditLogResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Filter state
  const [filters, setFilters] = useState({
    search: "",
    transaction_type: "",
    start_date: "",
    end_date: "",
    user_id: "",
  });

  const [currentPage, setCurrentPage] = useState(1);
  const [showFilters, setShowFilters] = useState(false);

  // Fetch audit logs
  const fetchAuditLogs = useCallback(
    async (page = 1) => {
      setIsLoading(true);
      setError(null);

      try {
        const searchParams = new URLSearchParams();

        if (filters.search) searchParams.set("search", filters.search);
        if (filters.transaction_type) searchParams.set("transaction_type", filters.transaction_type);
        if (filters.start_date) searchParams.set("start_date", filters.start_date);
        if (filters.end_date) searchParams.set("end_date", filters.end_date);
        if (filters.user_id) searchParams.set("user_id", filters.user_id);

        searchParams.set("page", page.toString());
        searchParams.set("limit", "20");

        const response = await fetch(`/api/admin/audit?${searchParams.toString()}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch audit logs: ${response.statusText}`);
        }

        const data = await response.json();
        setAuditData(data);
        setCurrentPage(page);
      } catch (error) {
        console.error("Failed to fetch audit logs:", error);
        setError(error instanceof Error ? error.message : "Failed to fetch audit logs");
        toast.error("Failed to load audit logs");
      } finally {
        setIsLoading(false);
      }
    },
    [filters]
  );

  // Handle filter changes
  const handleFilterChange = useCallback((key: string, value: string) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  }, []);

  // Handle search
  const handleSearch = useCallback(() => {
    setCurrentPage(1);
    fetchAuditLogs(1);
  }, [fetchAuditLogs]);

  // Handle page change
  const handlePageChange = useCallback(
    (page: number) => {
      fetchAuditLogs(page);
    },
    [fetchAuditLogs]
  );

  // Reset filters
  const handleResetFilters = useCallback(() => {
    setFilters({
      search: "",
      transaction_type: "",
      start_date: "",
      end_date: "",
      user_id: "",
    });
    setCurrentPage(1);
  }, []);

  // Format date for display
  const formatDate = useCallback((dateString: string) => {
    return new Date(dateString).toLocaleString();
  }, []);

  // Get transaction type badge variant
  const getTransactionTypeBadge = useCallback((type: string) => {
    switch (type) {
      case "admin_adjustment":
        return "default";
      case "deduction":
        return "destructive";
      case "addition":
        return "secondary";
      default:
        return "outline";
    }
  }, []);

  // Format amount with sign and color
  const formatAmount = useCallback((amount: number) => {
    const sign = amount >= 0 ? "+" : "";
    const color = amount >= 0 ? "text-green-600" : "text-red-600";
    return { text: `${sign}${amount}`, color };
  }, []);

  // Initial load
  useEffect(() => {
    if (isOpen) {
      fetchAuditLogs(1);
    }
  }, [isOpen, fetchAuditLogs]);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-50"
        onClick={onClose}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => e.key === "Escape" && onClose()}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-6xl max-h-[90vh] overflow-hidden">
          <CardHeader className="border-b">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Activity className="size-5" />
                <CardTitle>Audit Log</CardTitle>
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" size="sm" onClick={() => setShowFilters(!showFilters)}>
                  <Filter className="size-4 mr-2" />
                  Filters
                </Button>
                <Button variant="ghost" size="sm" onClick={onClose}>
                  ×
                </Button>
              </div>
            </div>

            {/* Filters */}
            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 pt-4 border-t">
                <div>
                  <Label htmlFor="search">Search</Label>
                  <Input
                    id="search"
                    placeholder="Search description or email..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange("search", e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="transaction_type">Transaction Type</Label>
                  <Select value={filters.transaction_type} onValueChange={(value) => handleFilterChange("transaction_type", value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All types</SelectItem>
                      <SelectItem value="admin_adjustment">Admin Adjustment</SelectItem>
                      <SelectItem value="deduction">Deduction</SelectItem>
                      <SelectItem value="addition">Addition</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="start_date">Start Date</Label>
                  <Input id="start_date" type="date" value={filters.start_date} onChange={(e) => handleFilterChange("start_date", e.target.value)} />
                </div>

                <div>
                  <Label htmlFor="end_date">End Date</Label>
                  <Input id="end_date" type="date" value={filters.end_date} onChange={(e) => handleFilterChange("end_date", e.target.value)} />
                </div>

                <div className="flex items-end gap-2">
                  <Button onClick={handleSearch} disabled={isLoading}>
                    <Search className="size-4 mr-2" />
                    Search
                  </Button>
                  <Button variant="outline" onClick={handleResetFilters}>
                    Reset
                  </Button>
                </div>
              </div>
            )}
          </CardHeader>

          <CardContent className="p-0">
            {/* Results Summary */}
            {auditData && (
              <div className="p-4 border-b bg-muted/50">
                <p className="text-sm text-muted-foreground">
                  Showing {auditData.logs.length} of {auditData.total} audit log entries
                  {auditData.total > 0 && ` (Page ${auditData.page})`}
                </p>
              </div>
            )}

            {/* Audit Log List */}
            <div className="overflow-auto max-h-[60vh]">
              {isLoading ? (
                <div className="flex items-center justify-center p-8">
                  <div className="animate-spin rounded-full size-8 border-b-2 border-primary" />
                </div>
              ) : error ? (
                <div className="p-8 text-center text-red-600">
                  <p>Error: {error}</p>
                  <Button variant="outline" onClick={() => fetchAuditLogs(currentPage)} className="mt-4">
                    Retry
                  </Button>
                </div>
              ) : !auditData?.logs.length ? (
                <div className="p-8 text-center text-muted-foreground">
                  <Activity className="size-12 mx-auto mb-4 opacity-50" />
                  <p>No audit log entries found</p>
                </div>
              ) : (
                <div className="divide-y">
                  {auditData.logs.map((log) => {
                    const amount = formatAmount(log.amount);
                    return (
                      <div key={log.id} className="p-4 hover:bg-muted/50">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant={getTransactionTypeBadge(log.transaction_type)}>{log.transaction_type.replace("_", " ")}</Badge>
                              <span className={`font-mono text-sm ${amount.color}`}>{amount.text} credits</span>
                            </div>

                            <p className="text-sm mb-1">{log.description}</p>

                            <div className="flex items-center gap-4 text-xs text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <User className="size-3" />
                                {log.user_email || log.user_id.slice(0, 8)}
                              </span>
                              <span className="flex items-center gap-1">
                                <Calendar className="size-3" />
                                {formatDate(log.created_at)}
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>

            {/* Pagination */}
            {auditData && auditData.total > 0 && (
              <div className="flex items-center justify-between p-4 border-t">
                <p className="text-sm text-muted-foreground">
                  Page {auditData.page} of {Math.ceil(auditData.total / auditData.limit)}
                </p>

                <div className="flex items-center gap-2">
                  <Button variant="outline" size="sm" onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage <= 1 || isLoading}>
                    <ChevronLeft className="size-4" />
                    Previous
                  </Button>

                  <Button variant="outline" size="sm" onClick={() => handlePageChange(currentPage + 1)} disabled={!auditData.hasMore || isLoading}>
                    Next
                    <ChevronRight className="size-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </>
  );
}
