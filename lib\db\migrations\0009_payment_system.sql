-- Payment System Migration
-- Creates tables for credit packages, payment transactions, and webhook events

-- Credit packages table
CREATE TABLE IF NOT EXISTS "credit_packages" (
  "id" varchar(50) PRIMARY KEY NOT NULL,
  "name" varchar(100) NOT NULL,
  "name_en" varchar(100),
  "credits" integer NOT NULL,
  "price_vnd" integer NOT NULL,
  "price_usd" integer,
  "currency" varchar(3) NOT NULL DEFAULT 'VND',
  "description" text NOT NULL,
  "features" json NOT NULL,
  "ui_config" json,
  "provider_configs" json,
  "is_popular" boolean NOT NULL DEFAULT false,
  "is_active" boolean NOT NULL DEFAULT true,
  "sort_order" integer NOT NULL DEFAULT 0,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Payment transactions table (provider-agnostic)
CREATE TABLE IF NOT EXISTS "payment_transactions" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "user_id" uuid NOT NULL,
  "package_id" varchar(50),
  "order_code" varchar(100) NOT NULL UNIQUE,
  "amount" integer NOT NULL,
  "currency" varchar(3) NOT NULL DEFAULT 'VND',
  "status" varchar(20) NOT NULL DEFAULT 'PENDING',
  "payment_provider" varchar(50) NOT NULL,
  "provider_payment_id" varchar(255),
  "provider_data" json,
  "checkout_url" text,
  "return_url" text,
  "cancel_url" text,
  "expires_at" timestamp,
  "paid_at" timestamp,
  "cancelled_at" timestamp,
  "created_at" timestamp DEFAULT now() NOT NULL,
  "updated_at" timestamp DEFAULT now() NOT NULL
);

-- Webhook events log
CREATE TABLE IF NOT EXISTS "payment_webhook_events" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  "payment_transaction_id" uuid,
  "provider" varchar(50) NOT NULL,
  "event_type" varchar(50) NOT NULL,
  "payload" json NOT NULL,
  "signature" varchar(255),
  "processed" boolean NOT NULL DEFAULT false,
  "processed_at" timestamp,
  "created_at" timestamp DEFAULT now() NOT NULL
);

-- Add foreign key constraints
DO $$ BEGIN
  ALTER TABLE "payment_transactions" ADD CONSTRAINT "payment_transactions_user_id_User_id_fk" 
    FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
  ALTER TABLE "payment_transactions" ADD CONSTRAINT "payment_transactions_package_id_credit_packages_id_fk" 
    FOREIGN KEY ("package_id") REFERENCES "public"."credit_packages"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

DO $$ BEGIN
  ALTER TABLE "payment_webhook_events" ADD CONSTRAINT "payment_webhook_events_payment_transaction_id_payment_transactions_id_fk" 
    FOREIGN KEY ("payment_transaction_id") REFERENCES "public"."payment_transactions"("id") ON DELETE set null ON UPDATE no action;
EXCEPTION
  WHEN duplicate_object THEN null;
END $$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS "idx_payment_transactions_user_id" ON "payment_transactions" ("user_id");
CREATE INDEX IF NOT EXISTS "idx_payment_transactions_order_code" ON "payment_transactions" ("order_code");
CREATE INDEX IF NOT EXISTS "idx_payment_transactions_status" ON "payment_transactions" ("status");
CREATE INDEX IF NOT EXISTS "idx_payment_transactions_provider" ON "payment_transactions" ("payment_provider");
CREATE INDEX IF NOT EXISTS "idx_payment_transactions_created_at" ON "payment_transactions" ("created_at");

CREATE INDEX IF NOT EXISTS "idx_webhook_events_provider" ON "payment_webhook_events" ("provider");
CREATE INDEX IF NOT EXISTS "idx_webhook_events_processed" ON "payment_webhook_events" ("processed");
CREATE INDEX IF NOT EXISTS "idx_webhook_events_created_at" ON "payment_webhook_events" ("created_at");

CREATE INDEX IF NOT EXISTS "idx_credit_packages_active" ON "credit_packages" ("is_active");
CREATE INDEX IF NOT EXISTS "idx_credit_packages_sort_order" ON "credit_packages" ("sort_order");

-- Insert default credit packages
INSERT INTO "credit_packages" (
  "id", "name", "name_en", "credits", "price_vnd", "price_usd", "currency", 
  "description", "features", "ui_config", "is_popular", "is_active", "sort_order"
) VALUES 
(
  'basic',
  'Cơ Bản',
  'Basic',
  50,
  120000,
  5,
  'VND',
  'Phù hợp cho người dùng không thường xuyên',
  '["50 tin nhắn chat", "Bao gồm sử dụng công cụ", "Sử dụng trong 30 ngày"]'::json,
  '{"color": "bg-gray-50 dark:bg-gray-900/50 border-gray-200 dark:border-gray-800", "popular": false, "sortOrder": 1}'::json,
  false,
  true,
  1
),
(
  'pro',
  'VIP',
  'Pro',
  250,
  500000,
  20,
  'VND',
  'Gói tốt nhất cho người dùng thường xuyên',
  '["250 tin nhắn chat", "Bao gồm tất cả công cụ", "Sử dụng trong 30 ngày", "Hỗ trợ ưu tiên", "Được 1 lần đọc lá số trực tiếp từ thầy Việt"]'::json,
  '{"color": "bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800", "popular": true, "sortOrder": 2}'::json,
  true,
  true,
  2
),
(
  'enterprise',
  'VIP Pro',
  'Enterprise',
  2000,
  3000000,
  125,
  'VND',
  'Dành cho người dùng chuyên sâu',
  '["2000 tin nhắn chat", "Tất cả tính năng cao cấp", "Sử dụng trong 60 ngày", "Được 3 lần đọc lá số trực tiếp từ thầy Việt"]'::json,
  '{"color": "bg-emerald-50 dark:bg-emerald-900/20 border-emerald-200 dark:border-emerald-800", "popular": false, "sortOrder": 3}'::json,
  false,
  true,
  3
)
ON CONFLICT (id) DO NOTHING;

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_credit_packages_updated_at 
  BEFORE UPDATE ON credit_packages 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_transactions_updated_at 
  BEFORE UPDATE ON payment_transactions 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
