# MCP (Model Context Protocol) Integration

This document explains how to use the MCP integration in the application to access external tools from an MCP server.

## Overview

The application integrates with MCP servers to provide AI models with access to external tools. This allows the AI to perform actions beyond its built-in capabilities, such as searching the web, accessing databases, or interacting with external APIs.

## Configuration

To configure the MCP integration, you need to set the following environment variables:

```
MCP_SERVER_URL=https://your-mcp-server.com/mcp
MCP_API_KEY=your-mcp-api-key
```

You can copy the `.env.local.example` file to `.env.local` and update the values accordingly.

## How It Works

1. When a user sends a message to the AI, the application connects to the MCP server to retrieve available tools.
2. These tools are then provided to the Google Gemini model along with the user's message.
3. If the model decides to use a tool, the tool call is forwarded to the MCP server, which executes the tool and returns the result.
4. The model can then use the tool result to generate a response to the user.

## Implementation Details

The MCP integration is implemented in the following files:

- `lib/ai/mcp-client.ts`: Contains utility functions for connecting to MCP servers and retrieving tools.
- `lib/ai/providers.ts`: Configures the MCP server connection and provides functions to get MCP tools.
- `app/(chat)/api/chat/route.ts`: Integrates MCP tools with the chat API.

## Available MCP Tools

The specific tools available depend on the MCP server you connect to. Common tools include:

- `search-web`: Search the web for information
- `get-weather`: Get weather information for a location
- `execute-code`: Execute code in various languages
- `query-database`: Query a database for information

## Troubleshooting

If you encounter issues with the MCP integration:

1. Check that the MCP server URL and API key are correctly configured.
2. Verify that the MCP server is running and accessible from your application.
3. Check the server logs for any errors related to MCP tool calls.
4. Ensure that the tools you're trying to use are available on the MCP server.

## References

- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
- [Vercel AI SDK MCP Documentation](https://sdk.vercel.ai/docs/ai-sdk-core/tools-and-tool-calling#mcp-tools)
