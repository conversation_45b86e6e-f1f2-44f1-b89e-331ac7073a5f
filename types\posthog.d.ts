/**
 * TypeScript declaration for PostHog global
 */
interface Window {
  posthog?: {
    capture: (event: string, properties?: any) => void;
    identify: (id: string, properties?: any) => void;
    reset: () => void;
    flush: () => void;
    get_session_id: () => string | null;
    get_distinct_id: () => string;
    opt_in_capturing: () => void;
    opt_out_capturing: () => void;
    has_opted_out_capturing: () => boolean;
    has_opted_in_capturing: () => boolean;
    debug: (enabled?: boolean) => void;
    register: (properties: Record<string, any>) => void;
    unregister: (property: string) => void;
    register_once: (properties: Record<string, any>) => void;
    [key: string]: any;
  };
}
