/**
 * Middleware utility for Supabase authentication
 * Following official Supabase Next.js documentation patterns.
 */
import { createServerClient } from "@supabase/ssr";
import { NextResponse, type NextRequest } from "next/server";
import { isEmailAdmin } from "@/lib/auth/admin-config";

export async function updateSession(request: NextRequest) {
  let supabaseResponse = NextResponse.next({
    request,
  });

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error("Missing Supabase environment variables");
  }

  const supabase = createServerClient(supabaseUrl, supabaseAnonKey, {
    cookies: {
      getAll() {
        return request.cookies.getAll();
      },
      setAll(cookiesToSet) {
        cookiesToSet.forEach(({ name, value, options }) => request.cookies.set(name, value));
        supabaseResponse = NextResponse.next({
          request,
        });
        cookiesToSet.forEach(({ name, value, options }) => supabaseResponse.cookies.set(name, value, options));
      },
    },
  });

  // IMPORTANT: Avoid using the user object returned from supabase.auth.getUser() within middleware to determine the user's auth state.
  // Instead, use supabase.auth.getUser() in the page or layout that the middleware is protecting.
  // This is a known issue that is being worked on.
  const {
    data: { user },
  } = await supabase.auth.getUser();

  // Special handling for login and register pages - redirect to home if already authenticated
  if ((request.nextUrl.pathname === "/login" || request.nextUrl.pathname === "/register") && user) {
    return NextResponse.redirect(new URL("/", request.url));
  }

  // If no user and not accessing auth-related routes, redirect to login
  if (
    !user &&
    !request.nextUrl.pathname.startsWith("/login") &&
    !request.nextUrl.pathname.startsWith("/register") &&
    !request.nextUrl.pathname.startsWith("/auth") &&
    !request.nextUrl.pathname.startsWith("/api/auth") &&
    !request.nextUrl.pathname.startsWith("/_next") &&
    !request.nextUrl.pathname.startsWith("/favicon.ico") &&
    !request.nextUrl.pathname.startsWith("/sitemap.xml") &&
    !request.nextUrl.pathname.startsWith("/robots.txt") &&
    !request.nextUrl.pathname.startsWith("/manifest.webmanifest") &&
    !request.nextUrl.pathname.startsWith("/icon.png") &&
    !request.nextUrl.pathname.startsWith("/images/") &&
    !request.nextUrl.pathname.startsWith("/api/webhooks/") // Allow webhooks without auth
  ) {
    return NextResponse.redirect(new URL("/login", request.url));
  }

  // Admin route protection - check if user is trying to access admin routes
  if (user && isAdminRoute(request.nextUrl.pathname)) {
    const isAdmin = isEmailAdmin(user.email);

    if (!isAdmin) {
      return NextResponse.redirect(new URL("/", request.url));
    }
  }

  // IMPORTANT: You must return the supabaseResponse object as is
  return supabaseResponse;
}

// Helper function to check if a route is an admin route
function isAdminRoute(pathname: string): boolean {
  return pathname.startsWith("/admin") || pathname.startsWith("/api/admin");
}

// Helper function to check if a route is public
function isPublicRoute(pathname: string): boolean {
  return (
    pathname.startsWith("/api/auth") ||
    pathname.startsWith("/_next") ||
    pathname.startsWith("/favicon.ico") ||
    pathname.startsWith("/sitemap.xml") ||
    pathname.startsWith("/robots.txt") ||
    pathname === "/login" ||
    pathname === "/register"
  );
}
