// <PERSON>ript to test direct connection to PostHog API
require('dotenv').config({ path: '.env.local' });
const https = require('node:https');

const POSTHOG_API = 'https://us.i.posthog.com';
const POSTHOG_KEY = process.env.NEXT_PUBLIC_POSTHOG_KEY;

if (!POSTHOG_KEY) {
  console.error('NEXT_PUBLIC_POSTHOG_KEY is not set in .env.local');
  process.exit(1);
}

// Create a test event
const testEvent = {
  api_key: POSTHOG_KEY,
  event: 'test_connection_script',
  properties: {
    distinct_id: `test_user_${Date.now()}`,
    timestamp: new Date().toISOString(),
    $lib: 'node-script'
  }
};

// Send the event to PostHog
const req = https.request(`${POSTHOG_API}/capture/`, {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  }
}, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  
  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    console.log('Response Body:', data);
    if (res.statusCode === 200) {
      console.log('Successfully connected to PostHog API!');
    } else {
      console.error('Failed to connect to PostHog API');
    }
  });
});

req.on('error', (error) => {
  console.error('Error connecting to PostHog API:', error);
});

// Send the request
req.write(JSON.stringify(testEvent));
req.end();

console.log('Sending test event to PostHog...');
