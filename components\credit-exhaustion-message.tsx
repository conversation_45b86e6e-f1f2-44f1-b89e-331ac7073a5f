"use client";

import { AlertTriangle, Coins } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { GetMoreCreditsButton } from "./get-more-credits-button";

interface CreditExhaustionMessageProps {
  className?: string;
  variant?: "inline" | "overlay" | "banner";
  showGetMoreCredits?: boolean;
  title?: string;
  description?: string;
}

export function CreditExhaustionMessage({
  className,
  variant = "inline",
  showGetMoreCredits = true,
  title = "No Credits Remaining",
  description = "You need credits to send messages and use tools. Get more credits to continue using Bát Tự Master V.",
}: CreditExhaustionMessageProps) {
  const baseClasses = "flex flex-col items-center gap-4 text-center";

  const variantClasses = {
    inline: "p-6 rounded-lg border bg-muted/50",
    overlay: "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center p-4",
    banner: "p-4 border-b bg-amber-50 dark:bg-amber-900/20 border-amber-200 dark:border-amber-800",
  };

  const iconClasses = {
    inline: "size-12 text-amber-600 dark:text-amber-400",
    overlay: "size-16 text-amber-600 dark:text-amber-400",
    banner: "size-6 text-amber-600 dark:text-amber-400",
  };

  const titleClasses = {
    inline: "text-lg font-semibold text-foreground",
    overlay: "text-xl font-semibold text-foreground",
    banner: "text-base font-medium text-foreground",
  };

  const descriptionClasses = {
    inline: "text-sm text-muted-foreground max-w-md",
    overlay: "text-base text-muted-foreground max-w-lg",
    banner: "text-sm text-muted-foreground",
  };

  if (variant === "overlay") {
    return (
      <div className={cn(variantClasses.overlay, className)}>
        <div className="bg-background rounded-lg border shadow-lg p-8 max-w-md w-full">
          <div className={baseClasses}>
            <div className="flex items-center justify-center size-16 rounded-full bg-amber-100 dark:bg-amber-900/30">
              <AlertTriangle className={iconClasses.overlay} />
            </div>

            <div className="space-y-2">
              <h3 className={titleClasses.overlay}>{title}</h3>
              <p className={descriptionClasses.overlay}>{description}</p>
            </div>

            {showGetMoreCredits && (
              <div className="flex flex-col gap-2 w-full">
                <GetMoreCreditsButton size="lg" className="w-full" />
                <Button variant="outline" size="sm" onClick={() => window.location.reload()}>
                  Refresh Page
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  if (variant === "banner") {
    return (
      <div className={cn(variantClasses.banner, className)}>
        <div className="flex items-center gap-3 max-w-4xl mx-auto">
          <AlertTriangle className={iconClasses.banner} />
          <div className="flex-1 min-w-0">
            <p className={titleClasses.banner}>{title}</p>
            <p className={descriptionClasses.banner}>{description}</p>
          </div>
          {showGetMoreCredits && <GetMoreCreditsButton size="sm" variant="outline" />}
        </div>
      </div>
    );
  }

  // Default inline variant
  return (
    <div className={cn(baseClasses, variantClasses.inline, className)}>
      <div className="flex items-center justify-center size-12 rounded-full bg-amber-100 dark:bg-amber-900/30">
        <Coins className={iconClasses.inline} />
      </div>

      <div className="space-y-2">
        <h3 className={titleClasses.inline}>{title}</h3>
        <p className={descriptionClasses.inline}>{description}</p>
      </div>

      {showGetMoreCredits && <GetMoreCreditsButton />}
    </div>
  );
}

// Specialized variants for common use cases
export function ChatCreditExhaustionMessage({ className }: { className?: string }) {
  return (
    <CreditExhaustionMessage
      className={className}
      variant="inline"
      title="No Credits for Chat"
      description="You need 1 credit to send each message. Get more credits to continue chatting with Bát Tự Master V."
    />
  );
}

export function ToolCreditExhaustionMessage({ className }: { className?: string }) {
  return (
    <CreditExhaustionMessage
      className={className}
      variant="inline"
      title="No Credits for Tools"
      description="You need credits to use tools and features. Get more credits to access all functionality."
    />
  );
}

export function CreditExhaustionBanner({ className }: { className?: string }) {
  return (
    <CreditExhaustionMessage
      className={className}
      variant="banner"
      title="Credits Exhausted"
      description="Get more credits to continue using all features."
    />
  );
}
