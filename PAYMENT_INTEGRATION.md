# PayOS Payment Integration

This document describes the PayOS payment integration implemented for the Bát Tự Master V application using the official `@payos/node` SDK.

## Overview

The payment system provides a provider-agnostic architecture that currently supports PayOS (Vietnamese payment gateway) with the ability to easily add other providers like Stripe in the future. The implementation uses the official PayOS SDK for improved reliability and maintainability.

## Features

- ✅ **PayOS Integration**: Full integration with PayOS using official `@payos/node` SDK
- ✅ **VietQR Support**: QR code payments through Vietnamese banking system
- ✅ **Provider Abstraction**: Easy to switch between payment providers
- ✅ **Automatic Credit Addition**: Credits are automatically added after successful payment
- ✅ **Webhook Handling**: Secure webhook processing using PayOS SDK validation
- ✅ **Payment Status Tracking**: Real-time payment status updates
- ✅ **Error Handling**: Comprehensive error handling and user feedback
- ✅ **Security**: Built-in signature validation through PayOS SDK

## Architecture

### Core Components

1. **Payment Provider Interface** (`lib/types/payment.ts`)

   - Defines the contract for all payment providers
   - Provider-agnostic types and interfaces

2. **Base Provider** (`lib/payment/providers/base-provider.ts`)

   - Abstract base class with common functionality
   - Validation and utility methods

3. **PayOS Provider** (`lib/payment/providers/payos-provider.ts`)

   - PayOS-specific implementation using official `@payos/node` SDK
   - Handles PayOS API calls and webhook processing with built-in validation

4. **Payment Service** (`lib/payment/payment-service.ts`)

   - Main orchestrator for payment operations
   - Manages database transactions and credit additions

5. **Payment Configuration** (`lib/payment/payment-config.ts`)
   - Provider factory and configuration management
   - Environment-based configuration

### Database Schema

The payment system adds three new tables:

- `credit_packages`: Stores available credit packages
- `payment_transactions`: Tracks all payment transactions
- `payment_webhook_events`: Logs webhook events for debugging

## Setup Instructions

### 1. PayOS Account Setup

1. Create an account at [https://my.payos.vn](https://my.payos.vn)
2. Complete business verification
3. Create a payment channel
4. Get your credentials:
   - Client ID
   - API Key
   - Checksum Key

### 2. Install Dependencies

The implementation uses the official PayOS SDK:

```bash
npm install @payos/node
```

### 3. Environment Configuration

Add the following environment variables to your `.env` file:

```bash
# PayOS Configuration (Required)
PAYOS_CLIENT_ID="your-payos-client-id"
PAYOS_API_KEY="your-payos-api-key"
PAYOS_CHECKSUM_KEY="your-payos-checksum-key"
PAYOS_PARTNER_CODE="your-partner-code" # Optional

# Application URLs
NEXT_PUBLIC_SITE_URL="http://localhost:3000"

# Payment Configuration
PAYMENT_TEST_MODE="true" # Set to false in production
```

**Note**: `PAYOS_BASE_URL` is no longer needed as the PayOS SDK handles the API endpoint automatically.

### 4. Database Migration

Run the payment system migration:

```bash
# Apply the migration
npm run db:migrate

# Or manually run the SQL file
psql -d your_database -f lib/db/migrations/0009_payment_system.sql
```

### 5. Webhook Configuration

Configure your webhook URL in PayOS dashboard:

- Webhook URL: `https://yourdomain.com/api/webhooks/payment/payos`
- Make sure your server is accessible from the internet

## API Endpoints

### Payment Creation

- **POST** `/api/payment/create`
- Creates a new payment for a credit package
- Returns checkout URL for redirection

### Payment Details

- **GET** `/api/payment/create?orderCode=123456`
- Retrieves payment details by order code

### PayOS Webhook

- **POST** `/api/webhooks/payment/payos`
- Handles payment notifications from PayOS
- Automatically processes successful payments

## Payment Flow

1. **User selects package** on `/payment` page
2. **Payment creation** via API call to `/api/payment/create`
3. **Redirect to PayOS** checkout page
4. **User completes payment** using VietQR or bank transfer
5. **PayOS sends webhook** to `/api/webhooks/payment/payos`
6. **Credits are added** automatically to user account
7. **User redirected** to success/cancel page

## Credit Packages

The system includes three default packages:

- **Basic (Cơ Bản)**: 50 credits for 120,000 VND
- **Pro (VIP)**: 250 credits for 500,000 VND
- **Enterprise (VIP Pro)**: 2,000 credits for 3,000,000 VND

Packages can be updated through the database or admin interface.

## Security Features

- **Built-in Signature Validation**: PayOS SDK handles all signature validation automatically
- **Provider Isolation**: Each provider handles its own security requirements
- **Input Validation**: Comprehensive validation of all inputs
- **Error Handling**: Secure error messages without exposing internals
- **SDK Security**: Official PayOS SDK provides tested and secure implementations

## Testing

### Local Testing

1. Use PayOS sandbox environment
2. Set `PAYMENT_TEST_MODE="true"`
3. Use test bank accounts provided by PayOS

### Webhook Testing

Use tools like ngrok to expose your local server:

```bash
# Install ngrok
npm install -g ngrok

# Expose local server
ngrok http 3000

# Use the ngrok URL for webhook configuration
# Example: https://abc123.ngrok.io/api/webhooks/payment/payos
```

## Monitoring and Debugging

### Webhook Events

All webhook events are logged in the `payment_webhook_events` table for debugging:

```sql
SELECT * FROM payment_webhook_events
WHERE processed = false
ORDER BY created_at DESC;
```

### Payment Transactions

Monitor payment status:

```sql
SELECT
  order_code,
  status,
  amount,
  payment_provider,
  created_at,
  paid_at
FROM payment_transactions
ORDER BY created_at DESC;
```

## Adding New Payment Providers

To add a new payment provider (e.g., Stripe):

1. Create provider class extending `BasePaymentProvider`
2. Implement required methods
3. Add provider configuration
4. Update `PaymentProviderFactory`
5. Add webhook endpoint

Example structure:

```typescript
class StripeProvider extends BasePaymentProvider {
  name = "stripe";

  async createPayment(request: CreatePaymentRequest): Promise<CreatePaymentResponse> {
    // Stripe implementation
  }

  // ... other required methods
}
```

## Troubleshooting

### Common Issues

1. **Invalid Signature**: Check checksum key configuration
2. **Webhook Not Received**: Verify webhook URL is accessible
3. **Payment Not Processed**: Check webhook event logs
4. **Credits Not Added**: Verify webhook processing completed

### Debug Mode

Enable debug logging by setting:

```bash
NODE_ENV=development
```

This will provide detailed logs for payment processing and webhook handling.

## Support

For PayOS-specific issues:

- PayOS Documentation: [https://payos.vn/docs/](https://payos.vn/docs/)
- PayOS Support: Contact through their dashboard

For application issues:

- Email: <EMAIL>
- Facebook: [Long Nhãn](https://www.facebook.com/longmaba)
