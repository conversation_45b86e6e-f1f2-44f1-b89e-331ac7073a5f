/**
 * Analytics utility for tracking events with PostHog
 * This file provides a standardized way to track events across the application
 */
import { posthog } from "posthog-js";

// Event name constants to ensure consistency
export const ANALYTICS_EVENTS = {
  // User activity events
  USER_SIGNED_UP: "user_signed_up",
  USER_LOGGED_IN: "user_logged_in",
  USER_LOGGED_OUT: "user_logged_out",

  // Chat events
  CHAT_STARTED: "chat_started",
  CHAT_MESSAGE_SENT: "chat_message_sent",
  CHAT_DELETED: "chat_deleted",

  // Tool events
  TOOL_CALLED: "tool_called",
  TOOL_RESULT_RECEIVED: "tool_result_received",

  // Feature usage events
  FEATURE_USED: "feature_used",
};

// Property name constants to ensure consistency
export const ANALYTICS_PROPERTIES = {
  // User properties
  USER_ID: "user_id",
  USER_TYPE: "user_type",
  AUTH_STATUS: "auth_status",
  AUTH_PROVIDER: "auth_provider",

  // Chat properties
  CHAT_ID: "chat_id",
  MESSAGE_ID: "message_id",
  MESSAGE_LENGTH: "message_length",
  CHAT_MODEL: "chat_model",
  VISIBILITY_TYPE: "visibility_type",

  // Tool properties
  TOOL_NAME: "tool_name",
  TOOL_ARGS: "tool_args",
  TOOL_RESULT: "tool_result",
  TOOL_SUCCESS: "tool_success",
  TOOL_DURATION_MS: "tool_duration_ms",

  // Session properties (PostHog built-in)
  SESSION_ID: "$session_id",
  SESSION_DURATION: "$session_duration",
  PAGEVIEW_COUNT: "$pageview_count",

  // Feature properties
  FEATURE_NAME: "feature_name",
  FEATURE_CATEGORY: "feature_category",
};

/**
 * Track a user signup event
 * @param userId The user's ID
 * @param userType The user's type (regular, guest)
 * @param email The user's email (optional)
 */
export function trackSignup(userId: string, userType: string, email?: string) {
  posthog.identify(userId, {
    email,
    [ANALYTICS_PROPERTIES.USER_TYPE]: userType,
  });

  posthog.capture(ANALYTICS_EVENTS.USER_SIGNED_UP, {
    [ANALYTICS_PROPERTIES.USER_ID]: userId,
    [ANALYTICS_PROPERTIES.USER_TYPE]: userType,
  });
}

/**
 * Track a user login event
 * @param userId The user's ID
 * @param userType The user's type (regular, guest)
 * @param provider The authentication provider (optional)
 */
export function trackLogin(userId: string, userType: string, provider?: string) {
  posthog.identify(userId, {
    [ANALYTICS_PROPERTIES.USER_TYPE]: userType,
    ...(provider && { [ANALYTICS_PROPERTIES.AUTH_PROVIDER]: provider }),
  });

  posthog.capture(ANALYTICS_EVENTS.USER_LOGGED_IN, {
    [ANALYTICS_PROPERTIES.USER_ID]: userId,
    [ANALYTICS_PROPERTIES.USER_TYPE]: userType,
    ...(provider && { [ANALYTICS_PROPERTIES.AUTH_PROVIDER]: provider }),
  });
}

/**
 * Track a user logout event
 */
export function trackLogout() {
  posthog.capture(ANALYTICS_EVENTS.USER_LOGGED_OUT);
  posthog.reset(); // Clear the user identity
}

/**
 * Track a chat started event
 * @param chatId The chat ID
 * @param chatModel The chat model being used
 * @param visibilityType The visibility type of the chat
 */
export function trackChatStarted(chatId: string, chatModel: string, visibilityType: string) {
  // Get the current session ID from PostHog
  const sessionId = posthog.get_session_id();

  posthog.capture(ANALYTICS_EVENTS.CHAT_STARTED, {
    [ANALYTICS_PROPERTIES.CHAT_ID]: chatId,
    [ANALYTICS_PROPERTIES.CHAT_MODEL]: chatModel,
    [ANALYTICS_PROPERTIES.VISIBILITY_TYPE]: visibilityType,
    // Include the session ID explicitly for easier querying
    [ANALYTICS_PROPERTIES.SESSION_ID]: sessionId,
  });
}

/**
 * Track a chat message sent event
 * @param chatId The chat ID
 * @param messageId The message ID
 * @param messageLength The length of the message
 */
export function trackMessageSent(chatId: string, messageId: string, messageLength: number) {
  // Get the current session ID from PostHog
  const sessionId = posthog.get_session_id();

  posthog.capture(ANALYTICS_EVENTS.CHAT_MESSAGE_SENT, {
    [ANALYTICS_PROPERTIES.CHAT_ID]: chatId,
    [ANALYTICS_PROPERTIES.MESSAGE_ID]: messageId,
    [ANALYTICS_PROPERTIES.MESSAGE_LENGTH]: messageLength,
    // Include the session ID explicitly for easier querying
    [ANALYTICS_PROPERTIES.SESSION_ID]: sessionId,
  });
}

/**
 * Track a tool call event
 * @param chatId The chat ID
 * @param toolName The name of the tool being called
 * @param args The arguments passed to the tool
 */
export function trackToolCall(chatId: string, toolName: string, args: Record<string, unknown>) {
  // Get the current session ID from PostHog
  const sessionId = posthog.get_session_id();

  posthog.capture(ANALYTICS_EVENTS.TOOL_CALLED, {
    [ANALYTICS_PROPERTIES.CHAT_ID]: chatId,
    [ANALYTICS_PROPERTIES.TOOL_NAME]: toolName,
    [ANALYTICS_PROPERTIES.TOOL_ARGS]: JSON.stringify(args),
    // Include the session ID explicitly for easier querying
    [ANALYTICS_PROPERTIES.SESSION_ID]: sessionId,
  });
}

/**
 * Track a tool result event
 * @param chatId The chat ID
 * @param toolName The name of the tool
 * @param success Whether the tool call was successful
 * @param durationMs The duration of the tool call in milliseconds
 * @param result The result of the tool call (optional)
 */
export function trackToolResult(chatId: string, toolName: string, success: boolean, durationMs: number, result?: unknown) {
  // Get the current session ID from PostHog
  const sessionId = posthog.get_session_id();

  posthog.capture(ANALYTICS_EVENTS.TOOL_RESULT_RECEIVED, {
    [ANALYTICS_PROPERTIES.CHAT_ID]: chatId,
    [ANALYTICS_PROPERTIES.TOOL_NAME]: toolName,
    [ANALYTICS_PROPERTIES.TOOL_SUCCESS]: success,
    [ANALYTICS_PROPERTIES.TOOL_DURATION_MS]: durationMs,
    [ANALYTICS_PROPERTIES.TOOL_RESULT]: result ? JSON.stringify(result) : undefined,
    // Include the session ID explicitly for easier querying
    [ANALYTICS_PROPERTIES.SESSION_ID]: sessionId,
  });
}

/**
 * Track a feature usage event
 * @param featureName The name of the feature
 * @param featureCategory The category of the feature
 * @param properties Additional properties to track
 */
export function trackFeatureUsed(featureName: string, featureCategory: string, properties?: Record<string, unknown>) {
  // Get the current session ID from PostHog
  const sessionId = posthog.get_session_id();

  posthog.capture(ANALYTICS_EVENTS.FEATURE_USED, {
    [ANALYTICS_PROPERTIES.FEATURE_NAME]: featureName,
    [ANALYTICS_PROPERTIES.FEATURE_CATEGORY]: featureCategory,
    // Include the session ID explicitly for easier querying
    [ANALYTICS_PROPERTIES.SESSION_ID]: sessionId,
    ...properties,
  });
}
