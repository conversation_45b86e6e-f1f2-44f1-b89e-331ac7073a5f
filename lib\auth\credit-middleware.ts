/**
 * Credit validation middleware for API routes
 * Provides utilities for checking and deducting credits before processing requests
 */

import { getServerSession } from "@/app/(auth)/auth-server";
import { getServerCreditService } from "@/lib/services/credit-service";
import { ChatSDKError } from "@/lib/errors";
import type { CreditDeductionResult } from "@/lib/types/credit";

/**
 * Credit cost constants for different operations
 */
export const CREDIT_COSTS = {
  CHAT_MESSAGE: 1,
  TOOL_USAGE: 0, // Tools are free as per requirements
} as const;

/**
 * Validates that user has sufficient credits and deducts them atomically
 * Implements fail-fast approach: deduct credits before processing
 *
 * @param creditCost Number of credits to deduct
 * @param description Description for the credit transaction
 * @returns Promise that resolves if successful, throws ChatSDKError if insufficient credits
 */
export async function validateAndDeductCredits(creditCost: number, description: string): Promise<CreditDeductionResult> {
  // Get current session
  const session = await getServerSession();

  if (!session?.user?.id) {
    throw new ChatSDKError("unauthorized:chat", "User not authenticated");
  }

  const userId = session.user.id;
  const creditService = getServerCreditService();

  try {
    // First, check if user has sufficient credits
    const hasCredits = await creditService.validateSufficientCredits(userId, creditCost);

    if (!hasCredits) {
      // Get current balance for error message
      const currentCredits = await creditService.getUserCredits(userId);
      throw new ChatSDKError("payment_required:chat", `Current balance: ${currentCredits}, Required: ${creditCost}`);
    }

    // Deduct credits atomically using RPC function
    const deductionResult = await creditService.deductCredit(userId, creditCost, description);

    if (!deductionResult.success) {
      // Handle specific error cases
      if (deductionResult.error?.includes("Insufficient credits")) {
        throw new ChatSDKError("payment_required:chat", deductionResult.error);
      }
      throw new ChatSDKError("forbidden:credit", deductionResult.error || "Credit deduction failed");
    }

    return deductionResult;
  } catch (error) {
    // Re-throw ChatSDKError as-is
    if (error instanceof ChatSDKError) {
      throw error;
    }

    // Handle other errors
    console.error("Credit validation failed:", error);
    throw new ChatSDKError("forbidden:credit", "Failed to process credit deduction");
  }
}

/**
 * Checks if user has sufficient credits without deducting them
 * Useful for pre-validation before expensive operations
 *
 * @param creditCost Number of credits required
 * @returns Promise<boolean> true if user has sufficient credits
 */
export async function checkSufficientCredits(creditCost: number): Promise<boolean> {
  try {
    const session = await getServerSession();

    if (!session?.user?.id) {
      return false;
    }

    const creditService = getServerCreditService();
    return await creditService.validateSufficientCredits(session.user.id, creditCost);
  } catch (error) {
    console.error("Credit check failed:", error);
    return false;
  }
}

/**
 * Gets current user's credit balance
 *
 * @returns Promise<number> Current credit balance, 0 if error
 */
export async function getCurrentUserCredits(): Promise<number> {
  try {
    const session = await getServerSession();

    if (!session?.user?.id) {
      return 0;
    }

    const creditService = getServerCreditService();
    return await creditService.getUserCredits(session.user.id);
  } catch (error) {
    console.error("Failed to get user credits:", error);
    return 0;
  }
}

/**
 * Middleware wrapper for API routes that require credit deduction
 *
 * @param creditCost Number of credits to deduct
 * @param description Description for the transaction
 * @param handler The actual API route handler
 * @returns Wrapped handler that deducts credits before execution
 */
export function withCreditDeduction<T extends any[], R>(creditCost: number, description: string, handler: (...args: T) => Promise<R>) {
  return async (...args: T): Promise<R> => {
    // Deduct credits first (fail-fast approach)
    await validateAndDeductCredits(creditCost, description);

    // Execute the actual handler
    return await handler(...args);
  };
}

/**
 * Utility to create standardized credit transaction descriptions
 */
export const createTransactionDescription = {
  chatMessage: (messagePreview: string) => `Chat message: ${messagePreview.substring(0, 50)}${messagePreview.length > 50 ? "..." : ""}`,

  toolUsage: (toolName: string) => `Tool usage: ${toolName}`,

  custom: (description: string) => description,
} as const;
