"use client";

import { Coins, CreditCard, Plus } from "lucide-react";
import { Button, type ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface GetMoreCreditsButtonProps extends Omit<ButtonProps, "onClick"> {
  icon?: "coins" | "credit-card" | "plus" | "none";
  text?: string;
  redirectTo?: string;
}

export function GetMoreCreditsButton({
  icon = "coins",
  text = "Get More Credits",
  redirectTo = "/payment",
  className,
  variant = "default",
  size = "default",
  disabled,
  ...props
}: GetMoreCreditsButtonProps) {
  const router = useRouter();
  const [isNavigating, setIsNavigating] = useState(false);

  const handleClick = async () => {
    if (disabled || isNavigating) return;

    setIsNavigating(true);
    try {
      router.push(redirectTo);
    } catch (error) {
      console.error("Navigation failed:", error);
      setIsNavigating(false);
    }
  };

  const getIcon = () => {
    if (icon === "none") return null;

    const iconClasses = "size-4";

    switch (icon) {
      case "credit-card":
        return <CreditCard className={iconClasses} />;
      case "plus":
        return <Plus className={iconClasses} />;
      case "coins":
      default:
        return <Coins className={iconClasses} />;
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={cn("gap-2 transition-all duration-200", isNavigating && "opacity-75", className)}
      disabled={disabled || isNavigating}
      onClick={handleClick}
      {...props}
    >
      {getIcon()}
      {isNavigating ? "Redirecting..." : text}
    </Button>
  );
}

// Specialized variants for common use cases
export function GetMoreCreditsIconButton({ className, ...props }: Omit<GetMoreCreditsButtonProps, "text" | "icon">) {
  return <GetMoreCreditsButton icon="coins" text="" size="icon" className={cn("rounded-full", className)} title="Get More Credits" {...props} />;
}

export function GetMoreCreditsLink({
  className,
  children = "Get More Credits",
  ...props
}: Omit<GetMoreCreditsButtonProps, "text"> & { children?: React.ReactNode }) {
  return (
    <GetMoreCreditsButton
      variant="link"
      icon="none"
      text={typeof children === "string" ? children : "Get More Credits"}
      className={cn("p-0 h-auto font-medium underline-offset-4", className)}
      {...props}
    />
  );
}

export function GetMoreCreditsOutlineButton({ className, ...props }: GetMoreCreditsButtonProps) {
  return (
    <GetMoreCreditsButton
      variant="outline"
      className={cn(
        "border-amber-200 text-amber-700 hover:bg-amber-50 dark:border-amber-800 dark:text-amber-400 dark:hover:bg-amber-900/20",
        className
      )}
      {...props}
    />
  );
}

export function GetMoreCreditsPrimaryButton({ className, ...props }: GetMoreCreditsButtonProps) {
  return (
    <GetMoreCreditsButton
      variant="default"
      className={cn("bg-amber-600 hover:bg-amber-700 text-white dark:bg-amber-600 dark:hover:bg-amber-700", className)}
      {...props}
    />
  );
}
