/*
 * <PERSON>àm tính ngày của tiết khí (24 solar terms) trong năm theo công thức xấp xỉ
 * Dựa trên: Ngày = floor(Y*0.2422 + C) - floor((Y-1)/4)
 * Y = hai chữ số cuối của năm (vd: 2025 -> Y = 25)
 * C: hằng số cho từng tiết khí
 * Trả về đối tượng Date với ngày tháng chính xác (giờ luôn là 0:00)
 */

/**
 * Hàm trợ giúp để chuyển đổi Date sang múi giờ cụ thể
 * @param {Date} date - Đ<PERSON>i tượng Date cần chuyển đổi
 * @param {string} timezone - Múi giờ đích (ví dụ: "Asia/Ho_Chi_Minh")
 * @returns {Date} - Đối tượng Date đã được điều chỉnh theo múi giờ
 */
function convertToTimezone(date, timezone) {
  // Step 1: Find the date components (year, month, day) in the target timezone.
  // 'date' is a UTC Date object representing a specific instant in time.
  const targetYear = Number(date.toLocaleString("en-US", { timeZone: timezone, year: "numeric" }));
  const targetMonth = Number(date.toLocaleString("en-US", { timeZone: timezone, month: "2-digit" })) - 1; // JS month is 0-indexed
  const targetDay = Number(date.toLocaleString("en-US", { timeZone: timezone, day: "2-digit" }));

  // Step 2: Create a new Date object whose UTC value corresponds to midnight (00:00:00)
  // on that specific local date in the target timezone.
  const utcEquivalentOfLocalMidnight = Date.UTC(targetYear, targetMonth, targetDay, 0, 0, 0);

  // Step 3: Create a new Date object from this UTC timestamp.
  // This Date object will represent 00:00:00 of the local day in the target timezone.
  return new Date(utcEquivalentOfLocalMidnight);
}

const SOLAR_TERMS = [
  { name: "Lập xuân", month: 2, C: 3.87 },
  { name: "Vũ thủy", month: 2, C: 18.73 },
  { name: "Kinh trập", month: 3, C: 5.63 },
  { name: "Xuân phân", month: 3, C: 20.646 },
  { name: "Thanh minh", month: 4, C: 4.81 },
  { name: "Cốc vũ", month: 4, C: 20.1 },
  { name: "Lập hạ", month: 5, C: 5.52 },
  { name: "Tiểu mãn", month: 5, C: 21.04 },
  { name: "Mang chủng", month: 6, C: 5.678 },
  { name: "Hạ chí", month: 6, C: 21.37 },
  { name: "Tiểu thử", month: 7, C: 7.108 },
  { name: "Đại thử", month: 7, C: 22.83 },
  { name: "Lập thu", month: 8, C: 7.5 },
  { name: "Xử thử", month: 8, C: 23.13 },
  { name: "Bạch lộ", month: 9, C: 7.646 },
  { name: "Thu phân", month: 9, C: 23.042 },
  { name: "Hàn lộ", month: 10, C: 8.318 },
  { name: "Sương giáng", month: 10, C: 23.438 },
  { name: "Lập đông", month: 11, C: 7.438 },
  { name: "Tiểu tuyết", month: 11, C: 22.36 },
  { name: "Đại tuyết", month: 12, C: 7.18 },
  { name: "Đông chí", month: 12, C: 21.94 },
  { name: "Tiểu hàn", month: 1, C: 5.4055, adjust: (y) => (y > 2000 ? -0.0001 * (y - 2000) : 0) },
  { name: "Đại hàn", month: 1, C: 20.12, adjust: (y) => (y > 2000 ? -0.0001 * (y - 2000) : 0) },
];

/**
 * Trả về ngày tiết khí cho năm và tên tiết khí
 * @param {number} year - Năm đầy đủ, ví dụ 2025
 * @param {string} termName - Tên tiết khí (ví dụ: "Lập xuân")
 * @param {string} timezone - Múi giờ (mặc định: "Asia/Ho_Chi_Minh")
 * @returns {Date} - Đối tượng Date với ngày chính xác theo múi giờ
 */
function getSolarTermDate(year, termName, timezone = "Asia/Ho_Chi_Minh") {
  if (typeof termName === "number") {
    // Hỗ trợ ngược cho cách gọi cũ với index
    if (termName < 0 || termName > 23) {
      throw new RangeError("Index phải từ 0 đến 23");
    }
    return getSolarTermDate(year, SOLAR_TERMS[termName].name, timezone);
  }

  const index = SOLAR_TERMS.findIndex((term) => term.name.toLowerCase() === termName.toLowerCase());
  if (index === -1) {
    throw new RangeError(`Không tìm thấy tiết khí "${termName}"`);
  }

  const term = SOLAR_TERMS[index];
  const Y = year % 100;
  const leapCorrection = Math.floor((Y - 1) / 4);
  // Phép điều chỉnh nếu có cho năm > 2000
  const extra = term.adjust ? term.adjust(year) : 0;
  const day = Math.floor(Y * 0.2422 + term.C + extra) - leapCorrection;

  // The formula calculates the day of the solar term assuming it's 00:00 in UTC+8 (China Time).
  // We need to find the equivalent UTC instant for this event.
  // Date.UTC(year, term.month - 1, day) would give 00:00 on that day *in UTC*.
  // To get 00:00 on that day in UTC+8, we subtract 8 hours from the UTC time.
  // console.log(year, term.month - 1, day);
  const solarTermInstantInUtc = Date.UTC(year, term.month - 1, day, 0, 0, 0);
  const dateAtUtc = new Date(solarTermInstantInUtc);

  // Convert this UTC instant to a Date object representing midnight of the local day
  // in the specified timezone.
  return dateAtUtc;
  // return convertToTimezone(dateAtUtc, timezone);
}

/**
 * Xác định tiết khí cho một ngày cụ thể
 * @param {Date|Object} date - Đối tượng Date hoặc object chứa {day, month, year}
 * @param {string} timezone - Múi giờ (mặc định: "Asia/Ho_Chi_Minh")
 * @returns {Object} - Thông tin về tiết khí: {name, startDate, endDate}
 */
function getSolarTermFromDate(date, timezone = "Asia/Ho_Chi_Minh") {
  // Chuẩn hóa tham số date
  let targetDate;
  if (date instanceof Date) {
    targetDate = new Date(date);
  } else if (typeof date === "object" && date.day && date.month && date.year) {
    // Nếu truyền vào là object {day, month, year}
    targetDate = new Date(Date.UTC(date.year, date.month - 1, date.day));
    targetDate = convertToTimezone(targetDate, timezone);
  } else {
    throw new TypeError("Tham số date phải là đối tượng Date hoặc object {day, month, year}");
  }

  // Lấy năm từ ngày đã chuẩn hóa
  const year = targetDate.getFullYear();

  // Tính toán ngày bắt đầu của tất cả các tiết khí trong năm
  const currentYearTerms = SOLAR_TERMS.map((term) => ({
    name: term.name,
    date: getSolarTermDate(year, term.name, timezone),
  }));

  // Thêm tiết khí đầu tiên của năm sau để xác định kết thúc của tiết khí cuối cùng
  const firstTermNextYear = {
    name: SOLAR_TERMS[0].name,
    date: getSolarTermDate(year + 1, SOLAR_TERMS[0].name, timezone),
  };

  // Sắp xếp các tiết khí theo thứ tự thời gian
  const allTerms = [...currentYearTerms, firstTermNextYear].sort((a, b) => a.date - b.date);

  // Tìm tiết khí mà ngày đã cho thuộc về
  for (let i = 0; i < allTerms.length - 1; i++) {
    // nếu i là lẻ, thì lấy allTerms[i-1]
    let currentTerm;
    if (i % 2 === 1) {
      currentTerm = allTerms[i - 1];
    } else {
      currentTerm = allTerms[i];
    }
    const nextTerm = allTerms[i + 1];

    if (
      (targetDate >= currentTerm.date && targetDate < nextTerm.date) ||
      // Xử lý trường hợp đặc biệt: ngày nằm chính xác trên ranh giới
      targetDate.getTime() === currentTerm.date.getTime()
    ) {
      return {
        name: currentTerm.name,
        startDate: currentTerm.date,
        endDate: nextTerm.date,
      };
    }
  }

  // Nếu không tìm thấy, có thể ngày thuộc về tiết khí cuối năm trước
  // Kiểm tra tiết khí cuối cùng của năm trước
  const lastTermPrevYear = getSolarTermDate(year - 1, SOLAR_TERMS[SOLAR_TERMS.length - 1].name, timezone);
  const firstTermCurrentYear = getSolarTermDate(year, SOLAR_TERMS[0].name, timezone);

  if (targetDate >= lastTermPrevYear && targetDate < firstTermCurrentYear) {
    return {
      name: SOLAR_TERMS[SOLAR_TERMS.length - 1].name,
      startDate: lastTermPrevYear,
      endDate: firstTermCurrentYear,
    };
  }

  // Nếu vẫn không tìm thấy (hiếm khi xảy ra), trả về null
  return null;
}

// Ví dụ sử dụng:
// console.log(getSolarTermDate(2023, "Lập xuân")); // Sử dụng múi giờ mặc định (Asia/Ho_Chi_Minh)
// console.log(getSolarTermDate(2023, "Xuân phân", "UTC")); // Sử dụng múi giờ UTC
// console.log(getSolarTermDate(2023, "Đông chí", "America/New_York")); // Sử dụng múi giờ New York
// console.log(getSolarTermFromDate(new Date(2023, 1, 4))); // Tìm tiết khí cho ngày 4/2/2023

module.exports = { getSolarTermDate, convertToTimezone, getSolarTermFromDate };
