import { PostHog } from "posthog-node";

export default function PostHogClient() {
  const posthogApiKey = process.env.NEXT_PUBLIC_POSTHOG_KEY;
  if (!posthogApiKey) {
    // In a server-side context, you might throw an error or log a warning.
    // For this example, let's log a warning and proceed with a dummy key
    // or handle it as per your application's error handling strategy.
    console.warn("PostHog API key is not configured. Please set NEXT_PUBLIC_POSTHOG_KEY.");
    // Depending on PostHog client leniency, it might handle an undefined key gracefully
    // or you might need to decide on a fallback or error throw.
    // For now, let's assume the original intent was to error out if missing.
    throw new Error("PostHog API key is not configured.");
  }
  const posthogClient = new PostHog(posthogApiKey, {
    host: process.env.NEXT_PUBLIC_POSTHOG_HOST,
    flushAt: 1,
    flushInterval: 0,
  });
  return posthogClient;
}
