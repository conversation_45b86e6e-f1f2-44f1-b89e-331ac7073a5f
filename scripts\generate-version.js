#!/usr/bin/env node

const fs = require("node:fs");
const path = require("node:path");

// Generate version based on timestamp + git commit hash (if available)
function generateVersion() {
  const timestamp = Date.now();
  let version = timestamp.toString();

  try {
    const { execSync } = require("node:child_process");
    const gitHash = execSync("git rev-parse --short HEAD", { encoding: "utf8" }).trim();
    version = `${timestamp}-${gitHash}`;
  } catch (error) {
    console.warn("Git not available, using timestamp only for version");
  }

  return version;
}

// Generate version file
function generateVersionFile() {
  const version = generateVersion();
  const buildTimestamp = Date.now();

  const versionContent = `// Auto-generated version file - DO NOT EDIT MANUALLY
// Generated at: ${new Date().toISOString()}

export const APP_VERSION = '${version}';
export const BUILD_TIMESTAMP = ${buildTimestamp};

// Version checking utilities
export const VERSION_CHECK_INTERVAL = 30000; // Check every 30 seconds
export const VERSION_STORAGE_KEY = 'app_version';

export interface VersionInfo {
  version: string;
  timestamp: number;
  forceReload?: boolean;
}

export async function checkForUpdate(): Promise<VersionInfo | null> {
  try {
    // Fetch version info from a dedicated endpoint
    const response = await fetch(\`/api/version?\${new URLSearchParams({
      t: Date.now().toString() // Prevent caching
    })}\`, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      }
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch version info');
    }
    
    const versionInfo: VersionInfo = await response.json();
    const currentVersion = localStorage.getItem(VERSION_STORAGE_KEY);
    
    if (currentVersion && currentVersion !== versionInfo.version) {
      return {
        ...versionInfo,
        forceReload: true
      };
    }
    
    // Store current version
    localStorage.setItem(VERSION_STORAGE_KEY, versionInfo.version);
    
    return versionInfo;
  } catch (error) {
    console.error('Version check failed:', error);
    return null;
  }
}

export function forceAppUpdate() {
  // Clear all caches
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(registrations => {
      for (const registration of registrations) {
        registration.unregister();
      }
    });
  }
  
  // Clear localStorage/sessionStorage
  try {
    localStorage.clear();
    sessionStorage.clear();
  } catch (e) {
    console.warn('Could not clear storage:', e);
  }
  
  // Force reload with cache bust
  window.location.href = \`\${window.location.href}?v=\${Date.now()}\`;
}
`;

  // Write to lib/version.ts
  const versionFilePath = path.join(process.cwd(), "lib", "version.ts");
  fs.writeFileSync(versionFilePath, versionContent);

  console.log(`✅ Generated version file: ${version}`);

  // Also set environment variables for Next.js
  process.env.NEXT_PUBLIC_APP_VERSION = version;
  process.env.NEXT_PUBLIC_BUILD_TIMESTAMP = buildTimestamp.toString();

  return { version, buildTimestamp };
}

// Run if called directly
if (require.main === module) {
  generateVersionFile();
}

module.exports = { generateVersionFile, generateVersion };
