/**
 * PayOS Webhook Endpoint
 * Handles payment notifications from PayOS
 */

import { type NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase/server";
import { createPaymentProvider } from "@/lib/payment/payment-config";
import { getPaymentService } from "@/lib/payment/payment-service";

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient();

    // Get webhook payload and signature
    const payload = await request.json();
    const signature = request.headers.get("signature") || "";

    console.log("PayOS webhook received:", {
      orderCode: payload.data?.orderCode,
      status: payload.data?.code,
      timestamp: new Date().toISOString(),
    });

    // Log webhook event
    const { error: logError } = await supabase.from("payment_webhook_events").insert({
      provider: "payos",
      eventType: "payment_notification",
      payload,
      signature,
      processed: false,
    });

    if (logError) {
      console.error("Failed to log webhook event:", logError);
    }

    // Validate webhook signature
    const payosProvider = createPaymentProvider("payos");
    if (!payosProvider.validateWebhookSignature(payload, signature)) {
      console.error("Invalid PayOS webhook signature");
      return NextResponse.json({ error: "Invalid signature" }, { status: 401 });
    }

    // Process webhook
    const webhookResult = await payosProvider.processWebhook(payload, signature);
    console.log("PayOS webhook result:", JSON.stringify(webhookResult, null, 2));

    // Handle the webhook result
    const paymentService = getPaymentService();
    console.log("About to call handleWebhookResult with:", {
      orderCode: webhookResult.orderCode,
      status: webhookResult.status,
      amount: webhookResult.amount,
      paymentId: webhookResult.paymentId,
    });

    await paymentService.handleWebhookResult(webhookResult);
    console.log("handleWebhookResult completed successfully");

    // Mark webhook as processed
    if (!logError) {
      await supabase
        .from("payment_webhook_events")
        .update({
          processed: true,
          processedAt: new Date().toISOString(),
        })
        .eq("provider", "payos")
        .eq("payload->data->>orderCode", webhookResult.orderCode)
        .order("created_at", { ascending: false })
        .limit(1);
    }

    console.log("PayOS webhook processed successfully:", {
      orderCode: webhookResult.orderCode,
      status: webhookResult.status,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("PayOS webhook processing failed:", error);

    // Return success to PayOS to avoid retries for unrecoverable errors
    // PayOS will retry failed webhooks, so we need to be careful about what we return
    if (error instanceof Error && error.message.includes("not found")) {
      console.log("Payment transaction not found, returning success to avoid retries");
      return NextResponse.json({ success: true });
    }

    return NextResponse.json({ error: "Webhook processing failed" }, { status: 500 });
  }
}

// Handle PayOS webhook verification (for initial setup)
export async function GET(request: NextRequest) {
  try {
    // PayOS may send GET requests for webhook verification
    const { searchParams } = new URL(request.url);
    const challenge = searchParams.get("challenge");

    if (challenge) {
      return NextResponse.json({ challenge });
    }

    return NextResponse.json({
      status: "PayOS webhook endpoint is active",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("PayOS webhook verification failed:", error);
    return NextResponse.json({ error: "Webhook verification failed" }, { status: 500 });
  }
}
