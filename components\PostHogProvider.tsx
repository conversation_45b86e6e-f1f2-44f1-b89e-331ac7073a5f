"use client";

import { posthog } from "posthog-js";
import { <PERSON>Hog<PERSON><PERSON>ider as <PERSON><PERSON>rovider, usePostHog } from "posthog-js/react";
import { Suspense, useEffect } from "react";
import { usePathname, useSearchParams } from "next/navigation";

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    try {
      // Only initialize PostHog if we have a key
      if (process.env.NEXT_PUBLIC_POSTHOG_KEY) {
        posthog.init(process.env.NEXT_PUBLIC_POSTHOG_KEY, {
          // Use direct connection to PostHog instead of proxy
          api_host: "https://us.i.posthog.com",
          ui_host: "https://us.posthog.com",

          // Core functionality
          capture_pageview: false, // We capture pageviews manually
          persistence: "localStorage+cookie", // Use localStorage and cookie for persistence

          // Disable all debug settings in production
          debug: false,
          verbose: false,

          // Disable test events
          loaded: () => {
            // Silent initialization
          },
        });
      }
    } catch (error) {
      // Silent error handling in production
    }
  }, []);

  return (
    <PHProvider client={posthog}>
      <SuspendedPostHogPageView />
      {children}
    </PHProvider>
  );
}

function PostHogPageView() {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const posthog = usePostHog();

  useEffect(() => {
    if (pathname && posthog) {
      let url = `${window.origin}${pathname}`;
      const search = searchParams.toString();
      if (search) {
        url = `${url}?${search}`;
      }
      posthog.capture("$pageview", { $current_url: url });
    }
  }, [pathname, searchParams, posthog]);

  return null;
}

function SuspendedPostHogPageView() {
  return (
    <Suspense fallback={null}>
      <PostHogPageView />
    </Suspense>
  );
}
