/**
 * User list component for admin dashboard
 * Displays paginated list of users with credit information
 */

"use client";

import { useCallback, useState } from "react";
import { MoreHorizontal, CreditCard, User, Users, Activity } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CreditAdjustmentForm } from "./credit-adjustment-form";
import { BulkOperations } from "./bulk-operations";
import { AuditLogViewer } from "./audit-log-viewer";
import { toast } from "sonner";
import type { AdminUserData, AdminUserListResponse } from "@/lib/services/admin-service";

interface UserListProps {
  data: AdminUserListResponse | null;
  isLoading?: boolean;
  error?: string | null;
  onPageChange: (page: number) => void;
  onUserAction?: (action: string, user: AdminUserData) => void;
}

interface UserRowProps {
  user: AdminUserData;
  isSelected: boolean;
  onAction?: (action: string, user: AdminUserData) => void;
  onSelectionChange: (user: AdminUserData, selected: boolean) => void;
}

function UserRow({ user, isSelected, onAction, onSelectionChange }: UserRowProps) {
  const handleAction = useCallback(
    (action: string) => {
      onAction?.(action, user);
    },
    [onAction, user]
  );

  const handleSelectionChange = useCallback(
    (checked: boolean) => {
      onSelectionChange(user, checked);
    },
    [onSelectionChange, user]
  );

  // Get credit status color
  const getCreditBadgeVariant = (credits: number) => {
    if (credits === 0) return "destructive";
    if (credits <= 5) return "secondary";
    return "default";
  };

  // Get user initials for avatar
  const getUserInitials = (email: string) => {
    return email.split("@")[0].slice(0, 2).toUpperCase();
  };

  return (
    <div
      className={`grid gap-4 p-4 border-b hover:bg-muted/50 ${isSelected ? "bg-blue-50" : ""}`}
      style={{ gridTemplateColumns: "auto 2fr 1fr 1fr auto" }}
    >
      <div className="flex items-center justify-center w-8">
        <Checkbox checked={isSelected} onCheckedChange={handleSelectionChange} />
      </div>

      <div className="flex items-center gap-3 min-w-0">
        <div className="size-8 rounded-full bg-muted flex items-center justify-center text-xs font-medium shrink-0">
          {getUserInitials(user.email)}
        </div>
        <div className="min-w-0 flex-1">
          <div className="font-medium truncate">{user.email}</div>
          <div className="text-sm text-muted-foreground">ID: {user.id.slice(0, 8)}...</div>
        </div>
      </div>

      <div className="flex items-center">
        <Badge variant={getCreditBadgeVariant(user.credits)}>{user.credits} credits</Badge>
      </div>

      <div className="flex items-center text-sm">{user.total_credits_used}</div>

      <div className="flex items-center justify-end">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="size-8 p-0">
              <MoreHorizontal className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleAction("view")}>
              <User className="mr-2 size-4" />
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleAction("adjust-credits")}>
              <CreditCard className="mr-2 size-4" />
              Adjust Credits
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

function LoadingRow() {
  return (
    <div className="grid gap-4 p-4 border-b" style={{ gridTemplateColumns: "auto 2fr 1fr 1fr auto" }}>
      <div className="flex items-center justify-center w-8">
        <div className="size-4 bg-muted animate-pulse rounded" />
      </div>
      <div className="flex items-center gap-3 min-w-0">
        <div className="size-8 bg-muted animate-pulse rounded-full shrink-0" />
        <div className="space-y-1 min-w-0 flex-1">
          <div className="h-4 w-full max-w-48 bg-muted animate-pulse rounded" />
          <div className="h-3 w-24 bg-muted animate-pulse rounded" />
        </div>
      </div>
      <div className="flex items-center">
        <div className="h-6 w-20 bg-muted animate-pulse rounded" />
      </div>
      <div className="flex items-center">
        <div className="h-4 w-12 bg-muted animate-pulse rounded" />
      </div>
      <div className="flex items-center justify-end">
        <div className="size-8 bg-muted animate-pulse rounded" />
      </div>
    </div>
  );
}

function Pagination({ data, onPageChange }: { data: AdminUserListResponse; onPageChange: (page: number) => void }) {
  const { page, total, limit, hasMore } = data;
  const totalPages = Math.ceil(total / limit);

  return (
    <div className="flex items-center justify-between">
      <div className="text-sm text-muted-foreground">
        Showing {(page - 1) * limit + 1} to {Math.min(page * limit, total)} of {total} users
      </div>

      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={() => onPageChange(page - 1)} disabled={page <= 1}>
          Previous
        </Button>

        <div className="flex items-center gap-1">
          {/* Show page numbers */}
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            const pageNum = Math.max(1, Math.min(totalPages - 4, page - 2)) + i;
            if (pageNum > totalPages) return null;

            return (
              <Button
                key={pageNum}
                variant={pageNum === page ? "default" : "outline"}
                size="sm"
                onClick={() => onPageChange(pageNum)}
                className="size-8 p-0"
              >
                {pageNum}
              </Button>
            );
          })}
        </div>

        <Button variant="outline" size="sm" onClick={() => onPageChange(page + 1)} disabled={!hasMore}>
          Next
        </Button>
      </div>
    </div>
  );
}

export function UserList({ data, isLoading, error, onPageChange, onUserAction }: UserListProps) {
  const [selectedUser, setSelectedUser] = useState<AdminUserData | null>(null);
  const [selectedUsers, setSelectedUsers] = useState<AdminUserData[]>([]);
  const [isAdjustmentFormOpen, setIsAdjustmentFormOpen] = useState(false);
  const [isBulkOperationsOpen, setIsBulkOperationsOpen] = useState(false);
  const [isAuditLogOpen, setIsAuditLogOpen] = useState(false);

  // Handle user selection
  const handleUserSelection = useCallback((user: AdminUserData, selected: boolean) => {
    setSelectedUsers((prev) => {
      if (selected) {
        return [...prev, user];
      } else {
        return prev.filter((u) => u.id !== user.id);
      }
    });
  }, []);

  // Handle select all
  const handleSelectAll = useCallback(
    (checked: boolean) => {
      if (checked && data?.users) {
        setSelectedUsers(data.users);
      } else {
        setSelectedUsers([]);
      }
    },
    [data?.users]
  );

  // Handle user actions
  const handleUserAction = useCallback(
    (action: string, user: AdminUserData) => {
      if (action === "adjust-credits") {
        setSelectedUser(user);
        setIsAdjustmentFormOpen(true);
      } else if (action === "view") {
        toast.success("User details view coming in Phase 4");
      } else {
        onUserAction?.(action, user);
      }
    },
    [onUserAction]
  );

  // Handle credit adjustment success
  const handleCreditAdjustmentSuccess = useCallback(() => {
    setIsAdjustmentFormOpen(false);
    setSelectedUser(null);
    // Trigger a refresh of the user list
    if (onUserAction && selectedUser) {
      onUserAction("refresh", selectedUser);
    }
  }, [onUserAction, selectedUser]);

  // Handle bulk operations success
  const handleBulkOperationsSuccess = useCallback(() => {
    setIsBulkOperationsOpen(false);
    setSelectedUsers([]);
    // Trigger a refresh of the user list
    if (onUserAction && data?.users?.[0]) {
      onUserAction("refresh", data.users[0]);
    }
  }, [onUserAction, data?.users]);

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <p className="font-medium">Failed to load users</p>
            <p className="text-sm text-muted-foreground mt-1">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const allSelected = data?.users && selectedUsers.length === data.users.length;
  const someSelected = selectedUsers.length > 0;

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <User className="size-5" />
              User Management
            </CardTitle>

            {/* Bulk Operations Controls */}
            <div className="flex items-center gap-2">
              {someSelected && (
                <>
                  <Badge variant="secondary">{selectedUsers.length} selected</Badge>
                  <Button variant="outline" size="sm" onClick={() => setIsBulkOperationsOpen(true)} className="flex items-center gap-2">
                    <Users className="size-4" />
                    Bulk Operations
                  </Button>
                </>
              )}
              <Button variant="outline" size="sm" onClick={() => setIsAuditLogOpen(true)} className="flex items-center gap-2">
                <Activity className="size-4" />
                Audit Log
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {/* Table Header */}
          <div className="grid gap-4 p-4 border-b bg-muted/50 font-medium text-sm" style={{ gridTemplateColumns: "auto 2fr 1fr 1fr auto" }}>
            <div className="flex items-center justify-center w-8">
              <Checkbox checked={allSelected} onCheckedChange={handleSelectAll} indeterminate={someSelected && !allSelected} />
            </div>
            <div>User</div>
            <div>Credits</div>
            <div>Used</div>
            <div />
          </div>

          {/* Table Body */}
          <div>
            {isLoading ? (
              // Show loading rows
              Array.from({ length: 5 }, (_, i) => <LoadingRow key={`loading-row-${Date.now()}-${i}`} />)
            ) : data?.users.length ? (
              // Show actual users
              data.users.map((user) => (
                <UserRow
                  key={user.id}
                  user={user}
                  isSelected={selectedUsers.some((u) => u.id === user.id)}
                  onAction={handleUserAction}
                  onSelectionChange={handleUserSelection}
                />
              ))
            ) : (
              // Show empty state
              <div className="text-center py-8">
                <div className="text-muted-foreground">
                  <User className="size-8 mx-auto mb-2 opacity-50" />
                  <p>No users found</p>
                  <p className="text-sm">Try adjusting your search or filters</p>
                </div>
              </div>
            )}
          </div>

          {/* Pagination */}
          {data && data.total > 0 && !isLoading && (
            <div className="p-4 border-t">
              <Pagination data={data} onPageChange={onPageChange} />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Credit Adjustment Form */}
      {selectedUser && (
        <CreditAdjustmentForm
          user={selectedUser}
          isOpen={isAdjustmentFormOpen}
          onClose={() => setIsAdjustmentFormOpen(false)}
          onSuccess={handleCreditAdjustmentSuccess}
        />
      )}

      {/* Bulk Operations */}
      <BulkOperations
        selectedUsers={selectedUsers}
        isOpen={isBulkOperationsOpen}
        onClose={() => setIsBulkOperationsOpen(false)}
        onSuccess={handleBulkOperationsSuccess}
      />

      {/* Audit Log Viewer */}
      <AuditLogViewer isOpen={isAuditLogOpen} onClose={() => setIsAuditLogOpen(false)} />
    </>
  );
}
