/**
 * Server-side analytics utility for tracking events with PostHog
 * This file provides a standardized way to track events on the server
 */
import PostHogClient from "@/lib/posthog";

// Event name constants to ensure consistency
export const ANALYTICS_EVENTS = {
  // Tool events
  TOOL_CALLED: "tool_called",
  TOOL_RESULT_RECEIVED: "tool_result_received",

  // Tool events only
};

// Property name constants to ensure consistency
export const ANALYTICS_PROPERTIES = {
  // User properties
  USER_ID: "user_id",
  USER_TYPE: "user_type",

  // Chat properties
  CHAT_ID: "chat_id",
  MESSAGE_ID: "message_id",

  // Tool properties
  TOOL_NAME: "tool_name",
  TOOL_ARGS: "tool_args",
  TOOL_RESULT: "tool_result",
  TOOL_SUCCESS: "tool_success",
  TOOL_DURATION_MS: "tool_duration_ms",

  // Session properties (PostHog built-in)
  SESSION_ID: "$session_id",
};

/**
 * Track a tool call event on the server
 * @param userId The user's ID
 * @param chatId The chat ID
 * @param toolName The name of the tool being called
 * @param args The arguments passed to the tool
 */
export function trackToolCall(userId: string, chatId: string, toolName: string, args: Record<string, unknown>) {
  const posthog = PostHogClient();

  posthog.capture({
    distinctId: userId,
    event: ANALYTICS_EVENTS.TOOL_CALLED,
    properties: {
      [ANALYTICS_PROPERTIES.USER_ID]: userId,
      [ANALYTICS_PROPERTIES.CHAT_ID]: chatId,
      [ANALYTICS_PROPERTIES.TOOL_NAME]: toolName,
      [ANALYTICS_PROPERTIES.TOOL_ARGS]: JSON.stringify(args),
    },
  });

  // Make sure to flush events
  posthog.flush();
}

/**
 * Track a tool result event on the server
 * @param userId The user's ID
 * @param chatId The chat ID
 * @param toolName The name of the tool
 * @param success Whether the tool call was successful
 * @param durationMs The duration of the tool call in milliseconds
 * @param result The result of the tool call (optional)
 */
export function trackToolResult(userId: string, chatId: string, toolName: string, success: boolean, durationMs: number, result?: unknown) {
  const posthog = PostHogClient();

  posthog.capture({
    distinctId: userId,
    event: ANALYTICS_EVENTS.TOOL_RESULT_RECEIVED,
    properties: {
      [ANALYTICS_PROPERTIES.USER_ID]: userId,
      [ANALYTICS_PROPERTIES.CHAT_ID]: chatId,
      [ANALYTICS_PROPERTIES.TOOL_NAME]: toolName,
      [ANALYTICS_PROPERTIES.TOOL_SUCCESS]: success,
      [ANALYTICS_PROPERTIES.TOOL_DURATION_MS]: durationMs,
      [ANALYTICS_PROPERTIES.TOOL_RESULT]: result ? JSON.stringify(result) : undefined,
    },
  });

  // Make sure to flush events
  posthog.flush();
}
