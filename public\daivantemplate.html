<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Đại Vận Game Card</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #3c3836;
            /* Dark background from Bát Tự card style */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* Theme Colors from Bát Tự card style */
        :root {
            --theme-bg-cream: #fbf1c7;
            --theme-accent-red: #cc241d;
            --theme-accent-gold: #fabd2f;
            --theme-text-dark: #3c3836;
            --theme-text-light-gray: #7c6f64;
            --theme-border-subtle: #d5c4a1;

            /* Elemental Colors */
            --color-wood: #556b2f;
            /* Mộc - Dark Olive Green */
            --color-fire: #cc241d;
            /* Hỏa - Theme Red */
            --color-earth: #d4a373;
            /* Thổ - Earthy Brown/Tan */
            --color-metal: #808080;
            /* Kim - Grey */
            --color-water: #0077b6;
            /* Thủy - Blue */
        }

        .game-card {
            width: 380px;
            background-color: var(--theme-bg-cream);
            border: 3px solid var(--theme-accent-gold);
            border-radius: 1.5rem;
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1), 0 0 20px rgba(250, 189, 47, 0.2);
            color: var(--theme-text-dark);
            padding: 1.25rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
            /* Increased gap slightly for sections */
        }

        .card-header {
            display: flex;
            justify-content: center;
            /* Center title for Đại Vận card */
            align-items: center;
            border-bottom: 2px solid var(--theme-accent-gold);
            padding-bottom: 0.6rem;
        }

        .card-header .title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--theme-accent-red);
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.05);
        }

        /* Styling for the Đại Vận grid */
        .dai-van-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            /* 2 columns */
            gap: 0.75rem;
            /* Gap between items */
        }

        .dai-van-item {
            background-color: rgba(255, 255, 255, 0.3);
            padding: 0.75rem;
            /* More padding for readability */
            border-radius: 0.5rem;
            /* Slightly larger radius */
            text-align: center;
            border: 1px solid var(--theme-border-subtle);
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            /* Space between elements within an item */
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        /* Highlight style for the current Đại Vận period */
        .dai-van-item.current-dai-van {
            border-color: var(--theme-accent-red);
            /* Red border for highlight */
            box-shadow: 0 0 15px rgba(204, 36, 29, 0.4);
            /* Red glow */
            transform: scale(1.03);
            /* Slightly larger */
            background-color: rgba(250, 189, 47, 0.1);
            /* Subtle gold tint */
        }

        .dai-van-item.current-dai-van .dai-van-period-years {
            color: var(--theme-accent-red);
            font-weight: 700;
        }


        .dai-van-period-years {
            display: block;
            font-size: 0.7rem;
            /* Smaller font for year range */
            font-weight: 600;
            color: var(--theme-text-light-gray);
            margin-bottom: 0.2rem;
            background-color: rgba(0, 0, 0, 0.05);
            padding: 0.15rem 0.3rem;
            border-radius: 0.25rem;
        }

        .dai-van-period-stem-top {
            display: block;
            font-size: 1.1rem;
            /* Prominent Stem */
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .dai-van-period-combined {
            font-size: 1rem;
            /* Font size for combined Stem-Branch */
            font-weight: 700;
        }

        /* Color classes for Heavenly Stems (Thiên Can) */
        .can-Giáp,
        .can-Ất {
            color: var(--color-wood);
        }

        .can-Bính,
        .can-Đinh {
            color: var(--color-fire);
        }

        .can-Mậu,
        .can-Kỷ {
            color: var(--color-earth);
        }

        .can-Canh,
        .can-Tân {
            color: var(--color-metal);
        }

        .can-Nhâm,
        .can-Quý {
            color: var(--color-water);
        }

        /* Color classes for Earthly Branches (Địa Chi) */
        .chi-Dần,
        .chi-Mão {
            color: var(--color-wood);
        }

        .chi-Tỵ,
        .chi-Ngọ {
            color: var(--color-fire);
        }

        .chi-Thìn,
        .chi-Tuất,
        .chi-Sửu,
        .chi-Mùi {
            color: var(--color-earth);
        }

        .chi-Thân,
        .chi-Dậu {
            color: var(--color-metal);
        }

        .chi-Hợi,
        .chi-Tý {
            color: var(--color-water);
        }


        .card-footer {
            font-size: 0.7rem;
            text-align: center;
            color: var(--theme-text-light-gray);
            padding-top: 0.6rem;
            border-top: 1px solid var(--theme-border-subtle);
            margin-top: auto;
            /* Pushes footer to the bottom if card height allows */
        }
    </style>
</head>

<body>
    <div class="game-card">
        <div class="card-header">
            <h1 class="title">ĐẠI VẬN</h1>
        </div>

        <div class="dai-van-grid" id="daiVanGridContainer">
        </div>

        <div class="card-footer">
            <p>Bát Tự Destiny Card - Đại Vận</p>
        </div>
    </div>

    <script>
        const daiVanDataInput = {
            '2005': 'Ất', '2010': 'Dậu',
            '2015': 'Bính', '2020': 'Tuất',
            '2025': 'Đinh', '2030': 'Hợi',
            '2035': 'Mậu', '2040': 'Tý',
            '2045': 'Kỷ', '2050': 'Sửu',
            '2055': 'Canh', '2060': 'Dần',
            '2065': 'Tân', '2070': 'Mão',
            '2075': 'Nhâm', '2080': 'Thìn'
        };

        // Get current year - for a static example, we'll use 2025 as provided.
        // In a dynamic environment, you would use: const currentYear = new Date().getFullYear();
        const currentYear = 2025;

        function createDaiVanPeriods(data) {
            const periods = [];
            const sortedYears = Object.keys(data).map(Number).sort((a, b) => a - b);

            for (let i = 0; i < sortedYears.length; i += 2) {
                const startYearPeriod = sortedYears[i]; // This is the start year of the 10-year Đại Vận
                const stem = data[startYearPeriod.toString()];

                if (i + 1 < sortedYears.length) {
                    const branchKeyYear = sortedYears[i + 1];
                    const branch = data[branchKeyYear.toString()];

                    const endYearPeriod = startYearPeriod + 9;
                    periods.push({
                        startYear: startYearPeriod, // Store start year for comparison
                        endYear: endYearPeriod,     // Store end year for comparison
                        yearRange: `${startYearPeriod} - ${endYearPeriod}`,
                        topStem: stem,
                        combinedStem: stem,
                        combinedBranch: branch
                    });
                }
            }
            return periods;
        }

        function displayDaiVanPeriods() {
            const container = document.getElementById('daiVanGridContainer');
            if (!container) return;

            const periods = createDaiVanPeriods(daiVanDataInput);

            periods.forEach(period => {
                const itemDiv = document.createElement('div');
                itemDiv.classList.add('dai-van-item');

                // Check if this is the current Đại Vận period
                if (currentYear >= period.startYear && currentYear <= period.endYear) {
                    itemDiv.classList.add('current-dai-van');
                }

                const yearSpan = document.createElement('span');
                yearSpan.classList.add('dai-van-period-years');
                yearSpan.textContent = period.yearRange;

                const topStemSpan = document.createElement('span');
                topStemSpan.classList.add('dai-van-period-stem-top', `can-${period.topStem}`);
                topStemSpan.textContent = period.topStem;

                const combinedSpan = document.createElement('span');
                combinedSpan.classList.add('dai-van-period-combined');

                const combinedStemPart = document.createElement('span');
                combinedStemPart.classList.add(`can-${period.combinedStem}`);
                combinedStemPart.textContent = period.combinedStem;

                const combinedBranchPart = document.createElement('span');
                combinedBranchPart.classList.add(`chi-${period.combinedBranch}`);
                combinedBranchPart.textContent = period.combinedBranch;

                combinedSpan.appendChild(combinedStemPart);
                combinedSpan.appendChild(document.createTextNode(' '));
                combinedSpan.appendChild(combinedBranchPart);

                itemDiv.appendChild(yearSpan);
                itemDiv.appendChild(topStemSpan);
                itemDiv.appendChild(combinedSpan);
                container.appendChild(itemDiv);
            });
        }

        document.addEventListener('DOMContentLoaded', displayDaiVanPeriods);
    </script>
</body>

</html>