/**
 * Client-side admin status hook
 * Provides real-time admin status checking for React components
 */

"use client";

import { useMemo } from "react";
import { useAuth } from "@/lib/auth/auth-context";
import { isEmailAdmin } from "@/lib/auth/admin-config";

/**
 * Admin status hook result interface
 */
export interface UseIsAdminResult {
  isAdmin: boolean;
  isLoading: boolean;
  email: string | null;
  error: string | null;
}

/**
 * Hook to check if the current user is an admin
 * Provides real-time admin status updates based on authentication context
 *
 * @returns UseIsAdminResult with admin status and loading state
 */
export function useIsAdmin(): UseIsAdminResult {
  const { user, isLoadingAuth } = useAuth();

  const result = useMemo(() => {
    // If still loading authentication, return loading state
    if (isLoadingAuth) {
      return {
        isAdmin: false,
        isLoading: true,
        email: null,
        error: null,
      };
    }

    // If no user, return not admin
    if (!user) {
      return {
        isAdmin: false,
        isLoading: false,
        email: null,
        error: "No authenticated user",
      };
    }

    // If user has no email, return not admin
    if (!user.email) {
      return {
        isAdmin: false,
        isLoading: false,
        email: null,
        error: "User has no email address",
      };
    }

    try {
      // Check admin status
      const isAdminUser = isEmailAdmin(user.email);

      return {
        isAdmin: isAdminUser,
        isLoading: false,
        email: user.email,
        error: null,
      };
    } catch (error) {
      console.error("Error checking admin status in useIsAdmin:", error);

      return {
        isAdmin: false,
        isLoading: false,
        email: user.email,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }, [user, isLoadingAuth]);

  return result;
}

/**
 * Simple hook that returns just the admin status boolean
 * Useful when you only need to know if user is admin
 *
 * @returns boolean indicating if current user is admin
 */
export function useIsAdminSimple(): boolean {
  const { isAdmin } = useIsAdmin();
  return isAdmin;
}

/**
 * Hook that returns admin status with user email
 * Useful for displaying admin-specific UI elements
 *
 * @returns object with admin status and email
 */
export function useAdminStatus(): { isAdmin: boolean; email: string | null } {
  const { isAdmin, email } = useIsAdmin();
  return { isAdmin, email };
}

/**
 * Hook for conditional rendering based on admin status
 * Returns null if not admin, otherwise returns the provided content
 *
 * @param content - Content to render if user is admin
 * @returns content if admin, null otherwise
 */
export function useAdminOnly<T>(content: T): T | null {
  const { isAdmin, isLoading } = useIsAdmin();

  // Don't render anything while loading
  if (isLoading) {
    return null;
  }

  return isAdmin ? content : null;
}

/**
 * Hook for admin-specific error handling
 * Provides admin status with enhanced error information
 *
 * @returns UseIsAdminResult with detailed error information
 */
export function useIsAdminWithError(): UseIsAdminResult {
  return useIsAdmin();
}

/**
 * Hook that triggers a callback when admin status changes
 * Useful for side effects based on admin status
 *
 * @param callback - Function to call when admin status changes
 * @param deps - Additional dependencies for the effect
 */
export function useAdminStatusEffect(callback: (isAdmin: boolean, email: string | null) => void, deps: React.DependencyList = []): void {
  const { isAdmin, email, isLoading } = useIsAdmin();

  // Use useMemo to trigger callback when admin status changes
  // eslint-disable-next-line react-hooks/exhaustive-deps
  useMemo(() => {
    if (!isLoading) {
      callback(isAdmin, email);
    }
  }, [isAdmin, email, isLoading, callback, ...deps]);
}
