"use client";

import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface TransactionPaginationProps {
  currentPage: number;
  totalPages: number;
  hasMore: boolean;
  onPageChange: (page: number) => void;
  loading?: boolean;
  className?: string;
}

export function TransactionPagination({ currentPage, totalPages, hasMore, onPageChange, loading = false, className }: TransactionPaginationProps) {
  const canGoPrevious = currentPage > 1;
  const canGoNext = hasMore && currentPage < totalPages;

  const handlePrevious = () => {
    if (canGoPrevious && !loading) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (canGoNext && !loading) {
      onPageChange(currentPage + 1);
    }
  };

  const handlePageClick = (page: number) => {
    if (page !== currentPage && !loading) {
      onPageChange(page);
    }
  };

  // Generate page numbers to show
  const getPageNumbers = () => {
    const pages: (number | string)[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // Show all pages if total is small
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Show smart pagination with ellipsis
      if (currentPage <= 3) {
        // Show first few pages
        for (let i = 1; i <= 4; i++) {
          pages.push(i);
        }
        pages.push("...");
        pages.push(totalPages);
      } else if (currentPage >= totalPages - 2) {
        // Show last few pages
        pages.push(1);
        pages.push("...");
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        // Show middle pages
        pages.push(1);
        pages.push("...");
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push("...");
        pages.push(totalPages);
      }
    }

    return pages;
  };

  if (totalPages <= 1 && !hasMore) {
    return null;
  }

  return (
    <div className={cn("flex items-center justify-between", className)}>
      {/* Page Info */}
      <div className="text-sm text-muted-foreground">
        Page {currentPage} {totalPages > 0 && `of ${totalPages}`}
        {hasMore && totalPages === 0 && " (more available)"}
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center gap-1">
        {/* Previous Button */}
        <Button variant="outline" size="sm" onClick={handlePrevious} disabled={!canGoPrevious || loading} className="gap-1">
          <ChevronLeft className="size-4" />
          Previous
        </Button>

        {/* Page Numbers */}
        {totalPages > 0 && (
          <div className="hidden sm:flex items-center gap-1 mx-2">
            {getPageNumbers().map((page, index) => (
              <div key={page === "..." ? `ellipsis-${index}` : `page-${page}`}>
                {page === "..." ? (
                  <div className="px-3 py-1">
                    <MoreHorizontal className="size-4 text-muted-foreground" />
                  </div>
                ) : (
                  <Button
                    variant={page === currentPage ? "default" : "ghost"}
                    size="sm"
                    onClick={() => handlePageClick(page as number)}
                    disabled={loading}
                    className="size-8 p-0"
                  >
                    {page}
                  </Button>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Next Button */}
        <Button variant="outline" size="sm" onClick={handleNext} disabled={!canGoNext || loading} className="gap-1">
          Next
          <ChevronRight className="size-4" />
        </Button>
      </div>
    </div>
  );
}

// Simple pagination for basic use cases
export function SimplePagination({ currentPage, hasMore, onPageChange, loading = false, className }: Omit<TransactionPaginationProps, "totalPages">) {
  const canGoPrevious = currentPage > 1;

  return (
    <div className={cn("flex items-center justify-between", className)}>
      <div className="text-sm text-muted-foreground">Page {currentPage}</div>

      <div className="flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={() => onPageChange(currentPage - 1)} disabled={!canGoPrevious || loading}>
          <ChevronLeft className="size-4" />
        </Button>

        <span className="text-sm font-medium px-2">{currentPage}</span>

        <Button variant="outline" size="sm" onClick={() => onPageChange(currentPage + 1)} disabled={!hasMore || loading}>
          <ChevronRight className="size-4" />
        </Button>
      </div>
    </div>
  );
}
