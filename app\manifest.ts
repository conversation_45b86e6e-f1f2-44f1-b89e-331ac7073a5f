import type { MetadataRoute } from "next";

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: "<PERSON><PERSON>t Tự Master V",
    short_name: "<PERSON><PERSON><PERSON> Tự V",
    description: "Ứng dụng phân tích Bát Tự và tư vấn về số mệnh, cuộc đời, tình du<PERSON>ên, công việc.",
    start_url: "/",
    display: "fullscreen",
    background_color: "#FDF0CF",
    theme_color: "#9D0D11",
    id: "/",
    orientation: "portrait",
    scope: "/",
    icons: [
      {
        src: "/images/icon-192x192.png",
        sizes: "192x192",
        type: "image/png",
        purpose: "any",
      },
      {
        src: "/images/icon-512x512.png",
        sizes: "512x512",
        type: "image/png",
        purpose: "any",
      },
    ],
    shortcuts: [
      {
        name: "Tạo cuộc hội thoại mới",
        url: "/chat",
        description: "Bắt đầu cuộc hội thoại mới với B<PERSON>t Tự Master V",
      },
    ],
    categories: ["lifestyle", "utilities"],
  };
}
