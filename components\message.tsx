"use client";

import type { UIMessage } from "ai";
import cx from "classnames";
import { AnimatePresence, motion } from "framer-motion";
import { memo, useState } from "react";
import type { Vote } from "@/lib/db/schema";
import { DocumentToolCall, DocumentToolResult } from "./document";
import { PencilEditIcon } from "./icons";
import Image from "next/image";
import { Markdown } from "./markdown";
import { MessageActions } from "./message-actions";
import { PreviewAttachment } from "./preview-attachment";
import { Weather } from "./weather";
import { BatTuInfo } from "./BatTuInfo";
import equal from "fast-deep-equal";
import { cn, sanitizeText } from "@/lib/utils";
import { Button } from "./ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { MessageEditor } from "./message-editor";
import { DocumentPreview } from "./document-preview";
import { MessageReasoning } from "./message-reasoning";
import type { UseChatHelpers } from "@ai-sdk/react";
import { ToolCallUI } from "./tool-call-ui";
import { DaiVanInfo } from "./DaiVanInfo";

const PurePreviewMessage = ({
  chatId,
  message,
  vote,
  isLoading,
  setMessages,
  reload,
  isReadonly,
  requiresScrollPadding,
}: {
  chatId: string;
  message: UIMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  setMessages: UseChatHelpers["setMessages"];
  reload: UseChatHelpers["reload"];
  isReadonly: boolean;
  requiresScrollPadding: boolean;
}) => {
  const [mode, setMode] = useState<"view" | "edit">("view");

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{ y: 5, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        data-role={message.role}
      >
        <div
          className={cn("flex gap-4 w-full group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl", {
            "w-full": mode === "edit",
            "group-data-[role=user]/message:w-fit": mode !== "edit",
          })}
        >
          {message.role === "assistant" && (
            <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border bg-background overflow-hidden">
              <Image src="/images/avatar.png" alt="Assistant Avatar" width={32} height={32} className="object-cover" />
            </div>
          )}

          <div
            className={cn("flex flex-col gap-4 w-full", {
              "min-h-96": message.role === "assistant" && requiresScrollPadding,
            })}
          >
            {message.experimental_attachments && message.experimental_attachments.length > 0 && (
              <div data-testid={`message-attachments`} className="flex flex-row justify-end gap-2">
                {message.experimental_attachments.map((attachment) => (
                  <PreviewAttachment key={attachment.url} attachment={attachment} />
                ))}
              </div>
            )}

            {message.parts?.map((part, index) => {
              const { type } = part;
              const key = `message-${message.id}-part-${index}`;

              if (type === "reasoning") {
                return <MessageReasoning key={key} isLoading={isLoading} reasoning={part.reasoning} />;
              }

              if (type === "text") {
                if (mode === "view") {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      {message.role === "user" && !isReadonly && (
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              data-testid="message-edit-button"
                              variant="ghost"
                              className="px-2 h-fit rounded-full text-muted-foreground opacity-0 group-hover/message:opacity-100"
                              onClick={() => {
                                setMode("edit");
                              }}
                            >
                              <PencilEditIcon />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>Edit message</TooltipContent>
                        </Tooltip>
                      )}

                      <div
                        data-testid="message-content"
                        className={cn("flex flex-col gap-4 message-content", {
                          "bg-primary text-primary-foreground px-3 py-2 rounded-xl": message.role === "user",
                        })}
                      >
                        <Markdown>{sanitizeText(part.text)}</Markdown>
                      </div>
                    </div>
                  );
                }

                if (mode === "edit") {
                  return (
                    <div key={key} className="flex flex-row gap-2 items-start">
                      <div className="size-8" />

                      <MessageEditor key={message.id} message={message} setMode={setMode} setMessages={setMessages} reload={reload} />
                    </div>
                  );
                }
              }

              if (type === "tool-invocation") {
                const { toolInvocation } = part;
                const { toolName, toolCallId, state } = toolInvocation;

                if (state === "call") {
                  const { args } = toolInvocation;

                  // Special handling for document-related tools
                  if (["createDocument", "updateDocument", "requestSuggestions"].includes(toolName)) {
                    return (
                      <ToolCallUI key={toolCallId} toolName={toolName} toolCallId={toolCallId} state={state} args={args}>
                        {toolName === "createDocument" ? (
                          <DocumentPreview isReadonly={isReadonly} args={args} />
                        ) : toolName === "updateDocument" ? (
                          <DocumentToolCall type="update" args={args} isReadonly={isReadonly} />
                        ) : toolName === "requestSuggestions" ? (
                          <DocumentToolCall type="request-suggestions" args={args} isReadonly={isReadonly} />
                        ) : null}
                      </ToolCallUI>
                    );
                  }

                  // Weather tool
                  if (toolName === "getWeather") {
                    return (
                      // <ToolCallUI key={toolCallId} toolName={toolName} toolCallId={toolCallId} state={state} args={args}>
                      <Weather key={toolCallId} />
                      // </ToolCallUI>
                    );
                  }

                  // Bát Tự tool
                  if (toolName === "checkBatTuTool") {
                    return <BatTuInfo key={toolCallId} batTuData={args} personName={args.name} />;
                  }

                  if (toolName === "getDaiVanTool") {
                    return <DaiVanInfo key={toolCallId} daiVanData={args} personName={args.name} />;
                  }

                  // Generic tool call UI for other tools
                  return (
                    <ToolCallUI key={toolCallId} toolName={toolName} toolCallId={toolCallId} state={state} args={args}>
                      <div className="text-sm text-muted-foreground">Calling {toolName}...</div>
                    </ToolCallUI>
                  );
                }

                if (state === "result") {
                  const { result } = toolInvocation;

                  // Special handling for document-related tools
                  if (["createDocument", "updateDocument", "requestSuggestions"].includes(toolName)) {
                    return (
                      <ToolCallUI key={toolCallId} toolName={toolName} toolCallId={toolCallId} state={state} result={result}>
                        {toolName === "createDocument" ? (
                          <DocumentPreview isReadonly={isReadonly} result={result} />
                        ) : toolName === "updateDocument" ? (
                          <DocumentToolResult type="update" result={result} isReadonly={isReadonly} />
                        ) : toolName === "requestSuggestions" ? (
                          <DocumentToolResult type="request-suggestions" result={result} isReadonly={isReadonly} />
                        ) : null}
                      </ToolCallUI>
                    );
                  }

                  // Weather tool
                  if (toolName === "getWeather") {
                    return (
                      // <ToolCallUI key={toolCallId} toolName={toolName} toolCallId={toolCallId} state={state} result={result}>
                      <Weather key={toolCallId} weatherAtLocation={result} />
                      // </ToolCallUI>
                    );
                  }

                  // Bát Tự tool
                  if (toolName === "checkBatTuTool") {
                    return <BatTuInfo key={toolCallId} batTuData={result} personName={toolInvocation.args?.name} />;
                  }

                  // Special handling for test tool
                  if (toolName === "testTool") {
                    return (
                      <ToolCallUI key={toolCallId} toolName={toolName} toolCallId={toolCallId} state={state} result={result}>
                        <div className="text-sm">
                          <div className="flex flex-col gap-2">
                            <p className="font-medium">{result.message}</p>
                            <p className="text-xs text-muted-foreground">Timestamp: {new Date(result.timestamp).toLocaleString()}</p>
                          </div>
                        </div>
                      </ToolCallUI>
                    );
                  }

                  if (toolName === "getDaiVanTool") {
                    return <DaiVanInfo key={toolCallId} daiVanData={result} personName={toolInvocation.args?.name} />;
                  }

                  // Generic tool result UI for other tools
                  return (
                    <ToolCallUI key={toolCallId} toolName={toolName} toolCallId={toolCallId} state={state} result={result}>
                      <div className="text-sm">
                        {typeof result === "object" && result.message ? (
                          <p>{result.message}</p>
                        ) : (
                          <pre className="bg-muted p-2 rounded-md overflow-x-auto text-xs">{JSON.stringify(result, null, 2)}</pre>
                        )}
                      </div>
                    </ToolCallUI>
                  );
                }
              }
            })}

            {!isReadonly && <MessageActions key={`action-${message.id}`} chatId={chatId} message={message} vote={vote} isLoading={isLoading} />}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export const PreviewMessage = memo(PurePreviewMessage, (prevProps, nextProps) => {
  if (prevProps.isLoading !== nextProps.isLoading) return false;
  if (prevProps.message.id !== nextProps.message.id) return false;
  if (prevProps.requiresScrollPadding !== nextProps.requiresScrollPadding) return false;
  if (!equal(prevProps.message.parts, nextProps.message.parts)) return false;
  if (!equal(prevProps.vote, nextProps.vote)) return false;

  return true;
});

export const ThinkingMessage = () => {
  const role = "assistant";

  return (
    <motion.div
      data-testid="message-assistant-loading"
      className="w-full mx-auto max-w-3xl px-4 group/message min-h-96"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1, transition: { delay: 1 } }}
      data-role={role}
    >
      <div
        className={cx(
          "flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl",
          {
            "group-data-[role=user]/message:bg-muted": true,
          }
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border overflow-hidden">
          <Image src="/images/avatar.png" alt="Assistant Avatar" width={32} height={32} className="object-cover" />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">Hmm...</div>
        </div>
      </div>
    </motion.div>
  );
};

export const ThinkingAfterToolMessage = ({ toolName }: { toolName: string }) => {
  const role = "assistant";
  const formattedToolName = toolName
    .replace(/([A-Z])/g, " $1") // Add space before capital letters
    .replace(/^./, (str) => str.toUpperCase()) // Capitalize first letter
    .replace(/([a-z])([A-Z])/g, "$1 $2") // Add space between camelCase
    .replace(/-/g, " ") // Replace hyphens with spaces
    .replace(/Tool$/, "") // Remove "Tool" suffix if present
    .replace(/_/g, " ") // Replace underscores with spaces
    .trim();

  return (
    <motion.div
      data-testid="message-assistant-thinking-after-tool"
      className="w-full mx-auto max-w-3xl px-4 group/message"
      initial={{ y: 5, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      data-role={role}
    >
      <div
        className={cx(
          "flex gap-4 group-data-[role=user]/message:px-3 w-full group-data-[role=user]/message:w-fit group-data-[role=user]/message:ml-auto group-data-[role=user]/message:max-w-2xl group-data-[role=user]/message:py-2 rounded-xl",
          {
            "group-data-[role=user]/message:bg-muted": true,
          }
        )}
      >
        <div className="size-8 flex items-center rounded-full justify-center ring-1 shrink-0 ring-border overflow-hidden">
          <Image src="/images/avatar.png" alt="Assistant Avatar" width={32} height={32} className="object-cover" />
        </div>

        <div className="flex flex-col gap-2 w-full">
          <div className="flex flex-col gap-4 text-muted-foreground">Processing {formattedToolName} results...</div>
        </div>
      </div>
    </motion.div>
  );
};
