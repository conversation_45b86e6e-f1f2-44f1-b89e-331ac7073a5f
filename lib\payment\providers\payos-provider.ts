/**
 * PayOS Payment Provider Implementation
 * Handles PayOS-specific payment processing for Vietnamese market
 * Uses the official @payos/node SDK
 */

import PayOS from "@payos/node";
import { BasePaymentProvider } from "./base-provider";
import {
  type CreatePaymentRequest,
  type CreatePaymentResponse,
  type PaymentDetails,
  type CancelPaymentResponse,
  type WebhookResult,
  type PayOSConfig,
  PaymentStatus,
  PaymentProviderError,
} from "@/lib/types/payment";

export class PayOSProvider extends BasePaymentProvider {
  name = "payos";
  private payos: PayOS;

  constructor(config: PayOSConfig) {
    super(config);

    this.validateConfig(["clientId", "apiKey", "checksumKey"]);

    // Initialize PayOS SDK
    this.payos = new PayOS(config.clientId, config.apiKey, config.checksumKey, config.partnerCode);

    this.log("info", "PayOS provider initialized with official SDK");
  }

  async createPayment(request: CreatePaymentRequest): Promise<CreatePaymentResponse> {
    try {
      this.validateCreatePaymentRequest(request);

      const orderCode = Number.parseInt(request.orderCode);
      if (Number.isNaN(orderCode)) {
        throw new PaymentProviderError("Order code must be a valid number for PayOS", this.name);
      }

      // Prepare PayOS request data
      const payosRequest = {
        orderCode,
        amount: this.formatAmount(request.amount),
        description: this.formatDescription(request.description, 25), // PayOS has shorter limit
        buyerName: request.customer?.name,
        buyerEmail: request.customer?.email,
        buyerPhone: request.customer?.phone,
        buyerAddress: request.customer?.address,
        items: request.items?.map((item) => ({
          name: item.name,
          quantity: item.quantity,
          price: item.price,
        })),
        cancelUrl: request.cancelUrl,
        returnUrl: request.returnUrl,
        expiredAt: request.expiresAt ? Math.floor(request.expiresAt.getTime() / 1000) : undefined,
      };

      this.log("info", "Creating PayOS payment", { orderCode, amount: request.amount });

      // Use PayOS SDK to create payment link
      const response = await this.payos.createPaymentLink(payosRequest);

      return {
        paymentId: response.paymentLinkId,
        checkoutUrl: response.checkoutUrl,
        qrCode: response.qrCode,
        status: this.mapPayOSStatus(response.status),
        expiresAt: request.expiresAt,
        providerData: response,
      };
    } catch (error) {
      this.log("error", "Failed to create payment", error);
      this.handleProviderError(error, "createPayment");
    }
  }

  async getPayment(paymentId: string): Promise<PaymentDetails> {
    try {
      this.log("info", "Getting PayOS payment details", { paymentId });

      // Use PayOS SDK to get payment information
      const data = await this.payos.getPaymentLinkInformation(paymentId);

      return {
        paymentId: data.id,
        orderCode: data.orderCode.toString(),
        amount: data.amount,
        amountPaid: data.amountPaid || 0,
        amountRemaining: data.amountRemaining || data.amount,
        currency: "VND", // PayOS always uses VND
        status: this.mapPayOSStatus(data.status),
        createdAt: new Date(data.createdAt),
        paidAt: data.transactions?.[0]?.transactionDateTime ? new Date(data.transactions[0].transactionDateTime) : undefined,
        cancelledAt: data.canceledAt ? new Date(data.canceledAt) : undefined,
        transactions:
          data.transactions?.map((tx: any) => ({
            reference: tx.reference,
            amount: tx.amount,
            accountNumber: tx.accountNumber,
            description: tx.description,
            transactionDateTime: new Date(tx.transactionDateTime),
            virtualAccountName: tx.virtualAccountName,
            virtualAccountNumber: tx.virtualAccountNumber,
            counterAccountBankId: tx.counterAccountBankId,
            counterAccountBankName: tx.counterAccountBankName,
            counterAccountName: tx.counterAccountName,
            counterAccountNumber: tx.counterAccountNumber,
          })) || [],
        cancellationReason: data.cancellationReason || undefined,
        providerData: data,
      };
    } catch (error) {
      this.log("error", "Failed to get payment details", error);
      this.handleProviderError(error, "getPayment");
    }
  }

  async cancelPayment(paymentId: string, reason?: string): Promise<CancelPaymentResponse> {
    try {
      this.log("info", "Cancelling PayOS payment", { paymentId, reason });

      // Use PayOS SDK to cancel payment link
      const data = await this.payos.cancelPaymentLink(paymentId, reason);

      return {
        paymentId: data.id,
        status: this.mapPayOSStatus(data.status),
        cancelledAt: data.canceledAt ? new Date(data.canceledAt) : new Date(),
        cancellationReason: data.cancellationReason || undefined,
      };
    } catch (error) {
      this.log("error", "Failed to cancel payment", error);
      this.handleProviderError(error, "cancelPayment");
    }
  }

  async processWebhook(payload: any, signature: string): Promise<WebhookResult> {
    try {
      this.log("info", "Processing PayOS webhook", { orderCode: payload.data?.orderCode });

      if (!this.validateWebhookSignature(payload, signature)) {
        throw new PaymentProviderError("Invalid webhook signature", this.name);
      }

      // Use PayOS SDK to verify webhook data
      const data = this.payos.verifyPaymentWebhookData(payload);

      return {
        paymentId: data.paymentLinkId,
        orderCode: data.orderCode.toString(),
        status: this.mapPayOSStatus(data.code === "00" ? "PAID" : "FAILED"),
        amount: data.amount,
        amountPaid: data.amount,
        paidAt: data.transactionDateTime ? new Date(data.transactionDateTime) : undefined,
        reference: data.reference,
        description: data.description,
        providerData: data,
      };
    } catch (error) {
      this.log("error", "Failed to process webhook", error);
      throw error;
    }
  }

  validateWebhookSignature(payload: any, _signature: string): boolean {
    try {
      // Use PayOS SDK to verify webhook data (this also validates signature)
      // The signature parameter is kept for interface compatibility but not used
      // as PayOS SDK handles signature validation internally
      this.payos.verifyPaymentWebhookData(payload);
      return true;
    } catch (error) {
      this.log("error", "Webhook signature validation failed", error);
      return false;
    }
  }

  private mapPayOSStatus(status: string): PaymentStatus {
    switch (status?.toUpperCase()) {
      case "PENDING":
        return PaymentStatus.PENDING;
      case "PAID":
        return PaymentStatus.PAID;
      case "CANCELLED":
        return PaymentStatus.CANCELLED;
      case "EXPIRED":
        return PaymentStatus.EXPIRED;
      default:
        return PaymentStatus.FAILED;
    }
  }
}
