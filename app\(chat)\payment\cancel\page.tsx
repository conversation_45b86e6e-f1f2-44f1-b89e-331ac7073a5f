"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState, useCallback } from "react";
import Link from "next/link";
import { XCircle, ArrowLeft, RotateCcw, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CreditDisplay } from "@/components/credit-display";
import { useAuth } from "@/lib/auth/auth-context";

interface PaymentDetails {
  paymentId: string;
  orderCode: string;
  amount: number;
  currency: string;
  status: string;
  createdAt: string;
  paidAt?: string;
}

export default function PaymentCancelPage() {
  const searchParams = useSearchParams();
  const { refreshCredits } = useAuth();
  const [paymentDetails, setPaymentDetails] = useState<PaymentDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const orderCode = searchParams.get("orderCode");

  const fetchPaymentDetails = useCallback(async () => {
    try {
      console.log("Checking payment status for cancelled order:", orderCode);
      const response = await fetch(`/api/payment/create?orderCode=${orderCode}`);
      const data = await response.json();

      console.log("Payment status response:", data);

      if (!response.ok) {
        throw new Error(data.error || "Failed to fetch payment details");
      }

      setPaymentDetails(data.data);

      // If payment was actually successful (user might have completed it), refresh credits
      if (data.data.status === "PAID") {
        console.log("Payment was actually successful, refreshing credits");
        await refreshCredits();
        console.log("Credits refreshed successfully");
      } else {
        console.log("Payment status confirmed as:", data.data.status);
      }
    } catch (err) {
      console.error("Failed to fetch payment details:", err);
      setError(err instanceof Error ? err.message : "Failed to fetch payment details");
    } finally {
      setLoading(false);
    }
  }, [orderCode, refreshCredits]);

  useEffect(() => {
    if (!orderCode) {
      setError("No order code provided");
      setLoading(false);
      return;
    }

    fetchPaymentDetails();
  }, [orderCode, fetchPaymentDetails]);

  // Show loading state
  if (loading) {
    return (
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 safe-area-top border-b">
          <Button variant="ghost" size="sm" asChild className="shrink-0">
            <Link href="/payment" className="gap-2">
              <ArrowLeft className="size-4" />
              <span className="hidden sm:inline">Back to Payment</span>
              <span className="sm:hidden">Back</span>
            </Link>
          </Button>
          <div className="ml-auto">
            <CreditDisplay size="md" />
          </div>
        </header>

        <main className="flex-1 overflow-y-auto px-3 sm:px-4 md:px-6 py-4 sm:py-6">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full size-8 border-b-2 border-primary" />
            </div>
          </div>
        </main>
      </div>
    );
  }

  // Determine actual payment status
  const isActuallyPaid = paymentDetails?.status === "PAID";
  const isCancelled = paymentDetails?.status === "CANCELLED" || paymentDetails?.status === "PENDING";

  // Show error state
  if (error || !paymentDetails) {
    return (
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 safe-area-top border-b">
          <Button variant="ghost" size="sm" asChild className="shrink-0">
            <Link href="/payment" className="gap-2">
              <ArrowLeft className="size-4" />
              <span className="hidden sm:inline">Back to Payment</span>
              <span className="sm:hidden">Back</span>
            </Link>
          </Button>
          <div className="ml-auto">
            <CreditDisplay size="md" />
          </div>
        </header>

        <main className="flex-1 overflow-y-auto px-3 sm:px-4 md:px-6 py-4 sm:py-6">
          <div className="max-w-2xl mx-auto">
            <Card>
              <CardHeader className="text-center">
                <CardTitle className="text-destructive">Payment Error</CardTitle>
                <CardDescription>{error || "Payment details not found"}</CardDescription>
              </CardHeader>
              <CardContent className="text-center">
                <Button asChild>
                  <Link href="/payment">Return to Payment</Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background">
      <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 safe-area-top border-b">
        <Button variant="ghost" size="sm" asChild className="shrink-0">
          <Link href={isActuallyPaid ? "/" : "/payment"} className="gap-2">
            <ArrowLeft className="size-4" />
            <span className="hidden sm:inline">{isActuallyPaid ? "Back to Chat" : "Back to Payment"}</span>
            <span className="sm:hidden">Back</span>
          </Link>
        </Button>
        <div className="ml-auto">
          <CreditDisplay size="md" />
        </div>
      </header>

      <main className="flex-1 overflow-y-auto px-3 sm:px-4 md:px-6 py-4 sm:py-6">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                {isActuallyPaid ? <CheckCircle className="size-16 text-green-600" /> : <XCircle className="size-16 text-orange-500" />}
              </div>
              <CardTitle className={isActuallyPaid ? "text-green-600" : "text-orange-600"}>
                {isActuallyPaid ? "Payment Successful!" : "Payment Cancelled"}
              </CardTitle>
              <CardDescription>
                {isActuallyPaid
                  ? "Your payment was completed successfully and credits have been added to your account."
                  : "Your payment was cancelled. No charges were made to your account."}
              </CardDescription>
            </CardHeader>

            <CardContent className="space-y-6">
              <div className="bg-muted rounded-lg p-4 space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Order Code:</span>
                  <Badge variant="outline">{paymentDetails.orderCode}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Amount:</span>
                  <span className="font-medium">
                    {paymentDetails.amount.toLocaleString()} {paymentDetails.currency}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Status:</span>
                  <Badge variant={isActuallyPaid ? "default" : "secondary"}>{paymentDetails.status}</Badge>
                </div>
                {paymentDetails.paidAt && (
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">Paid At:</span>
                    <span className="text-sm">{new Date(paymentDetails.paidAt).toLocaleString()}</span>
                  </div>
                )}
              </div>

              {!isActuallyPaid && (
                <div className="text-center space-y-4">
                  <p className="text-muted-foreground">Don&apos;t worry! You can try again anytime. Your account remains unchanged.</p>

                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                    <h4 className="font-medium mb-2 text-blue-900 dark:text-blue-100">Need help with payment?</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-200">
                      If you&apos;re experiencing issues with payment, please contact us at{" "}
                      <a href="mailto:<EMAIL>" className="underline hover:no-underline">
                        <EMAIL>
                      </a>{" "}
                      or visit our{" "}
                      <a href="https://www.facebook.com/longmaba" target="_blank" rel="noopener noreferrer" className="underline hover:no-underline">
                        Facebook page
                      </a>
                      .
                    </p>
                  </div>
                </div>
              )}

              <div className="flex flex-col sm:flex-row gap-3">
                {isActuallyPaid ? (
                  <>
                    <Button asChild className="flex-1">
                      <Link href="/">Start Chatting</Link>
                    </Button>
                    <Button variant="outline" asChild className="flex-1">
                      <Link href="/payment">Buy More Credits</Link>
                    </Button>
                  </>
                ) : (
                  <>
                    <Button asChild className="flex-1">
                      <Link href="/payment" className="gap-2">
                        <RotateCcw className="size-4" />
                        Try Again
                      </Link>
                    </Button>
                    <Button variant="outline" asChild className="flex-1">
                      <Link href="/">Continue Chatting</Link>
                    </Button>
                  </>
                )}
              </div>

              <div className="text-center">
                <p className="text-xs text-muted-foreground">Payment powered by PayOS - Secure Vietnamese payment gateway</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
