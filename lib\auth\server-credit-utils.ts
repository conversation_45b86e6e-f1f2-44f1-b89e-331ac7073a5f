/**
 * Server-side credit utilities
 * Provides server-side functions for credit operations in API routes and server actions
 */

import { getServerCreditService } from "@/lib/services/credit-service";
import { getServerSession } from "@/app/(auth)/auth-server";
import { ChatSDKError } from "@/lib/errors";
import type { CreditDeductionResult, CreditAdditionResult } from "@/lib/types/credit";

/**
 * Get current user's credit balance (server-side)
 * @returns Credit balance or throws error if user not authenticated
 */
export async function getServerUserCredits(): Promise<number> {
  const session = await getServerSession();

  if (!session?.user?.id) {
    throw new ChatSDKError("unauthorized:credit", "User not authenticated");
  }

  try {
    const creditService = getServerCreditService();
    return await creditService.getUserCredits(session.user.id);
  } catch (error) {
    console.error("Failed to get server user credits:", error);
    throw new ChatSDKError("bad_request:database", "Failed to retrieve credit balance");
  }
}

/**
 * Deduct credits for current user (server-side)
 * @param amount Amount to deduct
 * @param description Description of the transaction
 * @returns Deduction result
 */
export async function deductServerUserCredits(amount: number, description: string): Promise<CreditDeductionResult> {
  const session = await getServerSession();

  if (!session?.user?.id) {
    throw new ChatSDKError("unauthorized:credit", "User not authenticated");
  }

  try {
    const creditService = getServerCreditService();
    return await creditService.deductCredit(session.user.id, amount, description);
  } catch (error) {
    console.error("Failed to deduct server user credits:", error);

    if (error instanceof ChatSDKError) {
      throw error;
    }

    throw new ChatSDKError("bad_request:database", "Failed to deduct credits");
  }
}

/**
 * Add credits for current user (server-side)
 * @param amount Amount to add
 * @param description Description of the transaction
 * @returns Addition result
 */
export async function addServerUserCredits(amount: number, description: string): Promise<CreditAdditionResult> {
  const session = await getServerSession();

  if (!session?.user?.id) {
    throw new ChatSDKError("unauthorized:credit", "User not authenticated");
  }

  try {
    const creditService = getServerCreditService();
    return await creditService.addCredits(session.user.id, amount, description);
  } catch (error) {
    console.error("Failed to add server user credits:", error);

    if (error instanceof ChatSDKError) {
      throw error;
    }

    throw new ChatSDKError("bad_request:database", "Failed to add credits");
  }
}

/**
 * Validate if current user has sufficient credits (server-side)
 * @param amount Required amount
 * @returns True if user has sufficient credits
 */
export async function validateServerUserCredits(amount: number): Promise<boolean> {
  try {
    const session = await getServerSession();

    if (!session?.user?.id) {
      return false;
    }

    const creditService = getServerCreditService();
    return await creditService.validateSufficientCredits(session.user.id, amount);
  } catch (error) {
    console.error("Failed to validate server user credits:", error);
    return false;
  }
}

/**
 * Middleware helper to check credit requirements
 * @param requiredCredits Minimum credits required
 * @returns Function that validates credits and throws error if insufficient
 */
export function requireCredits(requiredCredits: number) {
  return async (): Promise<void> => {
    const session = await getServerSession();

    if (!session?.user?.id) {
      throw new ChatSDKError("unauthorized:credit", "User not authenticated");
    }

    const hasCredits = await validateServerUserCredits(requiredCredits);

    if (!hasCredits) {
      const currentCredits = await getServerUserCredits();
      throw new ChatSDKError("forbidden:credit", `Insufficient credits: ${currentCredits} available, ${requiredCredits} required`);
    }
  };
}

/**
 * Server action wrapper that automatically deducts credits
 * @param creditCost Cost in credits for this operation
 * @param description Description for the transaction
 * @param action The actual server action to execute
 * @returns Wrapped server action
 */
export function withCreditDeduction<T extends any[], R>(creditCost: number, description: string, action: (...args: T) => Promise<R>) {
  return async (...args: T): Promise<R> => {
    // Check if user has sufficient credits first
    await requireCredits(creditCost)();

    // Deduct credits
    const deductionResult = await deductServerUserCredits(creditCost, description);

    if (!deductionResult.success) {
      throw new ChatSDKError("forbidden:credit", deductionResult.error || "Credit deduction failed");
    }

    try {
      // Execute the actual action
      return await action(...args);
    } catch (error) {
      // If the action fails, we could potentially refund credits here
      // For now, we'll log the error and let it propagate
      console.error("Action failed after credit deduction:", error);
      throw error;
    }
  };
}

/**
 * Get user session with fresh credit data
 * @returns Session with up-to-date credit information
 */
export async function getSessionWithCredits() {
  const session = await getServerSession();

  if (!session?.user?.id) {
    return null;
  }

  try {
    // Get fresh credit data
    const creditService = getServerCreditService();
    const credits = await creditService.getUserCredits(session.user.id);

    // Return session with updated credits
    return {
      ...session,
      user: {
        ...session.user,
        credits,
      },
    };
  } catch (error) {
    console.error("Failed to get session with credits:", error);
    // Return session with existing credit data
    return session;
  }
}

/**
 * Admin function to add credits to any user (server-side)
 * @param userId Target user ID
 * @param amount Amount to add
 * @param description Description of the transaction
 * @returns Addition result
 */
export async function addCreditsToUser(userId: string, amount: number, description: string): Promise<CreditAdditionResult> {
  // This would need admin authentication check
  // For now, just call the service directly
  try {
    const creditService = getServerCreditService();
    return await creditService.addCredits(userId, amount, description);
  } catch (error) {
    console.error("Failed to add credits to user:", error);

    if (error instanceof ChatSDKError) {
      throw error;
    }

    throw new ChatSDKError("bad_request:database", "Failed to add credits");
  }
}

/**
 * Admin function to get any user's credit balance (server-side)
 * @param userId Target user ID
 * @returns Credit balance
 */
export async function getUserCreditsById(userId: string): Promise<number> {
  // This would need admin authentication check
  // For now, just call the service directly
  try {
    const creditService = getServerCreditService();
    return await creditService.getUserCredits(userId);
  } catch (error) {
    console.error("Failed to get user credits by ID:", error);
    throw new ChatSDKError("bad_request:database", "Failed to retrieve credit balance");
  }
}
