"use client";

import Link from "next/link";
import { History, Coins } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface EmptyStateProps {
  className?: string;
  variant?: "no-transactions" | "no-results" | "error";
  title?: string;
  description?: string;
  actionLabel?: string;
  actionHref?: string;
  onAction?: () => void;
}

export function EmptyState({ className, variant = "no-transactions", title, description, actionLabel, actionHref, onAction }: EmptyStateProps) {
  const getDefaultContent = () => {
    switch (variant) {
      case "no-results":
        return {
          icon: History,
          title: "No transactions found",
          description: "Try adjusting your filters to see more results.",
          actionLabel: "Clear Filters",
        };
      case "error":
        return {
          icon: History,
          title: "Unable to load transactions",
          description: "There was an error loading your transaction history. Please try again.",
          actionLabel: "Retry",
        };
      case "no-transactions":
      default:
        return {
          icon: History,
          title: "No transactions yet",
          description: "Your credit transactions will appear here once you start using the application.",
          actionLabel: "Start Chatting",
          actionHref: "/",
        };
    }
  };

  const defaultContent = getDefaultContent();
  const Icon = defaultContent.icon;

  return (
    <div className={cn("text-center py-12", className)}>
      <div className="flex items-center justify-center size-16 mx-auto mb-4 rounded-full bg-muted">
        <Icon className="size-8 text-muted-foreground" />
      </div>

      <h3 className="text-lg font-medium mb-2">{title || defaultContent.title}</h3>

      <p className="text-muted-foreground mb-6 max-w-md mx-auto">{description || defaultContent.description}</p>

      {(actionLabel || defaultContent.actionLabel) && (
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          {actionHref || defaultContent.actionHref ? (
            <Button asChild>
              <Link href={actionHref || defaultContent.actionHref || "/"}>{actionLabel || defaultContent.actionLabel}</Link>
            </Button>
          ) : (
            <Button onClick={onAction}>{actionLabel || defaultContent.actionLabel}</Button>
          )}

          {variant === "no-transactions" && (
            <Button variant="outline" asChild>
              <Link href="/payment" className="gap-2">
                <Coins className="size-4" />
                Get More Credits
              </Link>
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

// Specialized variants for common use cases
export function NoTransactionsState({ className }: { className?: string }) {
  return <EmptyState className={className} variant="no-transactions" />;
}

export function NoResultsState({ className, onClearFilters }: { className?: string; onClearFilters?: () => void }) {
  return <EmptyState className={className} variant="no-results" onAction={onClearFilters} />;
}

export function ErrorState({ className, onRetry, error }: { className?: string; onRetry?: () => void; error?: string }) {
  return (
    <EmptyState
      className={className}
      variant="error"
      description={error || "There was an error loading your transaction history. Please try again."}
      onAction={onRetry}
    />
  );
}
