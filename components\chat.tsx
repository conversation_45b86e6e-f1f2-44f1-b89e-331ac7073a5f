"use client";

import type { Attachment, UIMessage } from "ai";
import { useChat } from "@ai-sdk/react";
import { useEffect, useState } from "react";
import useSWR, { useSWRConfig } from "swr";
import { ChatHeader } from "@/components/chat-header";
import type { Vote } from "@/lib/db/schema";
import { fetcher, fetchWithErrorHandlers, generateUUID } from "@/lib/utils";
import { Artifact } from "./artifact";
import { MultimodalInput } from "./multimodal-input";
import { Messages } from "./messages";
import type { VisibilityType } from "./visibility-selector";
import { useArtifactSelector } from "@/hooks/use-artifact";
import { unstable_serialize } from "swr/infinite";
import { getChatHistoryPaginationKey } from "./sidebar-history";
import { toast } from "./toast";
import { useAuth } from "@/lib/auth/auth-context";
import { trackChatStarted, trackMessageSent } from "@/lib/analytics";
import { useSearchPara<PERSON>, useRouter } from "next/navigation";
import { useChatVisibility } from "@/hooks/use-chat-visibility";
import { useAutoResume } from "@/hooks/use-auto-resume";
import { ChatSDKError } from "@/lib/errors";
import type { SupabaseSession } from "@/app/(auth)/auth";
import { Loader2 } from "lucide-react";

// Loading component for chat
function ChatLoadingState() {
  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background">
      <div className="flex-1 flex items-center justify-center">
        <div className="flex flex-col items-center gap-4 text-center">
          <Loader2 className="size-8 animate-spin text-muted-foreground" />
          <div className="space-y-2">
            <h3 className="text-lg font-medium">Loading Chat</h3>
            <p className="text-sm text-muted-foreground">Please wait while we load your credit information...</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export function Chat({
  id,
  initialMessages,
  initialChatModel,
  initialVisibilityType,
  isReadonly,
  session,
  autoResume,
}: {
  id: string;
  initialMessages: Array<UIMessage>;
  initialChatModel: string;
  initialVisibilityType: VisibilityType;
  isReadonly: boolean;
  session: SupabaseSession;
  autoResume: boolean;
}) {
  const router = useRouter();
  const { mutate } = useSWRConfig();
  const { refreshCredits, credits, isLoadingCredits } = useAuth();
  const searchParams = useSearchParams();
  const query = searchParams.get("query");
  const [hasAppendedQuery, setHasAppendedQuery] = useState(false);
  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isArtifactVisible = useArtifactSelector((state) => state.isVisible);

  // Initialize with empty values for when authentication fails
  const [shouldRender, setShouldRender] = useState(false);

  const { visibilityType } = useChatVisibility({
    chatId: id,
    initialVisibilityType,
  });

  const { messages, setMessages, handleSubmit, input, setInput, append, status, stop, reload, experimental_resume, data } = useChat({
    id,
    initialMessages,
    experimental_throttle: 100,
    sendExtraMessageFields: true,
    generateId: generateUUID,
    fetch: fetchWithErrorHandlers,
    experimental_prepareRequestBody: (body) => ({
      id,
      message: body.messages.at(-1),
      selectedChatModel: initialChatModel,
      selectedVisibilityType: visibilityType,
    }),
    onFinish: async () => {
      mutate(unstable_serialize(getChatHistoryPaginationKey));

      // Refresh credits after successful message completion
      // This ensures the UI shows the updated credit balance immediately
      try {
        console.log("[CHAT] Message completed, refreshing credits...");
        await refreshCredits();
        console.log("[CHAT] Credits refreshed successfully");
      } catch (error) {
        console.error("[CHAT] Failed to refresh credits after message completion:", error);
      }
    },
    onError: (error) => {
      if (error instanceof ChatSDKError) {
        toast({
          type: "error",
          description: error.message,
        });
      }
    },
    onResponse: () => {
      // Track message sent when we get a response
      const lastUserMessage = messages.findLast((msg) => msg.role === "user");
      if (lastUserMessage?.id) {
        // Convert message content to string for length calculation
        const content = typeof lastUserMessage.content === "string" ? lastUserMessage.content : JSON.stringify(lastUserMessage.content);

        // Use content length as message length
        const messageLength = content.length;

        trackMessageSent(id, lastUserMessage.id, messageLength);
      }

      // Optimistic credit update: immediately refresh credits when response starts
      // This provides faster UI feedback while the real-time subscription catches up
      console.log("[CHAT] Response started, triggering optimistic credit refresh...");
      refreshCredits().catch((error) => {
        console.error("[CHAT] Optimistic credit refresh failed:", error);
      });
    },
  });

  // Client-side authentication check
  useEffect(() => {
    if (!session || !session.user) {
      console.log("Chat component: No session or user, redirecting to login");
      router.push("/login");
      return;
    }

    // Check if user is a guest
    const isGuest = session.user.email && /^guest-\d+$/.test(session.user.email);
    if (isGuest) {
      console.log("Chat component: Guest user detected, redirecting to login");
      router.push("/login");
      return;
    }

    console.log("Chat component: Authenticated user, rendering chat interface");
    setShouldRender(true);
  }, [session, router]);

  // Handle query parameter
  useEffect(() => {
    if (query && !hasAppendedQuery && shouldRender && !isLoadingCredits) {
      append({
        role: "user",
        content: query,
      });

      setHasAppendedQuery(true);
      window.history.replaceState({}, "", `/chat/${id}`);
    }
  }, [query, append, hasAppendedQuery, id, shouldRender, isLoadingCredits]);

  // SWR hook for votes
  const { data: votes } = useSWR<Array<Vote>>(shouldRender && messages.length >= 2 ? `/api/vote?chatId=${id}` : null, fetcher);

  // Auto resume effect
  useAutoResume({
    autoResume,
    initialMessages,
    experimental_resume,
    data,
    setMessages,
  });

  // Track chat started when component mounts
  useEffect(() => {
    trackChatStarted(id, initialChatModel, visibilityType);
  }, [id, initialChatModel, visibilityType]);

  // Client-side timeout recovery for hanging requests
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;

    if (status === "submitted") {
      // Set a timeout to recover from hanging requests (30 seconds)
      timeoutId = setTimeout(() => {
        console.warn("Request appears to be hanging, attempting recovery");
        // Force status reset
        stop();
        // Show error to user
        toast({
          type: "error",
          description: "The response is taking too long. Please try again.",
        });
      }, 30000);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [status, stop]);

  // If authentication fails, don't render anything
  if (!shouldRender) {
    return null;
  }

  // Show loading state while credits are loading
  if (isLoadingCredits) {
    return <ChatLoadingState />;
  }

  return (
    <>
      <div className="flex flex-col min-w-0 h-dvh bg-background">
        <ChatHeader
          chatId={id}
          selectedModelId={initialChatModel}
          selectedVisibilityType={initialVisibilityType}
          isReadonly={isReadonly}
          session={session}
        />

        <Messages
          chatId={id}
          status={status}
          votes={votes}
          messages={messages}
          setMessages={setMessages}
          reload={reload}
          isReadonly={isReadonly}
          isArtifactVisible={isArtifactVisible}
        />

        <form className="flex mx-auto px-4 bg-background pb-4 md:pb-6 gap-2 w-full md:max-w-3xl">
          {!isReadonly && (
            <MultimodalInput
              chatId={id}
              input={input}
              setInput={setInput}
              handleSubmit={handleSubmit}
              status={status}
              stop={stop}
              attachments={attachments}
              setAttachments={setAttachments}
              messages={messages}
              setMessages={setMessages}
              append={append}
              selectedVisibilityType={visibilityType}
            />
          )}
        </form>
      </div>

      <Artifact
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={handleSubmit}
        status={status}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messages}
        setMessages={setMessages}
        reload={reload}
        votes={votes}
        isReadonly={isReadonly}
        selectedVisibilityType={visibilityType}
      />
    </>
  );
}
