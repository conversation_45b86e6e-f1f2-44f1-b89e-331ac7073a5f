import { tool } from "ai";
import { z } from "zod";
import { getDaiVan } from "@/lib/tuvibattu/ultilities/daivan";
import { checkBatTu } from "@/lib/tuvibattu/app";

/**
 * A simple test tool to verify that tool calling is working
 * This tool will echo back the input with a timestamp
 */
export const getDaiVanTool = tool({
  description:
    "Use this tool to get information about a person's Đại Vận. Will need the user's birth place for this to derive Timezone from it (City and country where they were born). Transform the input into the expected format",
  parameters: z.object({
    birthDate: z
      .object({
        day: z.number().describe("The day of the birth date"),
        month: z.number().describe("The month of the birth date"),
        year: z.number().describe("The year of the birth date"),
      })
      .describe("The birth date of the person in object format {day: number, month: number, year: number}"),
    hour: z.number().describe("The birth hour of the person, should be in 24 hours format"),
    gender: z.string().describe("The gender of the person, should be either <PERSON> or <PERSON><PERSON> (Vietnamese)"),
    timezone: z.string().describe("The timezone of the city where the person was born, in format like 'Asia/Ho_Chi_Minh'"),
  }),
  execute: async ({ birthDate, hour, gender, timezone }) => {
    const daiVan = getDaiVan(birthDate, hour, gender, timezone);
    const batTu = checkBatTu(birthDate, hour, gender);
    return {
      daiVan,
      batTu,
    };
  },
});
