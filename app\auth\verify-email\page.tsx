"use client";

import { Suspense, useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { toast } from "@/components/toast";
import { Button } from "@/components/ui/button";
import { StyledEnvelopeIcon, StyledLoaderIcon } from "@/components/styled-icons";
import { resendVerificationEmail } from "@/app/auth/actions";
import { useLocalStorage } from "usehooks-ts";

// Constants
const COOLDOWN_SECONDS = 44; // Match the Supabase security restriction
const STORAGE_KEY = "email-verification-cooldown";

// Main component that uses useSearchParams
function VerifyEmailContent() {
  const searchParams = useSearchParams();
  const email = searchParams.get("email");
  const [isResending, setIsResending] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);

  // Use localStorage to persist countdown between refreshes
  const [cooldownData, setCooldownData] = useLocalStorage<{
    endTime: number;
    email: string | null;
  }>(STORAGE_KEY, { endTime: 0, email: null });

  // Local countdown state for UI updates
  const [countdown, setCountdown] = useState(0);

  // Initialize countdown from localStorage on page load
  useEffect(() => {
    // Only apply cooldown if it's for the same email
    if (cooldownData.email === email && cooldownData.endTime > Date.now()) {
      const remainingSeconds = Math.ceil((cooldownData.endTime - Date.now()) / 1000);
      setCountdown(remainingSeconds > 0 ? remainingSeconds : 0);
    }
  }, [cooldownData, email]);

  // Countdown timer effect
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const handleResendEmail = async () => {
    if (!email || isResending || countdown > 0) return;

    setIsResending(true);

    try {
      const result = await resendVerificationEmail(email);

      if (!result.ok) {
        console.error("Error resending verification email:", result.error);

        // Check for the specific security timeout error
        if (result.error?.includes("security purposes") && result.error.includes("seconds")) {
          // Extract seconds from error message if possible
          const secondsMatch = result.error.match(/after (\d+) seconds/);
          const waitSeconds = secondsMatch ? Number.parseInt(secondsMatch[1]) : COOLDOWN_SECONDS;

          // Set the countdown
          setCountdown(waitSeconds);

          // Store in localStorage with end time
          setCooldownData({
            endTime: Date.now() + waitSeconds * 1000,
            email: email,
          });

          toast({
            type: "error",
            description: `Please wait ${waitSeconds} seconds before requesting another verification email.`,
          });
        } else {
          toast({
            type: "error",
            description: "Failed to resend verification email. Please try again.",
          });
        }
      } else {
        setResendSuccess(true);

        // Set the countdown
        setCountdown(COOLDOWN_SECONDS);

        // Store in localStorage with end time
        setCooldownData({
          endTime: Date.now() + COOLDOWN_SECONDS * 1000,
          email: email,
        });

        toast({
          type: "success",
          description: "Verification email resent. Please check your inbox.",
        });
      }
    } catch (error) {
      console.error("Error resending verification email:", error);
      toast({
        type: "error",
        description: "An unexpected error occurred. Please try again.",
      });
    } finally {
      setIsResending(false);
    }
  };

  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-8 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h1 className="app-title dark:text-zinc-50">Bát Tự Master V</h1>
          <h2 className="text-lg text-gray-700 dark:text-zinc-300 mb-4">Phán về số mệnh, cuộc đời, tình duyên, công việc</h2>

          <div className="flex items-center justify-center size-12 rounded-full bg-amber-100 dark:bg-amber-900 mb-4">
            <StyledEnvelopeIcon className="size-6 text-amber-600 dark:text-amber-300" />
          </div>

          <h3 className="text-xl font-semibold dark:text-zinc-50">Check your email</h3>

          {email ? (
            <p className="text-sm text-gray-500 dark:text-zinc-400 mt-1">
              We&apos;ve sent a verification link to <span className="font-medium text-gray-700 dark:text-zinc-300">{email}</span>
            </p>
          ) : (
            <p className="text-sm text-gray-500 dark:text-zinc-400 mt-1">We&apos;ve sent a verification link to your email address</p>
          )}
        </div>

        <div className="flex flex-col gap-4 px-4 sm:px-16">
          <div className="bg-muted rounded-lg p-4">
            <h4 className="font-medium text-sm mb-2">What to do next:</h4>
            <ol className="text-sm text-gray-600 dark:text-zinc-400 space-y-2 list-decimal pl-4">
              <li>Check your email inbox for the verification link</li>
              <li>Click the link in the email to verify your account</li>
              <li>After verification, you&apos;ll be able to sign in to your account</li>
            </ol>
          </div>

          <div className="mt-2">
            <Button
              onClick={handleResendEmail}
              disabled={isResending || countdown > 0}
              className="w-full"
              variant={countdown > 0 ? "outline" : "default"}
            >
              {isResending ? (
                <>
                  <StyledLoaderIcon className="mr-2 size-4 animate-spin" />
                  Sending...
                </>
              ) : countdown > 0 ? (
                `Resend email (${countdown}s)`
              ) : (
                "Resend verification email"
              )}
            </Button>
          </div>

          <div className="mt-2 text-center">
            <p className="text-sm text-gray-500 dark:text-zinc-400">
              Already verified?{" "}
              <Link href="/login" className="font-semibold text-gray-800 hover:underline dark:text-zinc-200">
                Sign in
              </Link>
            </p>
          </div>

          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500 dark:text-zinc-500">
              If you don&apos;t see the email, check your spam folder or make sure you entered the correct email address.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// Loading fallback component
function VerifyEmailLoading() {
  return (
    <div className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background">
      <div className="w-full max-w-md overflow-hidden rounded-2xl gap-8 flex flex-col">
        <div className="flex flex-col items-center justify-center gap-2 px-4 text-center sm:px-16">
          <h1 className="app-title dark:text-zinc-50">Bát Tự Master V</h1>
          <h2 className="text-lg text-gray-700 dark:text-zinc-300 mb-4">Phán về số mệnh, cuộc đời, tình duyên, công việc</h2>

          <div className="flex items-center justify-center size-12 rounded-full bg-amber-100 dark:bg-amber-900 mb-4">
            <StyledLoaderIcon className="size-6 text-amber-600 dark:text-amber-300 animate-spin" />
          </div>

          <h3 className="text-xl font-semibold dark:text-zinc-50">Loading...</h3>
          <p className="text-sm text-gray-500 dark:text-zinc-400 mt-1">Please wait while we prepare your verification page</p>
        </div>
      </div>
    </div>
  );
}

// Default export with Suspense boundary
export default function VerifyEmailPage() {
  return (
    <Suspense fallback={<VerifyEmailLoading />}>
      <VerifyEmailContent />
    </Suspense>
  );
}
