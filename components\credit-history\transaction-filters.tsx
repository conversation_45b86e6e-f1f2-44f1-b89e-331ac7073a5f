"use client";

import { <PERSON><PERSON><PERSON>, Filter, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface TransactionFiltersProps {
  dateFilter: string;
  typeFilter: string;
  onDateFilterChange: (value: string) => void;
  onTypeFilterChange: (value: string) => void;
  onClearFilters: () => void;
  className?: string;
}

const DATE_FILTER_OPTIONS = [
  { value: "all", label: "All Time" },
  { value: "today", label: "Today" },
  { value: "week", label: "Last 7 Days" },
  { value: "month", label: "Last 30 Days" },
  { value: "quarter", label: "Last 90 Days" },
];

const TYPE_FILTER_OPTIONS = [
  { value: "all", label: "All Types" },
  { value: "deduction", label: "Deductions" },
  { value: "addition", label: "Additions" },
  { value: "admin_adjustment", label: "Admin Adjustments" },
];

export function TransactionFilters({
  dateFilter,
  typeFilter,
  onDateFilterChange,
  onTypeFilterChange,
  onClearFilters,
  className,
}: TransactionFiltersProps) {
  const hasActiveFilters = dateFilter !== "all" || typeFilter !== "all";

  const getFilterLabel = (value: string, options: typeof DATE_FILTER_OPTIONS) => {
    return options.find((option) => option.value === value)?.label || value;
  };

  return (
    <Card className={cn("", className)}>
      <CardHeader className="pb-3 sm:pb-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <CardTitle className="text-base sm:text-lg flex items-center gap-2">
            <Filter className="size-4 sm:size-5" />
            Filter Transactions
          </CardTitle>
          {hasActiveFilters && (
            <Button variant="outline" size="sm" onClick={onClearFilters} className="gap-2 self-start sm:self-auto">
              <X className="size-4" />
              <span className="hidden sm:inline">Clear Filters</span>
              <span className="sm:hidden">Clear</span>
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Filter Controls */}
        <div className="grid gap-4 md:grid-cols-2">
          {/* Date Range Filter */}
          <div className="space-y-2">
            <label htmlFor="date-filter" className="text-sm font-medium">
              Date Range
            </label>
            <Select value={dateFilter} onValueChange={onDateFilterChange}>
              <SelectTrigger id="date-filter" className="gap-2">
                <CalendarIcon className="size-4" />
                <SelectValue placeholder="Select date range" />
              </SelectTrigger>
              <SelectContent>
                {DATE_FILTER_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Transaction Type Filter */}
          <div className="space-y-2">
            <label htmlFor="type-filter" className="text-sm font-medium">
              Transaction Type
            </label>
            <Select value={typeFilter} onValueChange={onTypeFilterChange}>
              <SelectTrigger id="type-filter" className="gap-2">
                <Filter className="size-4" />
                <SelectValue placeholder="Select transaction type" />
              </SelectTrigger>
              <SelectContent>
                {TYPE_FILTER_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Active Filters Display */}
        {hasActiveFilters && (
          <div className="flex flex-wrap gap-2 pt-2 border-t">
            <span className="text-sm text-muted-foreground">Active filters:</span>
            {dateFilter !== "all" && (
              <Badge variant="secondary" className="gap-1">
                <CalendarIcon className="size-3" />
                {getFilterLabel(dateFilter, DATE_FILTER_OPTIONS)}
                <button
                  type="button"
                  onClick={() => onDateFilterChange("all")}
                  className="ml-1 hover:bg-muted rounded-full p-0.5"
                  aria-label="Clear date filter"
                >
                  <X className="size-3" />
                </button>
              </Badge>
            )}
            {typeFilter !== "all" && (
              <Badge variant="secondary" className="gap-1">
                <Filter className="size-3" />
                {getFilterLabel(typeFilter, TYPE_FILTER_OPTIONS)}
                <button
                  type="button"
                  onClick={() => onTypeFilterChange("all")}
                  className="ml-1 hover:bg-muted rounded-full p-0.5"
                  aria-label="Clear type filter"
                >
                  <X className="size-3" />
                </button>
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Compact version for smaller spaces
export function CompactTransactionFilters({
  dateFilter,
  typeFilter,
  onDateFilterChange,
  onTypeFilterChange,
  onClearFilters,
  className,
}: TransactionFiltersProps) {
  const hasActiveFilters = dateFilter !== "all" || typeFilter !== "all";

  return (
    <div className={cn("flex flex-wrap gap-3 items-center", className)}>
      <div className="flex items-center gap-2">
        <Filter className="size-4 text-muted-foreground" />
        <span className="text-sm font-medium">Filters:</span>
      </div>

      <Select value={dateFilter} onValueChange={onDateFilterChange}>
        <SelectTrigger className="w-auto min-w-[120px]">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {DATE_FILTER_OPTIONS.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      <Select value={typeFilter} onValueChange={onTypeFilterChange}>
        <SelectTrigger className="w-auto min-w-[120px]">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {TYPE_FILTER_OPTIONS.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>

      {hasActiveFilters && (
        <Button variant="ghost" size="sm" onClick={onClearFilters} className="gap-1 text-muted-foreground hover:text-foreground">
          <X className="size-3" />
          Clear
        </Button>
      )}
    </div>
  );
}
