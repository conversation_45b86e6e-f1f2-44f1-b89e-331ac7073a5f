/**
 * TypeScript type definitions for admin operations
 * Centralized types for admin dashboard, credit operations, and audit logging
 */

// Base admin user data interface
export interface AdminUserData {
  id: string;
  email: string;
  credits: number;
  total_credits_used: number;
}

// User list response with pagination
export interface AdminUserListResponse {
  users: AdminUserData[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

// User search and filter options
export interface UserSearchOptions {
  search?: string;
  minCredits?: number;
  maxCredits?: number;
}

// Credit analytics data
export interface CreditAnalytics {
  total_credits_consumed: number;
  total_users: number;
  average_credits_per_user: number;
  most_active_users: Array<{
    user_id: string;
    email: string;
    credits_used: number;
  }>;
  usage_trends: Array<{
    date: string;
    credits_consumed: number;
  }>;
}

// Individual credit adjustment request
export interface CreditAdjustmentRequest {
  user_id: string;
  amount: number; // Positive for add, negative for deduct
  reason: string;
  operation_type: "add" | "deduct" | "set";
}

// Individual credit adjustment response
export interface CreditAdjustmentResponse {
  success: boolean;
  user: {
    id: string;
    email: string;
    previous_credits: number;
    new_credits: number;
    adjustment_amount: number;
  };
  operation: {
    type: string;
    amount: number;
    reason: string;
  };
}

// Bulk credit operation request
export interface BulkCreditRequest {
  user_ids: string[];
  amount: number;
  reason: string;
  operation_type: "add" | "deduct" | "set";
}

// Bulk operation result for individual user
export interface BulkUserResult {
  user_id: string;
  email: string;
  success: boolean;
  previous_credits: number;
  new_credits: number;
  error?: string;
}

// Bulk credit operation response
export interface BulkOperationResult {
  success: boolean;
  processed: number;
  failed: number;
  results: BulkUserResult[];
  operation: {
    type: string;
    amount: number;
    reason: string;
    total_users: number;
  };
}

// Audit log entry
export interface AuditLogEntry {
  id: string;
  user_id: string;
  amount: number;
  description: string;
  transaction_type: string;
  created_at: string;
  user_email?: string;
}

// Audit log response with pagination
export interface AuditLogResponse {
  logs: AuditLogEntry[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
  filters: {
    user_id?: string;
    start_date?: string;
    end_date?: string;
    transaction_type?: string;
  };
}

// Audit log filter options
export interface AuditLogFilters {
  search?: string;
  transaction_type?: string;
  start_date?: string;
  end_date?: string;
  user_id?: string;
}

// Audit statistics
export interface AuditStatistics {
  total_transactions: number;
  admin_adjustments: number;
  recent_activity_24h: number;
  top_users: Array<{
    user_id: string;
    email: string;
    transaction_count: number;
  }>;
}

// Admin operation types
export type AdminOperationType = "add" | "deduct" | "set";
export type TransactionType = "deduction" | "addition" | "admin_adjustment";

// Admin action types for audit logging
export type AdminActionType = 
  | "credit_adjustment"
  | "bulk_credit_operation"
  | "user_search"
  | "audit_log_access"
  | "analytics_access";

// Admin permission levels (for future expansion)
export type AdminPermissionLevel = "read" | "write" | "admin";

// Admin configuration
export interface AdminConfig {
  adminEmails: readonly string[];
  isEmailAdmin: (email: string | null | undefined) => boolean;
  normalizeEmail: (email: string) => string;
  validateEmail: (email: string) => boolean;
}

// Admin status result
export interface AdminStatusResult {
  isAdmin: boolean;
  error?: string;
}

// Rate limiting configuration for admin operations
export interface AdminRateLimit {
  maxRequests: number;
  windowMs: number;
  skipSuccessfulRequests?: boolean;
}

// Admin operation validation result
export interface AdminValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

// Admin dashboard state
export interface AdminDashboardState {
  userListData: AdminUserListResponse | null;
  analyticsData: CreditAnalytics | null;
  isLoadingUsers: boolean;
  isLoadingAnalytics: boolean;
  userListError: string | null;
  analyticsError: string | null;
  currentSearchOptions: UserSearchOptions;
  currentPage: number;
}

// Admin component props
export interface AdminComponentProps {
  isLoading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
}

// Bulk operation preview data
export interface BulkOperationPreview {
  usersAffected: number;
  totalCreditsChange: number;
  averageCreditsPerUser: number;
  estimatedDuration?: number;
}

// Admin error types
export type AdminErrorType = 
  | "unauthorized"
  | "validation_failed"
  | "operation_failed"
  | "rate_limit_exceeded"
  | "user_not_found"
  | "insufficient_credits"
  | "database_error";

// Admin error with context
export interface AdminError {
  type: AdminErrorType;
  message: string;
  context?: Record<string, any>;
  timestamp: string;
}

// Admin operation context for logging
export interface AdminOperationContext {
  adminUserId: string;
  adminEmail: string;
  operation: AdminActionType;
  targetUserId?: string;
  targetUserEmail?: string;
  metadata?: Record<string, any>;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
}

// Admin security event
export interface AdminSecurityEvent {
  type: "access_granted" | "access_denied" | "suspicious_activity";
  adminEmail: string;
  operation: string;
  details: string;
  timestamp: string;
  ipAddress?: string;
}
