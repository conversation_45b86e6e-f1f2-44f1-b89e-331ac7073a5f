/**
 * User details view for admin dashboard
 * Shows detailed user information and credit history
 */

"use client";

import { useState, useEffect, useCallback } from "react";
import { User, CreditCard, Activity, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";
import type { AdminUserData } from "@/lib/services/admin-service";

interface UserDetailsViewProps {
  user: AdminUserData;
  isOpen: boolean;
  onClose: () => void;
}

interface CreditTransaction {
  id: string;
  amount: number;
  description: string;
  transactionType: string;
  createdAt: string;
}

interface CreditHistory {
  transactions: CreditTransaction[];
  total: number;
  hasMore: boolean;
}

export function UserDetailsView({ user, isOpen, onClose }: UserDetailsViewProps) {
  const [creditHistory, setCreditHistory] = useState<CreditHistory | null>(null);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);

  const fetchCreditHistory = useCallback(async () => {
    setIsLoadingHistory(true);
    try {
      const response = await fetch(`/api/admin/credits?user_id=${user.id}&limit=10`);
      if (!response.ok) {
        throw new Error("Failed to fetch credit history");
      }
      const data = await response.json();
      setCreditHistory(data);
    } catch (error) {
      console.error("Failed to fetch credit history:", error);
      toast.error("Failed to load credit history");
    } finally {
      setIsLoadingHistory(false);
    }
  }, [user.id]);

  // Fetch credit history when component opens
  useEffect(() => {
    if (isOpen && user) {
      fetchCreditHistory();
    }
  }, [isOpen, user, fetchCreditHistory]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getTransactionBadgeVariant = (amount: number) => {
    if (amount > 0) return "default"; // Credit added
    if (amount < 0) return "destructive"; // Credit deducted
    return "secondary"; // Neutral
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black/50 z-50" />

      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
          <CardContent className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <User className="size-5" />
                <h2 className="text-lg font-semibold">User Details</h2>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="size-4" />
              </Button>
            </div>

            <div className="space-y-6">
              {/* User Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">User Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Email</p>
                      <p className="font-medium">{user.email}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">User ID</p>
                      <p className="font-mono text-sm">{user.id}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Current Credits</p>
                      <Badge variant="outline" className="text-lg px-3 py-1">
                        {user.credits} credits
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">Total Credits Used</p>
                      <p className="font-medium">{user.total_credits_used}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Credit History */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base flex items-center gap-2">
                    <Activity className="size-4" />
                    Recent Credit Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingHistory ? (
                    <div className="space-y-3">
                      {Array.from({ length: 3 }, (_, i) => (
                        <div key={`skeleton-${Date.now()}-${i}`} className="flex items-center justify-between p-3 border rounded">
                          <div className="space-y-1">
                            <Skeleton className="size-48" />
                            <Skeleton className="size-32" />
                          </div>
                          <Skeleton className="h-6 w-16" />
                        </div>
                      ))}
                    </div>
                  ) : creditHistory?.transactions.length ? (
                    <div className="space-y-3">
                      {creditHistory.transactions.map((transaction) => (
                        <div key={transaction.id} className="flex items-center justify-between p-3 border rounded">
                          <div>
                            <p className="font-medium text-sm">{transaction.description}</p>
                            <p className="text-xs text-muted-foreground">{formatDate(transaction.createdAt)}</p>
                          </div>
                          <Badge variant={getTransactionBadgeVariant(transaction.amount)}>
                            {transaction.amount > 0 ? "+" : ""}
                            {transaction.amount}
                          </Badge>
                        </div>
                      ))}

                      {creditHistory.hasMore && (
                        <div className="text-center pt-2">
                          <p className="text-sm text-muted-foreground">Showing recent {creditHistory.transactions.length} transactions</p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <CreditCard className="size-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm text-muted-foreground">No credit transactions found</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-base">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        toast.success("Credit adjustment form will open");
                        onClose();
                      }}
                    >
                      <CreditCard className="size-4 mr-2" />
                      Adjust Credits
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        navigator.clipboard.writeText(user.id);
                        toast.success("User ID copied to clipboard");
                      }}
                    >
                      Copy User ID
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Footer */}
            <div className="flex justify-end pt-6 border-t">
              <Button onClick={onClose}>Close</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </>
  );
}
