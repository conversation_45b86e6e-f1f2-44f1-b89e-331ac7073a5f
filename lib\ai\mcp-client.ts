import { experimental_createMCPClient as createMCPClient } from "ai";
import { z } from "zod";

// Debug configuration
// Disable debug logging by default in production
const DEBUG_MCP = process.env.DEBUG_MCP === "true"; // Disabled by default
const DEBUG_MCP_VERBOSE = false; // Always disabled in production

/**
 * Debug logger for MCP operations
 * Only logs when DEBUG_MCP is explicitly enabled
 */
export const mcpLogger = {
  info: (message: string, data?: any) => {
    if (DEBUG_MCP) {
      // Only log in development or when explicitly enabled
      if (process.env.NODE_ENV === "development") {
        console.log(`[MCP] ${message}`);
        if (DEBUG_MCP_VERBOSE && data) {
          console.log(JSON.stringify(data, null, 2));
        }
      }
    }
  },
  error: (message: string, error?: any) => {
    if (DEBUG_MCP) {
      // Only log in development or when explicitly enabled
      if (process.env.NODE_ENV === "development") {
        console.error(`[MCP ERROR] ${message}`);
        if (error) {
          console.error(error);
        }
      }
    }
  },
};

/**
 * Creates an MCP client to connect to a remote MCP server
 * @param url The URL of the MCP server
 * @param apiKey Optional API key for authentication
 * @returns An MCP client instance
 */
export async function createMCPRemoteClient(url: string, apiKey?: string) {
  mcpLogger.info(`Creating MCP client for URL: ${url}`);

  const headers: Record<string, string> = {};

  if (apiKey) {
    headers.Authorization = `Bearer ${apiKey}`;
    mcpLogger.info("Using API key for authentication");
  } else {
    mcpLogger.info("No API key provided for authentication");
  }

  try {
    // Create a promise that will reject after a timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`MCP client connection timed out after 30 seconds`)), 30000);
    });

    // Create the MCP client promise
    const clientPromise = createMCPClient({
      transport: {
        type: "sse",
        url,
        headers,
      },
    });

    // Race the two promises
    const client = (await Promise.race([clientPromise, timeoutPromise])) as Awaited<ReturnType<typeof createMCPClient>>;

    mcpLogger.info("MCP client created successfully");
    return client;
  } catch (error) {
    mcpLogger.error("Failed to create MCP client", error);
    throw error;
  }
}

/**
 * Creates an MCP client with predefined tool schemas
 * @param url The URL of the MCP server
 * @param apiKey Optional API key for authentication
 * @returns An MCP client with tools
 */
export async function createMCPClientWithTools(url: string, apiKey?: string) {
  mcpLogger.info("Creating MCP client with tools");
  const mcpClient = await createMCPRemoteClient(url, apiKey);

  try {
    // Try auto-discovery first with timeout
    mcpLogger.info("Attempting auto-discovery of tools");

    // Create a promise that will reject after a timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`MCP tools auto-discovery timed out after 30 seconds`)), 30000);
    });

    // Create the tools discovery promise
    const toolsPromise = mcpClient.tools();

    // Race the two promises
    const tools = (await Promise.race([toolsPromise, timeoutPromise])) as Awaited<ReturnType<typeof mcpClient.tools>>;

    const toolNames = Object.keys(tools);
    if (toolNames.length > 0) {
      mcpLogger.info(`Successfully discovered ${toolNames.length} tools via auto-discovery: ${toolNames.join(", ")}`);
      return { mcpClient, tools };
    }

    mcpLogger.info("Auto-discovery found no tools, trying with explicit schemas");

    // If auto-discovery returns no tools, try with explicit schemas
    // You can customize these schemas based on what your MCP server provides
    const schemas = {
      get_dai_van: {
        parameters: z.object({
          JSON: z
            .string()
            .describe(
              'The body should be in this format:\n{\n   "birthDate": { "day": 13, "month": 10, "year": 1992 } // Normal Calendar Birthdate\n, \n    "hour": 12, //Birth hour \n    "gender": "Nữ" // "Nam" or "Nữ",\n    "timezone": "Asia/Ho_Chi_Minh"\n}\n\nIMPORTANT: MUST BE A JSON OBJECT, NOT JSON STRING'
            ),
        }),
      },
      check_bat_tu: {
        parameters: z.object({
          JSON: z
            .string()
            .describe(
              'The body should be in this format:\n{\n    "birthDate": { "day": 13, "month": 10, "year": 1992 } // Normal Calendar Birthdate\n, \n    "hour": 12, //Birth hour \n    "gender": "Nữ" // "Nam" or "Nữ"\n}'
            ),
        }),
      },
      check_detailed_personality: {
        parameters: z.object({
          JSON: z
            .string()
            .describe(
              'The body should be in this format:\n{\n    "birthDate": { "day": 13, "month": 10, "year": 1992 } // Normal Calendar Birthdate\n, \n    "hour": 12, //Birth hour \n    "gender": "Nữ" // "Nam" or "Nữ"\n}'
            ),
        }),
      },
      check_personality: {
        parameters: z.object({
          JSON: z
            .string()
            .describe(
              'The body should be in this format:\n{\n    "birthDate": { "day": 13, "month": 10, "year": 1992 } // Normal Calendar Birthdate\n, \n    "hour": 12, //Birth hour \n    "gender": "Nữ" // "Nam" or "Nữ"\n}'
            ),
        }),
      },
      check_lunar_date: {
        parameters: z.object({
          JSON: z
            .string()
            .describe(
              'The body should be in this format:\n{\n    "birthDate": { "day": 13, "month": 10, "year": 1992 } // Normal Calendar Birthdate\n, \n    "hour": 12, //Birth hour \n    "gender": "Nữ" // "Nam" or "Nữ"\n}'
            ),
        }),
      },
    };

    mcpLogger.info("Using explicit schemas as fallback", schemas);

    // Create a promise that will reject after a timeout
    const schemaTimeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error(`MCP tools schema discovery timed out after 30 seconds`)), 30000);
    });

    // Create the schema-based tools discovery promise
    const schemaToolsPromise = mcpClient.tools({
      schemas,
    });

    // Race the two promises
    const toolsWithSchemas = (await Promise.race([schemaToolsPromise, schemaTimeoutPromise])) as Awaited<ReturnType<typeof mcpClient.tools>>;

    const schemaToolNames = Object.keys(toolsWithSchemas);
    mcpLogger.info(`Found ${schemaToolNames.length} tools with explicit schemas: ${schemaToolNames.join(", ")}`);
    return { mcpClient, tools: toolsWithSchemas };
  } catch (error) {
    mcpLogger.error("Error getting tools from MCP server", error);
    // Return empty tools object to avoid breaking the application
    return { mcpClient, tools: {} };
  }
}

/**
 * Closes an MCP client
 * @param mcpClient The MCP client to close
 */
export async function closeMCPClient(mcpClient: Awaited<ReturnType<typeof createMCPClient>>) {
  mcpLogger.info("Closing MCP client");
  try {
    await mcpClient.close();
    mcpLogger.info("MCP client closed successfully");
  } catch (error) {
    mcpLogger.error("Error closing MCP client", error);
  }
}
