/**
 * Admin configuration and email validation utilities
 * Provides secure admin access control based on email whitelist
 */

/**
 * Hardcoded admin email addresses
 * These emails have administrative privileges in the application
 */
const HARDCODED_ADMIN_EMAILS = ["<EMAIL>", "<EMAIL>"] as const;

/**
 * Admin configuration interface
 */
export interface AdminConfig {
  adminEmails: readonly string[];
  isEmailAdmin: (email: string | null | undefined) => boolean;
  normalizeEmail: (email: string) => string;
  validateEmail: (email: string) => boolean;
}

/**
 * Normalize email address for consistent comparison
 * - Convert to lowercase
 * - Trim whitespace
 * - Basic validation
 */
export function normalizeEmail(email: string): string {
  if (!email || typeof email !== "string") {
    return "";
  }

  return email.trim().toLowerCase();
}

/**
 * Basic email format validation
 * Uses a simple regex for basic email validation
 */
export function validateEmail(email: string): boolean {
  if (!email || typeof email !== "string") {
    return false;
  }

  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.trim());
}

/**
 * Get admin emails from environment variable or use hardcoded list
 * Environment variable format: "<EMAIL>,<EMAIL>"
 */
function getAdminEmailsFromEnv(): string[] {
  const envEmails = process.env.ADMIN_EMAILS;

  if (!envEmails || typeof envEmails !== "string") {
    return [...HARDCODED_ADMIN_EMAILS];
  }

  // Parse comma-separated emails and normalize them
  const emails = envEmails
    .split(",")
    .map((email) => normalizeEmail(email))
    .filter((email) => validateEmail(email));

  // If no valid emails found in env, fall back to hardcoded list
  if (emails.length === 0) {
    console.warn("ADMIN_EMAILS environment variable contains no valid emails, using hardcoded list");
    return [...HARDCODED_ADMIN_EMAILS];
  }

  return emails;
}

/**
 * Check if an email address is an admin email
 * Performs case-insensitive comparison with normalization
 */
export function isEmailAdmin(email: string | null | undefined): boolean {
  if (!email) {
    return false;
  }

  const normalizedEmail = normalizeEmail(email);

  if (!validateEmail(normalizedEmail)) {
    return false;
  }

  const adminEmails = getAdminEmailsFromEnv();
  return adminEmails.some((adminEmail) => normalizeEmail(adminEmail) === normalizedEmail);
}

/**
 * Get the current admin configuration
 * This is the main export that provides all admin utilities
 */
export function getAdminConfig(): AdminConfig {
  const adminEmails = getAdminEmailsFromEnv();

  return {
    adminEmails: adminEmails as readonly string[],
    isEmailAdmin,
    normalizeEmail,
    validateEmail,
  };
}

/**
 * Admin configuration singleton
 * Use this for consistent admin configuration across the application
 */
export const adminConfig = getAdminConfig();

/**
 * Type guard to check if a user has admin privileges
 * Can be used with user objects that have an email property
 */
export function isUserAdmin(user: { email?: string | null } | null | undefined): boolean {
  return isEmailAdmin(user?.email);
}

/**
 * Get admin emails for display purposes (without exposing internal logic)
 * Returns a readonly array of admin emails
 */
export function getAdminEmails(): readonly string[] {
  return getAdminConfig().adminEmails;
}

/**
 * Development helper to check admin configuration
 * Only available in development mode
 */
export function debugAdminConfig(): void {
  if (process.env.NODE_ENV !== "development") {
    return;
  }

  const config = getAdminConfig();
  console.log("Admin Configuration Debug:");
  console.log("- Admin emails count:", config.adminEmails.length);
  console.log("- Using environment override:", !!process.env.ADMIN_EMAILS);
  console.log("- Admin emails:", config.adminEmails);
}
