import { tool } from 'ai';
import { z } from 'zod';

/**
 * A simple test tool to verify that tool calling is working
 * This tool will echo back the input with a timestamp
 */
export const testTool = tool({
  description: 'A test tool that echoes back the input with a timestamp',
  parameters: z.object({
    input: z.string().describe('The input to echo back'),
  }),
  execute: async ({ input }) => {
    console.log(`Test tool called with input: ${input}`);
    
    return {
      message: `Echo: ${input}`,
      timestamp: new Date().toISOString(),
    };
  },
});
