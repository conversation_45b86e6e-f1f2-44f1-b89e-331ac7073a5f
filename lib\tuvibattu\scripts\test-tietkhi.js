// Test script for tietkhi.js timezone fix
const { getSolarTermDate } = require("../ultilities/tietkhi");

// Test with different timezones for the same solar term
console.log("=== Testing timezone adjustments for solar term dates ===");

// Test for <PERSON>ậ<PERSON> xuân (Beginning of Spring) in 2023
const year = 2023;
const termName = "Lập xuân";

// Test with different timezones
const timezones = [
  "UTC", // +0
  "Asia/Shanghai", // +8 (China)
  "Asia/Ho_Chi_Minh", // +7 (Vietnam)
  "Asia/Tokyo", // +9 (Japan)
  "Europe/London", // +0/+1 (UK)
  "America/New_York", // -5/-4 (US Eastern)
];

console.log(`\nSolar Term: ${termName} ${year}`);
console.log("-----------------------------------");

timezones.forEach((timezone) => {
  const date = getSolarTermDate(year, termName, timezone);
  // Display the date in its local timezone format
  console.log(`${timezone.padEnd(20)}: ${date.toLocaleString("en-US")}`);

  // Also show the UTC time for comparison
  console.log(`${" ".repeat(20)} UTC time: ${date.toUTCString()}`);
});

// Test another solar term
const anotherTerm = "Đông chí"; // Winter Solstice
console.log(`\nSolar Term: ${anotherTerm} ${year}`);
console.log("-----------------------------------");

timezones.forEach((timezone) => {
  const date = getSolarTermDate(year, anotherTerm, timezone);
  // Display the date in its local timezone format
  console.log(`${timezone.padEnd(20)}: ${date.toLocaleString("en-US")}`);

  // Also show the UTC time for comparison
  console.log(`${" ".repeat(20)} UTC time: ${date.toUTCString()}`);
});

console.log("\nNote: All dates should show the same day in their respective timezones.");
console.log("If the dates differ by a day between timezones, there might still be timezone issues.");
