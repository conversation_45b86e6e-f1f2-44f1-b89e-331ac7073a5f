"use client";

import { AuthProvider } from "@/lib/auth/auth-context";
import { AuthErrorRecovery } from "@/components/auth-error-recovery";

interface AuthProviderWrapperProps {
  children: React.ReactNode;
}

export function AuthProviderWrapper({ children }: AuthProviderWrapperProps) {
  return (
    <AuthProvider>
      <AuthErrorRecovery className="fixed top-4 inset-x-4 z-50 max-w-md mx-auto" />
      {children}
    </AuthProvider>
  );
}
