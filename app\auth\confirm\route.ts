/**
 * Auth confirmation route handler for Supabase
 * This handles email confirmation links
 */
import type { EmailOtpType } from '@supabase/supabase-js'
import type { NextRequest } from 'next/server'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const token_hash = searchParams.get('token_hash')
  const type = searchParams.get('type') as EmailOtpType | null
  const next = searchParams.get('next') ?? '/'

  if (token_hash && type) {
    const supabase = await createClient()

    const { error } = await supabase.auth.verifyOtp({
      type,
      token_hash,
    })
    
    if (!error) {
      // Redirect user to specified redirect URL or root of app
      redirect(next)
    } else {
      // Redirect the user to an error page with some instructions
      redirect('/error')
    }
  }

  // Redirect the user to an error page with some instructions
  redirect('/error')
}
