/**
 * <PERSON><PERSON><PERSON> to check for mismatches between database user IDs and Supabase user IDs
 * 
 * This script:
 * 1. Fetches all users from Supabase
 * 2. Fetches all chats from the database
 * 3. Checks if there are chats with user IDs that don't match any Supabase user
 * 4. Outputs a report of mismatches
 * 
 * Usage:
 * node scripts/check-user-ids.js
 */

const { createClient } = require('@supabase/supabase-js');
const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

// Initialize Supabase client
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Need service role key to access all users

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Initialize Postgres client
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL,
});

async function main() {
  try {
    console.log('Fetching users from Supabase...');
    const { data: supabaseUsers, error: supabaseError } = await supabase.auth.admin.listUsers();
    
    if (supabaseError) {
      throw new Error(`Error fetching Supabase users: ${supabaseError.message}`);
    }
    
    console.log(`Found ${supabaseUsers.users.length} users in Supabase`);
    
    // Extract Supabase user IDs
    const supabaseUserIds = supabaseUsers.users.map(user => user.id);
    
    // Connect to the database
    const client = await pool.connect();
    
    try {
      console.log('Fetching chats from database...');
      const chatResult = await client.query('SELECT id, "userId", visibility FROM "Chat"');
      const chats = chatResult.rows;
      
      console.log(`Found ${chats.length} chats in the database`);
      
      // Find chats with user IDs that don't match any Supabase user
      const mismatchedChats = chats.filter(chat => !supabaseUserIds.includes(chat.userId));
      
      if (mismatchedChats.length === 0) {
        console.log('No mismatches found! All chat user IDs match Supabase user IDs.');
        return;
      }
      
      console.log(`Found ${mismatchedChats.length} chats with user IDs that don't match any Supabase user:`);
      
      // Group mismatched chats by user ID
      const chatsByUserId = {};
      mismatchedChats.forEach(chat => {
        if (!chatsByUserId[chat.userId]) {
          chatsByUserId[chat.userId] = [];
        }
        chatsByUserId[chat.userId].push(chat);
      });
      
      // Print report
      console.log('\nMismatch Report:');
      console.log('----------------');
      
      Object.entries(chatsByUserId).forEach(([userId, userChats]) => {
        console.log(`\nUser ID: ${userId}`);
        console.log(`Number of chats: ${userChats.length}`);
        console.log('Sample chats:');
        userChats.slice(0, 5).forEach(chat => {
          console.log(`  - Chat ID: ${chat.id}, Visibility: ${chat.visibility}`);
        });
        if (userChats.length > 5) {
          console.log(`  - ... and ${userChats.length - 5} more`);
        }
      });
      
      console.log('\nTo fix these mismatches, you need to:');
      console.log('1. Identify which Supabase user should own these chats');
      console.log('2. Run a migration script to update the user IDs in the database');
      console.log('3. Alternatively, create new Supabase users with these IDs');
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
  }
}

main();
