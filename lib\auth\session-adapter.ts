/**
 * Adapter to convert SupabaseSession to a Session compatible with next-auth
 * This adds the missing 'expires' property required by tools
 */
import type { Session } from "next-auth";
import type { SupabaseSession } from "@/app/(auth)/auth";

/**
 * Adapts a SupabaseSession to be compatible with the Session type from next-auth
 * by adding the required 'expires' property
 */
export function adaptSupabaseSession(supabaseSession: SupabaseSession): Session {
  // Create a session with the expires property set to 30 days from now
  const expiresDate = new Date();
  expiresDate.setDate(expiresDate.getDate() + 30);
  
  return {
    ...supabaseSession,
    expires: expiresDate.toISOString(),
  };
}
