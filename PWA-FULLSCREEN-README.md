# PWA Fullscreen Mode Setup

This document explains how the fullscreen mode has been set up for the Bát Tự Master V Progressive Web App (PWA).

## Changes Made

1. **Updated Web App Manifest**

   - Changed `display` mode from `standalone` to `fullscreen` in `app/manifest.ts`
   - This tells browsers to launch the app without any browser UI elements

2. **Enhanced Viewport Settings**

   - Added `userScalable: false` to prevent pinch zooming
   - Set `viewportFit: "cover"` to enable safe area insets
   - This helps maintain the fullscreen experience without accidental zooming

3. **Improved iOS Support**

   - Updated `appleWebApp` settings in `app/layout.tsx`
   - Changed status bar style to `black-translucent` for better fullscreen integration

4. **Updated Service Worker**

   - Optimized cache list for essential assets
   - Incremented cache version to ensure new assets are cached

5. **Enhanced PWA Installer Component**

   - Updated installation instructions to mention fullscreen mode
   - Improved UI for the installation prompt

6. **Added Safe Area Support for Notches**
   - Implemented CSS classes for handling device notches and safe areas
   - Added `env(safe-area-inset-*)` CSS variables to properly pad content
   - Created utility classes for fixed elements to respect notches
   - Applied safe area classes to main layout and fixed components

## PWA Icons

The application uses the following icons for PWA installation:

- `/images/icon-192x192.png` - Standard icon (192x192 pixels)
- `/images/icon-512x512.png` - Standard icon (512x512 pixels)
- `/images/icon-maskable-192x192.png` - Maskable icon (192x192 pixels)
- `/images/icon-maskable-512x512.png` - Maskable icon (512x512 pixels)

See `PWA-ICONS-README.md` for more information on generating these icons.

## Testing Fullscreen Mode

To test the fullscreen mode:

1. Build and start the application in production mode:

   ```
   pnpm build
   pnpm start
   ```

2. Open the application in a mobile browser or desktop Chrome
3. Install the PWA:

   - On Android/Chrome: Use the install button in the browser or the PWA installer component
   - On iOS: Use the "Add to Home Screen" option in the share menu

4. Launch the installed app from your home screen or app drawer
   - It should open in fullscreen mode without any browser UI elements

## Troubleshooting

If the app is not opening in fullscreen mode:

1. **Check the manifest**

   - Verify that `display: 'fullscreen'` is set in `app/manifest.ts`
   - Use Chrome DevTools > Application > Manifest to inspect the loaded manifest

2. **Clear browser cache**

   - The old manifest might be cached
   - Clear browser cache and reload the page

3. **Reinstall the PWA**

   - Uninstall the PWA from your device
   - Clear browser cache
   - Reinstall the PWA

4. **iOS-specific issues**

   - iOS may not fully support fullscreen mode in the same way as Android
   - The status bar might still be visible on iOS devices
   - Use `black-translucent` status bar style for the best experience

5. **Safe area not working**
   - Make sure `viewportFit: "cover"` is set in the viewport settings
   - Check if the device supports safe area insets
   - Test on different devices to ensure compatibility

## Safe Area Implementation

The app has been configured to handle device notches at the top of the screen in fullscreen mode:

1. **CSS Variables**

   - We use the `env(safe-area-inset-top)` CSS variable to detect and adapt to device notches
   - This variable is provided by the browser and represents the size of the notch at the top of the screen

2. **Utility Classes**

   - `.safe-area-top`: Adds padding at the top for notches while preserving existing padding
   - `header.safe-area-top`: Special case for headers with different padding
   - These classes ensure we maintain regular padding even on devices without notches

3. **Application**
   - The class is applied to:
     - The chat header component
     - The sidebar header component
     - The mobile sidebar sheet component
     - The desktop sidebar component
   - This ensures all navigation elements respect the device notch
   - We've kept the implementation minimal and focused only on the top area

## Browser Compatibility

Fullscreen mode is supported by:

- Chrome for Android (full support)
- Safari for iOS (partial support - status bar may still be visible)
- Most modern mobile browsers

Desktop browsers will typically show the app without browser UI but may still show operating system UI elements.
