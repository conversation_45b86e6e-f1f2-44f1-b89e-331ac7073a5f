/**
 * Payment Service
 * Main orchestrator for payment operations across providers
 */

import { createClient } from "@/lib/supabase/server";
import { createPaymentProvider, getPaymentUrls } from "./payment-config";
import { getServerCreditService } from "@/lib/services/credit-service";
import {
  type CreatePaymentRequest,
  type CreatePaymentResponse,
  type PaymentDetails,
  type WebhookResult,
  type PaymentProviderName,
  type PaymentTransactionRecord,
  PaymentStatus,
  PaymentError,
  PAYMENT_CONSTANTS,
} from "@/lib/types/payment";
import { CREDIT_PACKAGES } from "@/lib/types/credit";

export class PaymentService {
  private supabase: any = null;
  private creditService = getServerCreditService();
  private initialized = false;

  private async ensureInitialized() {
    if (!this.initialized) {
      this.supabase = await createClient();
      this.initialized = true;
    }
  }

  async createPaymentForPackage(userId: string, packageId: string, provider: PaymentProviderName = "payos"): Promise<CreatePaymentResponse> {
    try {
      await this.ensureInitialized();

      // Get package details
      const creditPackage = CREDIT_PACKAGES.find((pkg) => pkg.id === packageId);
      if (!creditPackage || !creditPackage.isActive) {
        throw new PaymentError("Invalid or inactive package", "INVALID_PACKAGE");
      }

      // Generate unique order code
      const orderCode = this.generateOrderCode();
      const urls = getPaymentUrls();

      // Create payment transaction record
      const transactionRecord = await this.createPaymentTransaction({
        userId,
        packageId,
        orderCode,
        amount: creditPackage.priceVnd,
        currency: creditPackage.currency,
        paymentProvider: provider,
        returnUrl: `${urls.success}?orderCode=${orderCode}`,
        cancelUrl: `${urls.cancel}?orderCode=${orderCode}`,
      });

      // Get user info for payment
      const { data: user } = await this.supabase.from("User").select("email").eq("id", userId).single();

      // Create payment request
      const paymentRequest: CreatePaymentRequest = {
        orderCode,
        amount: creditPackage.priceVnd,
        currency: creditPackage.currency,
        description: `Purchase ${creditPackage.name} - ${creditPackage.credits} credits`,
        customer: user
          ? {
              email: user.email,
            }
          : undefined,
        items: [
          {
            name: creditPackage.name,
            quantity: 1,
            price: creditPackage.priceVnd,
          },
        ],
        returnUrl: transactionRecord.returnUrl || `${urls.success}?orderCode=${orderCode}`,
        cancelUrl: transactionRecord.cancelUrl || `${urls.cancel}?orderCode=${orderCode}`,
        expiresAt: new Date(Date.now() + PAYMENT_CONSTANTS.DEFAULT_EXPIRY_HOURS * 60 * 60 * 1000),
      };

      // Create payment with provider
      const paymentProvider = createPaymentProvider(provider);
      const paymentResponse = await paymentProvider.createPayment(paymentRequest);

      // Update transaction record with provider response
      await this.updatePaymentTransaction(transactionRecord.id, {
        providerPaymentId: paymentResponse.paymentId,
        checkoutUrl: paymentResponse.checkoutUrl,
        expiresAt: paymentResponse.expiresAt,
        providerData: paymentResponse.providerData,
      });

      return paymentResponse;
    } catch (error) {
      console.error("Failed to create payment for package:", error);
      throw error instanceof PaymentError ? error : new PaymentError("Failed to create payment", "PAYMENT_CREATION_FAILED");
    }
  }

  async getPaymentDetails(orderCode: string): Promise<PaymentDetails | null> {
    try {
      await this.ensureInitialized();

      // Get transaction record
      const { data: transaction } = await this.supabase.from("payment_transactions").select("*").eq("order_code", orderCode).single();

      if (!transaction) {
        return null;
      }

      // For PayOS, check payment status directly and update if needed
      if (transaction.payment_provider === "payos" && transaction.provider_payment_id) {
        try {
          const provider = createPaymentProvider("payos");
          const providerDetails = await provider.getPayment(transaction.provider_payment_id);

          // If provider status differs from our DB, update it
          if (providerDetails.status !== transaction.status && transaction.status === "PENDING") {
            console.log(`PayOS payment status changed from ${transaction.status} to ${providerDetails.status}, updating database`);

            // Update transaction status
            const updateData: any = {
              status: providerDetails.status,
              updatedAt: new Date(),
            };

            if (providerDetails.status === PaymentStatus.PAID) {
              updateData.paidAt = providerDetails.paidAt || new Date();
            } else if (providerDetails.status === PaymentStatus.CANCELLED) {
              updateData.cancelledAt = providerDetails.cancelledAt || new Date();
            }

            await this.updatePaymentTransaction(transaction.id, updateData);

            // Add credits only if payment is confirmed as PAID
            if (providerDetails.status === PaymentStatus.PAID && transaction.package_id) {
              const creditPackage = CREDIT_PACKAGES.find((pkg) => pkg.id === transaction.package_id);
              if (creditPackage) {
                console.log("Adding credits for confirmed payment:", {
                  userId: transaction.user_id,
                  amount: creditPackage.credits,
                  orderCode,
                });

                await this.creditService.addCredits(
                  transaction.user_id,
                  creditPackage.credits,
                  `Credit package purchase: ${creditPackage.name} (Order: ${orderCode})`
                );
              }
            }
          }

          return providerDetails;
        } catch (providerError) {
          console.error("Failed to get payment details from PayOS:", providerError);
          // Fall back to database details
        }
      }

      // Return details from our database
      return this.mapTransactionToPaymentDetails(transaction);
    } catch (error) {
      console.error("Failed to get payment details:", error);
      return null;
    }
  }

  async handleWebhookResult(result: WebhookResult): Promise<void> {
    try {
      console.log("handleWebhookResult called with:", JSON.stringify(result, null, 2));
      await this.ensureInitialized();

      // Find the payment transaction
      console.log("Looking for payment transaction with order_code:", result.orderCode);
      const { data: transaction, error: findError } = await this.supabase
        .from("payment_transactions")
        .select("*")
        .eq("order_code", result.orderCode)
        .single();

      if (findError) {
        console.error("Error finding payment transaction:", findError);
        throw new PaymentError(`Database error finding transaction: ${findError.message}`, "DB_ERROR");
      }

      if (!transaction) {
        console.error("Payment transaction not found for order_code:", result.orderCode);
        throw new PaymentError("Payment transaction not found", "TRANSACTION_NOT_FOUND");
      }

      console.log("Found payment transaction:", JSON.stringify(transaction, null, 2));

      // Update transaction status
      const updateData: Partial<PaymentTransactionRecord> = {
        status: result.status,
        updatedAt: new Date(),
      };

      if (result.status === PaymentStatus.PAID && result.paidAt) {
        updateData.paidAt = result.paidAt;
      }

      await this.updatePaymentTransaction(transaction.id, updateData);

      // If payment is successful, add credits to user
      console.log("Checking if payment is successful:", {
        status: result.status,
        isPaid: result.status === PaymentStatus.PAID,
        packageId: transaction.package_id,
      });

      if (result.status === PaymentStatus.PAID && transaction.package_id) {
        console.log("Payment is PAID, looking for credit package:", transaction.package_id);
        const creditPackage = CREDIT_PACKAGES.find((pkg) => pkg.id === transaction.package_id);
        console.log("Found credit package:", creditPackage ? JSON.stringify(creditPackage, null, 2) : "NOT FOUND");

        if (creditPackage) {
          const creditDescription = `Credit package purchase: ${creditPackage.name} (Order: ${result.orderCode})`;
          console.log("About to add credits:", {
            userId: transaction.user_id,
            amount: creditPackage.credits,
            description: creditDescription,
          });

          try {
            const creditResult = await this.creditService.addCredits(transaction.user_id, creditPackage.credits, creditDescription);
            console.log("Credits added successfully:", JSON.stringify(creditResult, null, 2));
          } catch (creditError) {
            console.error("Failed to add credits:", creditError);
            throw creditError;
          }
        } else {
          console.error("Credit package not found for package_id:", transaction.package_id);
        }
      } else {
        console.log("Skipping credit addition:", {
          reason: result.status !== PaymentStatus.PAID ? "Payment not PAID" : "No package_id",
          status: result.status,
          packageId: transaction.package_id,
        });
      }
    } catch (error) {
      console.error("Failed to handle webhook result:", error);
      throw error;
    }
  }

  async cancelPayment(orderCode: string, reason?: string): Promise<boolean> {
    try {
      await this.ensureInitialized();

      const { data: transaction } = await this.supabase.from("payment_transactions").select("*").eq("order_code", orderCode).single();

      if (!transaction || !transaction.provider_payment_id) {
        return false;
      }

      const provider = createPaymentProvider(transaction.payment_provider as PaymentProviderName);
      await provider.cancelPayment(transaction.provider_payment_id, reason);

      // Update our record
      await this.updatePaymentTransaction(transaction.id, {
        status: PaymentStatus.CANCELLED,
        cancelledAt: new Date(),
        updatedAt: new Date(),
      });

      return true;
    } catch (error) {
      console.error("Failed to cancel payment:", error);
      return false;
    }
  }

  private async createPaymentTransaction(data: Partial<PaymentTransactionRecord>): Promise<PaymentTransactionRecord> {
    // Validate required fields
    if (!data.userId || !data.orderCode || !data.amount || !data.paymentProvider) {
      throw new PaymentError("Missing required fields for payment transaction", "VALIDATION_ERROR");
    }

    const insertData = {
      user_id: data.userId,
      package_id: data.packageId,
      order_code: data.orderCode,
      amount: data.amount,
      currency: data.currency || PAYMENT_CONSTANTS.DEFAULT_CURRENCY,
      status: PaymentStatus.PENDING,
      payment_provider: data.paymentProvider,
      return_url: data.returnUrl,
      cancel_url: data.cancelUrl,
    };

    console.log("Creating payment transaction with data:", insertData);

    const { data: transaction, error } = await this.supabase.from("payment_transactions").insert(insertData).select().single();

    if (error) {
      console.error("Database error creating payment transaction:", {
        error,
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint,
        insertData,
      });
      throw new PaymentError(`Failed to create payment transaction: ${error.message}`, "DB_ERROR", undefined, error);
    }

    console.log("Payment transaction created successfully:", transaction.id);
    return transaction;
  }

  private async updatePaymentTransaction(id: string, data: Partial<PaymentTransactionRecord>): Promise<void> {
    // Convert camelCase to snake_case for database
    const updateData = this.convertToSnakeCase({ ...data, updatedAt: new Date() });

    console.log("Updating payment transaction:", { id, updateData });

    const { error } = await this.supabase.from("payment_transactions").update(updateData).eq("id", id);

    if (error) {
      console.error("Database error updating payment transaction:", {
        error,
        code: error.code,
        message: error.message,
        details: error.details,
        hint: error.hint,
        id,
        updateData,
      });
      throw new PaymentError(`Failed to update payment transaction: ${error.message}`, "DB_ERROR", undefined, error);
    }

    console.log("Payment transaction updated successfully:", id);
  }

  private convertToSnakeCase(data: any): any {
    const converted: any = {};
    for (const [key, value] of Object.entries(data)) {
      switch (key) {
        case "userId":
          converted.user_id = value;
          break;
        case "packageId":
          converted.package_id = value;
          break;
        case "orderCode":
          converted.order_code = value;
          break;
        case "paymentProvider":
          converted.payment_provider = value;
          break;
        case "providerPaymentId":
          converted.provider_payment_id = value;
          break;
        case "providerData":
          converted.provider_data = value;
          break;
        case "checkoutUrl":
          converted.checkout_url = value;
          break;
        case "returnUrl":
          converted.return_url = value;
          break;
        case "cancelUrl":
          converted.cancel_url = value;
          break;
        case "expiresAt":
          converted.expires_at = value;
          break;
        case "paidAt":
          converted.paid_at = value;
          break;
        case "cancelledAt":
          converted.cancelled_at = value;
          break;
        case "createdAt":
          converted.created_at = value;
          break;
        case "updatedAt":
          converted.updated_at = value;
          break;
        default:
          // Keep other fields as-is (like status, amount, currency)
          converted[key] = value;
          break;
      }
    }
    return converted;
  }

  private mapTransactionToPaymentDetails(transaction: any): PaymentDetails {
    return {
      paymentId: transaction.provider_payment_id || transaction.id,
      orderCode: transaction.order_code,
      amount: transaction.amount,
      amountPaid: transaction.status === PaymentStatus.PAID ? transaction.amount : 0,
      amountRemaining: transaction.status === PaymentStatus.PAID ? 0 : transaction.amount,
      currency: transaction.currency,
      status: transaction.status,
      createdAt: new Date(transaction.created_at),
      paidAt: transaction.paid_at ? new Date(transaction.paid_at) : undefined,
      cancelledAt: transaction.cancelled_at ? new Date(transaction.cancelled_at) : undefined,
      expiresAt: transaction.expires_at ? new Date(transaction.expires_at) : undefined,
      transactions: [],
      providerData: transaction.provider_data,
    };
  }

  private generateOrderCode(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, "0");
    return `${timestamp}${random}`;
  }
}

// Singleton instance
let paymentServiceInstance: PaymentService | null = null;

export function getPaymentService(): PaymentService {
  if (!paymentServiceInstance) {
    paymentServiceInstance = new PaymentService();
  }
  return paymentServiceInstance;
}
