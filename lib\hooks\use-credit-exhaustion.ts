"use client";

import { useCallback, useMemo } from "react";
import { useAuth } from "@/lib/auth/auth-context";
import { useCreditDisplay } from "./use-credits";

export interface CreditExhaustionState {
  // Credit state
  credits: number;
  isOutOfCredits: boolean;
  isLowCredits: boolean;
  isLoadingCredits: boolean;

  // UI state helpers
  shouldDisableChat: boolean;
  shouldDisableTools: boolean;
  shouldShowExhaustionMessage: boolean;
  shouldShowLowCreditWarning: boolean;

  // Action helpers
  canAffordAction: (cost: number) => boolean;
  getExhaustionMessage: (context?: "chat" | "tool" | "general") => string;
  getActionRequiredMessage: (action: string, cost: number) => string;
}

/**
 * Hook for managing credit exhaustion UI states
 * Provides comprehensive state and helpers for credit-related UI decisions
 */
export function useCreditExhaustion(): CreditExhaustionState {
  const { credits, isLoadingCredits } = useAuth();
  const { isOutOfCredits, isLowCredits } = useCreditDisplay();

  // Core state calculations - don't disable chat while loading
  const shouldDisableChat = useMemo(() => {
    return !isLoadingCredits && (isOutOfCredits || credits < 1);
  }, [isLoadingCredits, isOutOfCredits, credits]);

  const shouldDisableTools = useMemo(() => {
    return !isLoadingCredits && (isOutOfCredits || credits < 1);
  }, [isLoadingCredits, isOutOfCredits, credits]);

  const shouldShowExhaustionMessage = useMemo(() => {
    return !isLoadingCredits && isOutOfCredits;
  }, [isLoadingCredits, isOutOfCredits]);

  const shouldShowLowCreditWarning = useMemo(() => {
    return !isLoadingCredits && isLowCredits && !isOutOfCredits;
  }, [isLoadingCredits, isLowCredits, isOutOfCredits]);

  // Action helpers
  const canAffordAction = useCallback(
    (cost: number): boolean => {
      // During loading, assume they can afford it to prevent blocking
      if (isLoadingCredits) return true;
      return credits >= cost;
    },
    [credits, isLoadingCredits]
  );

  const getExhaustionMessage = useCallback((context: "chat" | "tool" | "general" = "general"): string => {
    switch (context) {
      case "chat":
        return "You need 1 credit to send each message. Get more credits to continue chatting.";
      case "tool":
        return "You need credits to use tools and features. Get more credits to access all functionality.";
      case "general":
      default:
        return "You need credits to use Bát Tự Master V. Get more credits to continue.";
    }
  }, []);

  const getActionRequiredMessage = useCallback(
    (action: string, cost: number): string => {
      return `You need ${cost} credit${cost > 1 ? "s" : ""} to ${action}. Current balance: ${credits}`;
    },
    [credits]
  );

  return {
    // Credit state
    credits,
    isOutOfCredits,
    isLowCredits,
    isLoadingCredits,

    // UI state helpers
    shouldDisableChat,
    shouldDisableTools,
    shouldShowExhaustionMessage,
    shouldShowLowCreditWarning,

    // Action helpers
    canAffordAction,
    getExhaustionMessage,
    getActionRequiredMessage,
  };
}

/**
 * Hook for credit exhaustion with specific action context
 * Provides focused state for specific actions (chat, tools, etc.)
 */
export function useCreditExhaustionForAction(action: string, cost = 1) {
  const exhaustion = useCreditExhaustion();

  const canAfford = useMemo(() => {
    return exhaustion.canAffordAction(cost);
  }, [exhaustion, cost]);

  const shouldDisable = useMemo(() => {
    return !canAfford;
  }, [canAfford]);

  const message = useMemo(() => {
    if (!canAfford) {
      return exhaustion.getActionRequiredMessage(action, cost);
    }
    return null;
  }, [exhaustion, action, cost, canAfford]);

  return {
    ...exhaustion,
    canAfford,
    shouldDisable,
    message,
    actionCost: cost,
    actionName: action,
  };
}

/**
 * Hook specifically for chat credit exhaustion
 */
export function useChatCreditExhaustion() {
  return useCreditExhaustionForAction("send a message", 1);
}

/**
 * Hook specifically for tool credit exhaustion
 */
export function useToolCreditExhaustion() {
  return useCreditExhaustionForAction("use this tool", 1);
}

/**
 * Hook for credit exhaustion warnings (when credits are low but not zero)
 */
export function useCreditWarning() {
  const { credits, isLowCredits, isOutOfCredits, isLoadingCredits } = useCreditDisplay();

  const shouldShowWarning = useMemo(() => {
    return isLowCredits && !isOutOfCredits;
  }, [isLowCredits, isOutOfCredits]);

  const warningMessage = useMemo(() => {
    if (shouldShowWarning) {
      return `You have ${credits} credit${credits !== 1 ? "s" : ""} remaining. Consider getting more credits soon.`;
    }
    return null;
  }, [shouldShowWarning, credits]);

  const warningLevel = useMemo(() => {
    if (isLoadingCredits) return "low"; // Default to low during loading
    if (isOutOfCredits) return "critical";
    if (credits <= 2) return "high";
    if (credits <= 5) return "medium";
    return "low";
  }, [credits, isOutOfCredits, isLoadingCredits]);

  return {
    shouldShowWarning,
    warningMessage,
    warningLevel,
    credits,
  };
}
