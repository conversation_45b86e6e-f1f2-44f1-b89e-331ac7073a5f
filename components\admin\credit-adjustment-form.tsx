/**
 * Individual credit adjustment form for admin dashboard
 * Allows admins to adjust credits for individual users with audit trail
 */

"use client";

import { useState, useCallback } from "react";
import { CreditCard, Plus, Minus, RotateCcw, AlertTriangle, X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import type { AdminUserData } from "@/lib/services/admin-service";

interface CreditAdjustmentFormProps {
  user: AdminUserData;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

type OperationType = "add" | "deduct" | "set";

interface AdjustmentData {
  amount: number;
  reason: string;
  operationType: OperationType;
}

export function CreditAdjustmentForm({ user, isOpen, onClose, onSuccess }: CreditAdjustmentFormProps) {
  const [adjustmentData, setAdjustmentData] = useState<AdjustmentData>({
    amount: 0,
    reason: "",
    operationType: "add",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Calculate the new credit balance
  const calculateNewBalance = useCallback(() => {
    const { amount, operationType } = adjustmentData;
    switch (operationType) {
      case "add":
        return user.credits + amount;
      case "deduct":
        return Math.max(0, user.credits - amount);
      case "set":
        return Math.max(0, amount);
      default:
        return user.credits;
    }
  }, [adjustmentData, user.credits]);

  // Validate the adjustment
  const validateAdjustment = useCallback(() => {
    const { amount, reason } = adjustmentData;

    if (amount <= 0) {
      return "Amount must be greater than 0";
    }

    if (!reason.trim()) {
      return "Reason is required for audit trail";
    }

    if (reason.trim().length < 5) {
      return "Reason must be at least 5 characters";
    }

    return null;
  }, [adjustmentData]);

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    const validationError = validateAdjustment();
    if (validationError) {
      toast.error(validationError);
      return;
    }

    setShowConfirmation(true);
  }, [validateAdjustment]);

  // Handle confirmed submission
  const handleConfirmedSubmit = useCallback(async () => {
    setIsSubmitting(true);
    setShowConfirmation(false);

    try {
      const response = await fetch("/api/admin/credits", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          user_id: user.id,
          amount: adjustmentData.operationType === "deduct" ? -adjustmentData.amount : adjustmentData.amount,
          reason: adjustmentData.reason.trim(),
          operation_type: adjustmentData.operationType,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to adjust credits");
      }

      const result = await response.json();

      toast.success(
        `Successfully ${adjustmentData.operationType === "add" ? "added" : adjustmentData.operationType === "deduct" ? "deducted" : "set"} ${
          adjustmentData.amount
        } credits for ${user.email}`
      );

      // Reset form
      setAdjustmentData({
        amount: 0,
        reason: "",
        operationType: "add",
      });

      onSuccess();
      onClose();
    } catch (error) {
      console.error("Credit adjustment error:", error);
      toast.error(error instanceof Error ? error.message : "Failed to adjust credits");
    } finally {
      setIsSubmitting(false);
    }
  }, [adjustmentData, user, onSuccess, onClose]);

  // Handle operation type change
  const handleOperationTypeChange = useCallback((type: OperationType) => {
    setAdjustmentData((prev) => ({ ...prev, operationType: type }));
  }, []);

  // Handle amount change
  const handleAmountChange = useCallback((value: string) => {
    const amount = Number.parseInt(value, 10) || 0;
    setAdjustmentData((prev) => ({ ...prev, amount: Math.max(0, amount) }));
  }, []);

  // Handle reason change
  const handleReasonChange = useCallback((value: string) => {
    setAdjustmentData((prev) => ({ ...prev, reason: value }));
  }, []);

  // Reset form
  const handleReset = useCallback(() => {
    setAdjustmentData({
      amount: 0,
      reason: "",
      operationType: "add",
    });
  }, []);

  const newBalance = calculateNewBalance();
  const validationError = validateAdjustment();

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black/50 z-50"
        onClick={onClose}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => e.key === "Escape" && onClose()}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-[500px] max-h-[90vh] overflow-y-auto">
          <CardContent className="p-6">
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-2">
                <CreditCard className="size-5" />
                <h2 className="text-lg font-semibold">Adjust Credits</h2>
              </div>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="size-4" />
              </Button>
            </div>

            <p className="text-sm text-muted-foreground mb-6">Adjust credit balance for {user.email}</p>

            <div className="space-y-6">
              {/* User Info */}
              <Card>
                <CardContent className="pt-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{user.email}</p>
                      <p className="text-sm text-muted-foreground">ID: {user.id.slice(0, 8)}...</p>
                    </div>
                    <Badge variant="outline" className="text-lg px-3 py-1">
                      {user.credits} credits
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Operation Type Selection */}
              <div className="space-y-3">
                <Label className="text-sm font-medium">Operation Type</Label>
                <div className="flex gap-2">
                  <Button
                    variant={adjustmentData.operationType === "add" ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleOperationTypeChange("add")}
                    className="flex items-center gap-2"
                  >
                    <Plus className="size-4" />
                    Add Credits
                  </Button>
                  <Button
                    variant={adjustmentData.operationType === "deduct" ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleOperationTypeChange("deduct")}
                    className="flex items-center gap-2"
                  >
                    <Minus className="size-4" />
                    Deduct Credits
                  </Button>
                  <Button
                    variant={adjustmentData.operationType === "set" ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleOperationTypeChange("set")}
                    className="flex items-center gap-2"
                  >
                    <RotateCcw className="size-4" />
                    Set Balance
                  </Button>
                </div>
              </div>

              {/* Amount Input */}
              <div className="space-y-2">
                <Label htmlFor="amount">{adjustmentData.operationType === "set" ? "New Balance" : "Amount"}</Label>
                <Input
                  id="amount"
                  type="number"
                  min="1"
                  value={adjustmentData.amount || ""}
                  onChange={(e) => handleAmountChange(e.target.value)}
                  placeholder={adjustmentData.operationType === "set" ? "Enter new balance" : "Enter amount"}
                />
              </div>

              {/* Reason Input */}
              <div className="space-y-2">
                <Label htmlFor="reason">Reason (Required for audit trail)</Label>
                <Textarea
                  id="reason"
                  value={adjustmentData.reason}
                  onChange={(e) => handleReasonChange(e.target.value)}
                  placeholder="Enter reason for credit adjustment..."
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">Minimum 5 characters. This will be logged for audit purposes.</p>
              </div>

              {/* Preview */}
              {adjustmentData.amount > 0 && (
                <Card className="bg-muted/50">
                  <CardContent className="pt-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Preview:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">{user.credits} credits</span>
                        <span className="text-muted-foreground">→</span>
                        <Badge variant={newBalance > user.credits ? "default" : newBalance < user.credits ? "secondary" : "outline"}>
                          {newBalance} credits
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Footer */}
            <div className="flex gap-2 pt-4 border-t">
              <Button variant="outline" onClick={handleReset} disabled={isSubmitting}>
                Reset
              </Button>
              <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
                Cancel
              </Button>
              <Button onClick={handleSubmit} disabled={isSubmitting || !!validationError}>
                {isSubmitting ? "Processing..." : "Adjust Credits"}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && (
        <>
          <div
            className="fixed inset-0 bg-black/50 z-[60]"
            onClick={() => setShowConfirmation(false)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => e.key === "Escape" && setShowConfirmation(false)}
          />
          <div className="fixed inset-0 z-[60] flex items-center justify-center p-4">
            <Card className="w-full max-w-[400px]">
              <CardContent className="p-6">
                <div className="flex items-center gap-2 mb-4">
                  <AlertTriangle className="size-5 text-orange-500" />
                  <h3 className="text-lg font-semibold">Confirm Credit Adjustment</h3>
                </div>

                <p className="text-sm text-muted-foreground mb-4">Please confirm this credit adjustment operation.</p>

                <div className="space-y-2 mb-4">
                  <p>
                    <strong>User:</strong> {user.email}
                  </p>
                  <p>
                    <strong>Operation:</strong>{" "}
                    {adjustmentData.operationType === "add" ? "Add" : adjustmentData.operationType === "deduct" ? "Deduct" : "Set balance to"}{" "}
                    {adjustmentData.amount} credits
                  </p>
                  <p>
                    <strong>Current Balance:</strong> {user.credits} credits
                  </p>
                  <p>
                    <strong>New Balance:</strong> {newBalance} credits
                  </p>
                  <p>
                    <strong>Reason:</strong> {adjustmentData.reason}
                  </p>
                </div>

                <div className="border-t pt-4 mb-4">
                  <p className="text-sm text-muted-foreground">This action will be logged in the audit trail and cannot be undone.</p>
                </div>

                <div className="flex gap-2">
                  <Button variant="outline" onClick={() => setShowConfirmation(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleConfirmedSubmit} disabled={isSubmitting}>
                    {isSubmitting ? "Processing..." : "Confirm Adjustment"}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </>
  );
}
