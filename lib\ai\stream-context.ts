import { createResumableStreamContext, type ResumableStreamContext } from "resumable-stream";
import { after } from "next/server";

// Global stream context for resumable streams
let globalStreamContext: ResumableStreamContext | null = null;

/**
 * Resets the global stream context
 * This is useful when we need to force a new context to be created
 */
export function resetStreamContext() {
  globalStreamContext = null;
  console.log("Stream context has been reset");
}

/**
 * Gets the current stream context or creates a new one if none exists
 * @returns The current stream context
 */
export function getStreamContext() {
  if (!globalStreamContext) {
    try {
      globalStreamContext = createResumableStreamContext({
        waitUntil: after,
      });
    } catch (error: any) {
      if (error.message.includes("REDIS_URL")) {
        console.log(" > Resumable streams are disabled due to missing REDIS_URL");
      } else {
        console.error(error);
      }
    }
  }

  return globalStreamContext;
}
