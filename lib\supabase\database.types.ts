/**
 * TypeScript definitions for Supabase database
 * Generated using supabase CLI or manually defined
 */
export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[];

export type Database = {
  public: {
    Tables: {
      User: {
        Row: {
          id: string;
          email: string;
          password?: string | null;
          credits: number;
        };
        Insert: {
          id?: string;
          email: string;
          password?: string | null;
          credits?: number;
        };
        Update: {
          id?: string;
          email?: string;
          password?: string | null;
          credits?: number;
        };
      };
      credit_transactions: {
        Row: {
          id: string;
          user_id: string;
          amount: number;
          description: string;
          transaction_type: "deduction" | "addition" | "admin_adjustment";
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          amount: number;
          description: string;
          transaction_type?: "deduction" | "addition" | "admin_adjustment";
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          amount?: number;
          description?: string;
          transaction_type?: "deduction" | "addition" | "admin_adjustment";
          created_at?: string;
        };
      };
    };
    Functions: {
      deduct_credit: {
        Args: {
          p_user_id: string;
          p_amount: number;
          p_description: string;
        };
        Returns: {
          success: boolean;
          balance: number;
          deducted?: number;
          error?: string;
        };
      };
      get_user_credits: {
        Args: {
          p_user_id: string;
        };
        Returns: number;
      };
      add_credits: {
        Args: {
          p_user_id: string;
          p_amount: number;
          p_description: string;
        };
        Returns: {
          success: boolean;
          balance: number;
          added?: number;
          error?: string;
        };
      };
    };
  };
};
