import { tool } from "ai";
import { z } from "zod";
import { getPersonality } from "@/lib/tuvibattu/ultilities/personality";

/**
 * A simple test tool to verify that tool calling is working
 * This tool will echo back the input with a timestamp
 */
export const checkDetailedPersonalityTool = tool({
  description:
    "Use this tool to get a more in-depth, detailed of a person's personality based on their birthdate, time of birth and gender. Usually will be use in case they asks more about work, life, love,..., Will need the user's birth place for this to derive Timezone from it (City and country where they were born). Transform the input into the expected format",
  parameters: z.object({
    birthDate: z
      .object({
        day: z.number().describe("The day of the birth date"),
        month: z.number().describe("The month of the birth date"),
        year: z.number().describe("The year of the birth date"),
      })
      .describe("The birth date of the person in object format {day: number, month: number, year: number}"),
    hour: z.number().describe("The birth hour of the person, should be in 24 hours format"),
    gender: z.string().describe("The gender of the person, should be either <PERSON> or <PERSON><PERSON> (vietnamese)"),
    timezone: z.string().describe("The timezone of the person, should be in the format of IANA timezone, default is Asia/Ho_Chi_Minh"),
  }),
  execute: async ({ birthDate, hour, gender, timezone }) => {
    return getPersonality(birthDate, hour, gender, timezone);
  },
});
