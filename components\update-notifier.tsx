"use client";

import { useVersionCheck } from "@/hooks/use-version-check";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RefreshCw, Download } from "lucide-react";

interface UpdateNotifierProps {
  autoUpdate?: boolean;
  showVersionInfo?: boolean;
}

export function UpdateNotifier({ autoUpdate = false, showVersionInfo = false }: UpdateNotifierProps) {
  const { isUpdateAvailable, versionInfo, isChecking, checkNow, updateNow } = useVersionCheck(true);

  // Auto-update if enabled and update is available
  if (autoUpdate && isUpdateAvailable) {
    updateNow();
    return null;
  }

  if (!isUpdateAvailable && !showVersionInfo) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <Card className="border-primary bg-background/95 backdrop-blur-sm shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">{isUpdateAvailable ? "Update Available" : "App Version"}</CardTitle>
            {isUpdateAvailable && (
              <Badge variant="secondary" className="animate-pulse">
                New
              </Badge>
            )}
          </div>
          {isUpdateAvailable && <CardDescription className="text-xs">A new version of the app is ready to install.</CardDescription>}
        </CardHeader>

        <CardContent className="pt-0">
          {versionInfo && showVersionInfo && (
            <div className="mb-3 text-xs text-muted-foreground space-y-1">
              <div>Version: {versionInfo.version}</div>
              <div>Built: {new Date(Number(versionInfo.timestamp)).toLocaleString()}</div>
            </div>
          )}

          <div className="flex gap-2">
            {isUpdateAvailable ? (
              <Button onClick={updateNow} size="sm" className="flex-1">
                <Download className="h-3 w-3 mr-1" />
                Update Now
              </Button>
            ) : (
              <Button onClick={checkNow} size="sm" variant="outline" disabled={isChecking} className="flex-1">
                <RefreshCw className={`h-3 w-3 mr-1 ${isChecking ? "animate-spin" : ""}`} />
                Check for Updates
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Simpler inline notification version
export function UpdateBanner({ className }: { className?: string }) {
  const { isUpdateAvailable, updateNow } = useVersionCheck(true);

  if (!isUpdateAvailable) {
    return null;
  }

  return (
    <div className={`bg-primary text-primary-foreground px-4 py-2 text-center text-sm ${className}`}>
      <span className="mr-2">A new version is available!</span>
      <Button onClick={updateNow} size="sm" variant="secondary" className="ml-2">
        Update Now
      </Button>
    </div>
  );
}
