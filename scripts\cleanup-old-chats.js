/**
 * <PERSON><PERSON>t to clean up chat messages older than 60 days
 *
 * This script:
 * 1. Connects to the database
 * 2. Deletes votes for messages older than 60 days
 * 3. Deletes messages older than 60 days
 * 4. Deletes empty chats (chats with no messages)
 *
 * Usage:
 * node scripts/cleanup-old-chats.js
 *
 * This script can be scheduled to run periodically using a cron job or similar.
 */

const { Pool } = require("pg");
require("dotenv").config({ path: ".env.local" });

// Initialize Postgres client
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL,
});

async function main() {
  const client = await pool.connect();

  try {
    // Start a transaction
    await client.query("BEGIN");

    console.log("Cleaning up old chat messages...");

    // Get count of messages to be deleted
    const oldMessagesResult = await client.query('SELECT COUNT(*) FROM "Message_v2" WHERE "createdAt" < NOW() - INTERVAL \'60 days\'');

    const oldMessagesCount = Number.parseInt(oldMessagesResult.rows[0].count);

    if (oldMessagesCount > 0) {
      console.log(`Found ${oldMessagesCount} messages older than 60 days`);

      // Delete votes for old messages
      const votesResult = await client.query(
        'DELETE FROM "Vote_v2" WHERE "messageId" IN (SELECT "id" FROM "Message_v2" WHERE "createdAt" < NOW() - INTERVAL \'60 days\') RETURNING *'
      );

      console.log(`Deleted ${votesResult.rowCount} votes for old messages`);

      // Delete old messages
      const messagesResult = await client.query('DELETE FROM "Message_v2" WHERE "createdAt" < NOW() - INTERVAL \'60 days\' RETURNING *');

      console.log(`Deleted ${messagesResult.rowCount} old messages`);

      // Delete empty chats
      const chatsResult = await client.query('DELETE FROM "Chat" WHERE "id" NOT IN (SELECT DISTINCT "chatId" FROM "Message_v2") RETURNING *');

      console.log(`Deleted ${chatsResult.rowCount} empty chats`);
    } else {
      console.log("No messages older than 30 days found");
    }

    // Commit the transaction
    await client.query("COMMIT");

    console.log("Cleanup completed successfully");
  } catch (error) {
    // Rollback the transaction in case of error
    await client.query("ROLLBACK");
    console.error("Error during cleanup:", error);
  } finally {
    // Release the client
    client.release();

    // Close the pool
    pool.end();
  }
}

// Run the main function
main().catch((error) => {
  console.error("Unhandled error:", error);
  process.exit(1);
});
