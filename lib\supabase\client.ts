/**
 * Client-side Supabase client.
 * This file provides a utility for creating a Supabase client in browser environments.
 * Following official Supabase Next.js documentation patterns.
 */
import { createBrowserClient } from "@supabase/ssr";
import type { Database } from "@/lib/supabase/database.types";

export function createClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

  if (!supabaseUrl || !supabaseAnonKey) {
    throw new Error("Missing Supabase environment variables");
  }

  // Create a supabase client on the browser with project's credentials
  return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey);
}
