"use client";

import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { ArrowDown, ArrowU<PERSON>, Settings, Coins } from "lucide-react";
import type { CreditTransaction } from "@/lib/types/credit";

interface TransactionItemProps {
  transaction: CreditTransaction;
  className?: string;
}

export function TransactionItem({ transaction, className }: TransactionItemProps) {
  const isDeduction = transaction.amount < 0;
  const isAddition = transaction.amount > 0;
  const isAdminAdjustment = transaction.transaction_type === "admin_adjustment";

  const getTransactionIcon = () => {
    if (isAdminAdjustment) {
      return <Settings className="size-4" />;
    }
    if (isDeduction) {
      return <ArrowDown className="size-4" />;
    }
    return <ArrowUp className="size-4" />;
  };

  const getTransactionColor = () => {
    if (isAdminAdjustment) {
      return "text-blue-600 dark:text-blue-400";
    }
    if (isDeduction) {
      return "text-red-600 dark:text-red-400";
    }
    return "text-green-600 dark:text-green-400";
  };

  const getBadgeVariant = () => {
    if (isAdminAdjustment) {
      return "secondary";
    }
    if (isDeduction) {
      return "destructive";
    }
    return "default";
  };

  const formatAmount = (amount: number) => {
    const absAmount = Math.abs(amount);
    if (amount > 0) {
      return `+${absAmount}`;
    }
    return `-${absAmount}`;
  };

  const formatTransactionType = (type: string) => {
    return type.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? "s" : ""} ago`;
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours);
      return `${hours} hour${hours !== 1 ? "s" : ""} ago`;
    } else if (diffInHours < 48) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
        year: date.getFullYear() !== now.getFullYear() ? "numeric" : undefined,
      });
    }
  };

  return (
    <div className={cn("p-3 sm:p-4 border rounded-lg hover:bg-muted/50 transition-colors", className)}>
      {/* Mobile Layout: Stack vertically */}
      <div className="block sm:hidden">
        <div className="flex items-start gap-3">
          {/* Transaction Icon */}
          <div
            className={cn(
              "flex items-center justify-center w-7 h-7 rounded-full shrink-0",
              isAdminAdjustment
                ? "bg-blue-100 dark:bg-blue-900/30"
                : isDeduction
                ? "bg-red-100 dark:bg-red-900/30"
                : "bg-green-100 dark:bg-green-900/30"
            )}
          >
            <div className={getTransactionColor()}>{getTransactionIcon()}</div>
          </div>

          {/* Transaction Details and Amount */}
          <div className="flex-1 min-w-0">
            {/* First Row: Description and Amount */}
            <div className="flex items-start justify-between gap-2 mb-1">
              <p
                className="font-medium text-sm leading-tight flex-1 overflow-hidden"
                style={{
                  display: "-webkit-box",
                  WebkitLineClamp: 2,
                  WebkitBoxOrient: "vertical",
                }}
              >
                {transaction.description}
              </p>
              <div className="text-right shrink-0">
                <p className={cn("font-semibold text-base", getTransactionColor())}>{formatAmount(transaction.amount)}</p>
                <p className="text-xs text-muted-foreground">credit{Math.abs(transaction.amount) !== 1 ? "s" : ""}</p>
              </div>
            </div>

            {/* Second Row: Badge */}
            <div className="mb-2">
              <Badge variant={getBadgeVariant()} className="text-xs shrink-0">
                {formatTransactionType(transaction.transaction_type)}
              </Badge>
            </div>

            {/* Third Row: Date and Transaction ID */}
            <div className="flex flex-col gap-1 text-xs text-muted-foreground">
              <span>{formatDate(transaction.created_at)}</span>
              <span className="flex items-center gap-1">
                <Coins className="size-3" />
                ID: {transaction.id.slice(-8)}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Layout: Horizontal */}
      <div className="hidden sm:flex items-center justify-between">
        <div className="flex items-start gap-3 flex-1 min-w-0">
          {/* Transaction Icon */}
          <div
            className={cn(
              "flex items-center justify-center size-8 rounded-full shrink-0",
              isAdminAdjustment
                ? "bg-blue-100 dark:bg-blue-900/30"
                : isDeduction
                ? "bg-red-100 dark:bg-red-900/30"
                : "bg-green-100 dark:bg-green-900/30"
            )}
          >
            <div className={getTransactionColor()}>{getTransactionIcon()}</div>
          </div>

          {/* Transaction Details */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <p className="font-medium text-sm truncate">{transaction.description}</p>
              <Badge variant={getBadgeVariant()} className="text-xs shrink-0">
                {formatTransactionType(transaction.transaction_type)}
              </Badge>
            </div>

            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <span>{formatDate(transaction.created_at)}</span>
              <span>•</span>
              <span className="flex items-center gap-1">
                <Coins className="size-3" />
                Transaction ID: {transaction.id.slice(-8)}
              </span>
            </div>
          </div>
        </div>

        {/* Amount */}
        <div className="text-right shrink-0 ml-4">
          <p className={cn("font-semibold text-lg", getTransactionColor())}>{formatAmount(transaction.amount)}</p>
          <p className="text-xs text-muted-foreground">credit{Math.abs(transaction.amount) !== 1 ? "s" : ""}</p>
        </div>
      </div>
    </div>
  );
}

// Specialized variants for different contexts
export function CompactTransactionItem({ transaction, className }: TransactionItemProps) {
  const isDeduction = transaction.amount < 0;
  const getTransactionColor = () => {
    if (transaction.transaction_type === "admin_adjustment") {
      return "text-blue-600 dark:text-blue-400";
    }
    return isDeduction ? "text-red-600 dark:text-red-400" : "text-green-600 dark:text-green-400";
  };

  const formatAmount = (amount: number) => {
    const absAmount = Math.abs(amount);
    if (amount > 0) {
      return `+${absAmount}`;
    }
    return `-${absAmount}`;
  };

  return (
    <div className={cn("py-2 px-3 text-sm border-b last:border-b-0", className)}>
      {/* Mobile: Stack vertically */}
      <div className="block sm:hidden">
        <div className="flex items-start justify-between gap-2 mb-1">
          <p
            className="font-medium flex-1 overflow-hidden"
            style={{
              display: "-webkit-box",
              WebkitLineClamp: 1,
              WebkitBoxOrient: "vertical",
            }}
          >
            {transaction.description}
          </p>
          <div className={cn("font-medium text-sm shrink-0", getTransactionColor())}>{formatAmount(transaction.amount)}</div>
        </div>
        <p className="text-xs text-muted-foreground">{new Date(transaction.created_at).toLocaleDateString()}</p>
      </div>

      {/* Desktop: Horizontal */}
      <div className="hidden sm:flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <p className="truncate font-medium">{transaction.description}</p>
          <p className="text-xs text-muted-foreground">{new Date(transaction.created_at).toLocaleDateString()}</p>
        </div>
        <div className={cn("font-medium ml-2", getTransactionColor())}>{formatAmount(transaction.amount)}</div>
      </div>
    </div>
  );
}
