/**
 * <PERSON><PERSON><PERSON> to fix mismatches between database user IDs and Supabase user IDs
 * 
 * This script:
 * 1. Takes a mapping of old user IDs to new Supabase user IDs
 * 2. Updates all chats in the database to use the new user IDs
 * 
 * Usage:
 * node scripts/fix-user-ids.js
 */

const { Pool } = require('pg');
require('dotenv').config({ path: '.env.local' });

// Initialize Postgres client
const pool = new Pool({
  connectionString: process.env.POSTGRES_URL,
});

// This mapping should be filled in after running check-user-ids.js
// Format: { 'old-user-id': 'new-supabase-user-id' }
const userIdMapping = {
  // Example: '123e4567-e89b-12d3-a456-426614174000': '098f6bcd-4621-3373-8ade-4e832627b4f6'
};

async function main() {
  // Validate that we have mappings
  if (Object.keys(userIdMapping).length === 0) {
    console.error('No user ID mappings provided. Please edit the script to add mappings.');
    process.exit(1);
  }

  const client = await pool.connect();
  
  try {
    // Start a transaction
    await client.query('BEGIN');
    
    console.log('Updating chat user IDs...');
    
    // Process each mapping
    for (const [oldUserId, newUserId] of Object.entries(userIdMapping)) {
      // Update chats
      const chatResult = await client.query(
        'UPDATE "Chat" SET "userId" = $1 WHERE "userId" = $2 RETURNING id',
        [newUserId, oldUserId]
      );
      
      console.log(`Updated ${chatResult.rowCount} chats for user ${oldUserId} -> ${newUserId}`);
      
      // If you have other tables with user IDs, update them here
      // For example:
      // await client.query('UPDATE "Document" SET "userId" = $1 WHERE "userId" = $2', [newUserId, oldUserId]);
    }
    
    // Commit the transaction
    await client.query('COMMIT');
    
    console.log('User ID migration completed successfully!');
  } catch (error) {
    // Rollback in case of error
    await client.query('ROLLBACK');
    console.error('Error during migration:', error);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

main();
