-- Migration: Create RPC functions for atomic credit operations
-- This migration creates PostgreSQL functions for safe credit operations

-- Function 1: deduct_credit - Atomic credit deduction with balance validation
CREATE OR REPLACE FUNCTION deduct_credit(
  p_user_id UUID,
  p_amount INTEGER,
  p_description TEXT
) RETURNS JSON AS $$
DECLARE
  current_credits INTEGER;
  new_balance INTEGER;
BEGIN
  -- Validate input parameters
  IF p_user_id IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User ID cannot be null',
      'balance', 0
    );
  END IF;

  IF p_amount IS NULL OR p_amount <= 0 THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Amount must be a positive integer',
      'balance', 0
    );
  END IF;

  IF p_description IS NULL OR LENGTH(TRIM(p_description)) = 0 THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Description cannot be empty',
      'balance', 0
    );
  END IF;

  -- Lock the user row to prevent race conditions
  SELECT credits INTO current_credits
  FROM "User"
  WHERE id = p_user_id
  FOR UPDATE;

  -- Check if user exists
  IF current_credits IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User not found',
      'balance', 0
    );
  END IF;

  -- Check if sufficient credits
  IF current_credits < p_amount THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Insufficient credits',
      'balance', current_credits
    );
  END IF;

  -- Calculate new balance
  new_balance := current_credits - p_amount;

  -- Update user credits
  UPDATE "User"
  SET credits = new_balance
  WHERE id = p_user_id;

  -- Insert transaction record (negative amount for deduction)
  INSERT INTO credit_transactions (user_id, amount, description, transaction_type)
  VALUES (p_user_id, -p_amount, p_description, 'deduction');

  -- Return success
  RETURN json_build_object(
    'success', true,
    'balance', new_balance,
    'deducted', p_amount
  );
EXCEPTION
  WHEN OTHERS THEN
    -- Handle any unexpected errors
    RETURN json_build_object(
      'success', false,
      'error', 'Database error: ' || SQLERRM,
      'balance', 0
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 2: get_user_credits - Get current credit balance
CREATE OR REPLACE FUNCTION get_user_credits(p_user_id UUID)
RETURNS INTEGER AS $$
DECLARE
  user_credits INTEGER;
BEGIN
  -- Validate input
  IF p_user_id IS NULL THEN
    RETURN 0;
  END IF;

  SELECT credits INTO user_credits
  FROM "User"
  WHERE id = p_user_id;

  -- Return 0 if user not found, otherwise return actual credits
  RETURN COALESCE(user_credits, 0);
EXCEPTION
  WHEN OTHERS THEN
    -- Return 0 on any error
    RETURN 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 3: add_credits - Add credits (for admin/payment operations)
CREATE OR REPLACE FUNCTION add_credits(
  p_user_id UUID,
  p_amount INTEGER,
  p_description TEXT
) RETURNS JSON AS $$
DECLARE
  current_credits INTEGER;
  new_balance INTEGER;
BEGIN
  -- Validate input parameters
  IF p_user_id IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User ID cannot be null'
    );
  END IF;

  IF p_amount IS NULL OR p_amount <= 0 THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Amount must be a positive integer'
    );
  END IF;

  IF p_description IS NULL OR LENGTH(TRIM(p_description)) = 0 THEN
    RETURN json_build_object(
      'success', false,
      'error', 'Description cannot be empty'
    );
  END IF;

  -- Get current credits with row lock
  SELECT credits INTO current_credits
  FROM "User"
  WHERE id = p_user_id
  FOR UPDATE;

  -- Check if user exists
  IF current_credits IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User not found'
    );
  END IF;

  -- Calculate new balance
  new_balance := current_credits + p_amount;

  -- Update user credits
  UPDATE "User"
  SET credits = new_balance
  WHERE id = p_user_id;

  -- Insert transaction record (positive amount for addition)
  INSERT INTO credit_transactions (user_id, amount, description, transaction_type)
  VALUES (p_user_id, p_amount, p_description, 'addition');

  -- Return success
  RETURN json_build_object(
    'success', true,
    'balance', new_balance,
    'added', p_amount
  );
EXCEPTION
  WHEN OTHERS THEN
    -- Handle any unexpected errors
    RETURN json_build_object(
      'success', false,
      'error', 'Database error: ' || SQLERRM
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 4: get_credit_history - Get transaction history with pagination
CREATE OR REPLACE FUNCTION get_credit_history(
  p_user_id UUID,
  p_limit INTEGER DEFAULT 50,
  p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
  id UUID,
  user_id UUID,
  amount INTEGER,
  description TEXT,
  transaction_type VARCHAR,
  created_at TIMESTAMP
) AS $$
BEGIN
  -- Validate input parameters
  IF p_user_id IS NULL THEN
    RETURN;
  END IF;

  -- Set reasonable defaults and limits
  p_limit := COALESCE(LEAST(p_limit, 1000), 50);
  p_offset := COALESCE(GREATEST(p_offset, 0), 0);

  RETURN QUERY
  SELECT
    ct.id,
    ct.user_id,
    ct.amount,
    ct.description,
    ct.transaction_type,
    ct.created_at
  FROM credit_transactions ct
  WHERE ct.user_id = p_user_id
  ORDER BY ct.created_at DESC
  LIMIT p_limit
  OFFSET p_offset;
EXCEPTION
  WHEN OTHERS THEN
    -- Return empty result on error
    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function 5: validate_sufficient_credits - Check if user has enough credits (utility function)
CREATE OR REPLACE FUNCTION validate_sufficient_credits(
  p_user_id UUID,
  p_required_amount INTEGER
) RETURNS BOOLEAN AS $$
DECLARE
  current_credits INTEGER;
BEGIN
  -- Validate input
  IF p_user_id IS NULL OR p_required_amount IS NULL OR p_required_amount < 0 THEN
    RETURN false;
  END IF;

  SELECT credits INTO current_credits
  FROM "User"
  WHERE id = p_user_id;

  -- Return false if user not found or insufficient credits
  RETURN COALESCE(current_credits, 0) >= p_required_amount;
EXCEPTION
  WHEN OTHERS THEN
    -- Return false on any error
    RETURN false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;