"use client";

import { useEffect, useState } from "react";
import { usePostHog } from "posthog-js/react";

export default function PostHogTestPage() {
  const posthog = usePostHog();
  const [eventSent, setEventSent] = useState(false);
  const [posthogInfo, setPosthogInfo] = useState<any>({});

  useEffect(() => {
    // Get PostHog information
    if (posthog) {
      const info = {
        distinctId: posthog.get_distinct_id?.() || "Not available",
        sessionId: posthog.get_session_id?.() || "Not available",
        hasOptedOut: posthog.has_opted_out_capturing?.() || false,
        hasOptedIn: posthog.has_opted_in_capturing?.() || false,
        config: posthog.config || {},
      };
      setPosthogInfo(info);
    }
  }, [posthog]);

  const sendTestEvent = () => {
    if (posthog) {
      posthog.capture("test_event", {
        timestamp: new Date().toISOString(),
        test_property: "test_value",
      });
      setEventSent(true);

      // Force flush events
      setTimeout(() => {
        const posthogAny = posthog as any;
        if (typeof posthogAny.flush === "function") {
          posthogAny.flush();
        }
      }, 500);
    }
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">PostHog Test Page</h1>

      <div className="mb-4">
        <button type="button" onClick={sendTestEvent} className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          Send Test Event
        </button>
        {eventSent && <p className="text-green-500 mt-2">Event sent! Check your PostHog dashboard.</p>}
      </div>

      <div className="mt-8">
        <h2 className="text-xl font-bold mb-2">PostHog Information</h2>
        <pre className="bg-gray-100 p-4 rounded overflow-auto">{JSON.stringify(posthogInfo, null, 2)}</pre>
      </div>
    </div>
  );
}
