/**
 * Base Payment Provider Abstract Class
 * Provides common functionality and validation for all payment providers
 */

import {
  type PaymentProvider,
  type CreatePaymentRequest,
  type CreatePaymentResponse,
  type PaymentDetails,
  type CancelPaymentResponse,
  type WebhookResult,
  PaymentValidationError,
  PAYMENT_CONSTANTS,
} from '@/lib/types/payment';

export abstract class BasePaymentProvider implements PaymentProvider {
  abstract name: string;

  constructor(protected config: Record<string, any>) {}

  // Abstract methods that must be implemented by concrete providers
  abstract createPayment(request: CreatePaymentRequest): Promise<CreatePaymentResponse>;
  abstract getPayment(paymentId: string): Promise<PaymentDetails>;
  abstract cancelPayment(paymentId: string, reason?: string): Promise<CancelPaymentResponse>;
  abstract processWebhook(payload: any, signature: string): Promise<WebhookResult>;
  abstract validateWebhookSignature(payload: any, signature: string): boolean;

  // Common validation methods
  protected validateCreatePaymentRequest(request: CreatePaymentRequest): void {
    const errors: string[] = [];

    // Validate order code
    if (!request.orderCode || typeof request.orderCode !== 'string' || request.orderCode.trim().length === 0) {
      errors.push('Order code is required and must be a non-empty string');
    }

    // Validate amount
    if (!request.amount || typeof request.amount !== 'number' || request.amount < PAYMENT_CONSTANTS.MIN_AMOUNT) {
      errors.push(`Amount must be at least ${PAYMENT_CONSTANTS.MIN_AMOUNT} ${request.currency || PAYMENT_CONSTANTS.DEFAULT_CURRENCY}`);
    }

    if (request.amount > PAYMENT_CONSTANTS.MAX_AMOUNT) {
      errors.push(`Amount cannot exceed ${PAYMENT_CONSTANTS.MAX_AMOUNT} ${request.currency || PAYMENT_CONSTANTS.DEFAULT_CURRENCY}`);
    }

    // Validate currency
    if (!request.currency || typeof request.currency !== 'string') {
      errors.push('Currency is required');
    }

    // Validate description
    if (!request.description || typeof request.description !== 'string' || request.description.trim().length === 0) {
      errors.push('Description is required and must be a non-empty string');
    }

    if (request.description.length > PAYMENT_CONSTANTS.MAX_DESCRIPTION_LENGTH) {
      errors.push(`Description cannot exceed ${PAYMENT_CONSTANTS.MAX_DESCRIPTION_LENGTH} characters`);
    }

    // Validate URLs
    if (!request.returnUrl || !this.isValidUrl(request.returnUrl)) {
      errors.push('Return URL is required and must be a valid URL');
    }

    if (!request.cancelUrl || !this.isValidUrl(request.cancelUrl)) {
      errors.push('Cancel URL is required and must be a valid URL');
    }

    // Validate customer info if provided
    if (request.customer) {
      if (request.customer.email && !this.isValidEmail(request.customer.email)) {
        errors.push('Customer email must be a valid email address');
      }
      if (request.customer.phone && !this.isValidPhone(request.customer.phone)) {
        errors.push('Customer phone must be a valid phone number');
      }
    }

    // Validate items if provided
    if (request.items) {
      if (!Array.isArray(request.items) || request.items.length === 0) {
        errors.push('Items must be a non-empty array');
      } else {
        request.items.forEach((item, index) => {
          if (!item.name || typeof item.name !== 'string') {
            errors.push(`Item ${index + 1}: name is required`);
          }
          if (!item.quantity || typeof item.quantity !== 'number' || item.quantity <= 0) {
            errors.push(`Item ${index + 1}: quantity must be a positive number`);
          }
          if (!item.price || typeof item.price !== 'number' || item.price <= 0) {
            errors.push(`Item ${index + 1}: price must be a positive number`);
          }
        });
      }
    }

    // Validate expiry date if provided
    if (request.expiresAt) {
      if (!(request.expiresAt instanceof Date) || request.expiresAt <= new Date()) {
        errors.push('Expiry date must be a future date');
      }
    }

    if (errors.length > 0) {
      throw new PaymentValidationError(`Validation failed: ${errors.join(', ')}`);
    }
  }

  // Utility validation methods
  protected isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  protected isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  protected isValidPhone(phone: string): boolean {
    // Vietnamese phone number validation
    const phoneRegex = /^(\+84|84|0)(3|5|7|8|9)[0-9]{8}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  }

  // Common utility methods
  protected generateOrderCode(): string {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${timestamp}${random}`;
  }

  protected formatAmount(amount: number): number {
    // Ensure amount is an integer (no decimal places for VND)
    return Math.round(amount);
  }

  protected formatDescription(description: string, maxLength: number = PAYMENT_CONSTANTS.MAX_DESCRIPTION_LENGTH): string {
    const trimmed = description.trim();
    return trimmed.length > maxLength ? `${trimmed.substring(0, maxLength - 3)}...` : trimmed;
  }

  // Error handling utilities
  protected handleProviderError(error: any, operation: string): never {
    console.error(`${this.name} provider error during ${operation}:`, error);
    
    if (error.response?.data) {
      throw new Error(`${this.name} API error: ${JSON.stringify(error.response.data)}`);
    }
    
    if (error.message) {
      throw new Error(`${this.name} error: ${error.message}`);
    }
    
    throw new Error(`${this.name} unknown error during ${operation}`);
  }

  // Configuration validation
  protected validateConfig(requiredFields: string[]): void {
    const missingFields = requiredFields.filter(field => !this.config[field]);
    
    if (missingFields.length > 0) {
      throw new Error(`${this.name} provider missing required configuration: ${missingFields.join(', ')}`);
    }
  }

  // Logging utilities
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const logMessage = `[${this.name.toUpperCase()}] ${message}`;
    
    switch (level) {
      case 'info':
        console.log(logMessage, data || '');
        break;
      case 'warn':
        console.warn(logMessage, data || '');
        break;
      case 'error':
        console.error(logMessage, data || '');
        break;
    }
  }
}
