# Chat Retention Policy

This document describes the 60-day retention policy for chat messages in the application.

> **IMPORTANT NOTE**: This retention policy is prepared but NOT TO BE APPLIED YET. The implementation is ready but will be activated at a later date.

## Overview

To comply with data minimization principles and improve database performance, chat messages are automatically deleted after 60 days. This is implemented through:

1. A PostgreSQL trigger that runs automatically to delete old messages
2. A manual cleanup script that can be run on demand or scheduled

## Implementation Details

### Database Trigger

The database trigger is created in the migration file `lib/db/migrations/0006_chat_retention_policy.sql`. This trigger:

- Deletes votes for messages older than 60 days
- Deletes messages older than 60 days
- Deletes empty chats (chats with no messages)

The trigger is executed after each new message is inserted, ensuring regular cleanup.

### Manual Cleanup Script

The manual cleanup script `scripts/cleanup-old-chats.js` can be run to perform the same cleanup operation on demand. This is useful for:

- Initial cleanup when implementing the retention policy
- Scheduled cleanup via cron jobs or similar
- Troubleshooting if the automatic trigger is not working as expected

## Running the Cleanup Script

To run the cleanup script manually:

```bash
node scripts/cleanup-old-chats.js
```

### Setting Up a Scheduled Task

#### On Linux/Unix (using cron)

Add a cron job to run the script daily:

```bash
# Edit crontab
crontab -e

# Add this line to run the script daily at 3 AM
0 3 * * * cd /path/to/your/project && node scripts/cleanup-old-chats.js >> /path/to/logs/cleanup.log 2>&1
```

#### On Windows (using Task Scheduler)

1. Open Task Scheduler
2. Create a new task
3. Set the trigger to run daily
4. Set the action to run a program:
   - Program/script: `node`
   - Arguments: `scripts/cleanup-old-chats.js`
   - Start in: `C:\path\to\your\project`

## Applying the Migration

The migration will be applied automatically when the application is built or when the migration script is run:

```bash
# When building the application
npm run build

# Or run migrations directly
npx tsx lib/db/migrate
```

## Considerations

- **User Notification**: Consider notifying users about the 30-day retention policy in your application's privacy policy or terms of service.
- **Backup Strategy**: If you need to retain messages for compliance purposes, implement a separate backup strategy before deletion.
- **Performance Impact**: The trigger runs after each message insertion, which could impact performance for high-volume applications. Monitor database performance and adjust as needed.

## Troubleshooting

If messages are not being deleted as expected:

1. Check if the migration was applied successfully
2. Run the manual cleanup script to see if it reports any errors
3. Check the PostgreSQL logs for any trigger-related errors
4. Verify that the trigger exists in the database:

```sql
SELECT * FROM pg_trigger WHERE tgname = 'trigger_delete_old_messages';
```
