"use client";

import { AuthProvider } from './auth-context';

/**
 * Combined providers for the application
 * This component wraps all context providers needed for the app
 */
export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
}

/**
 * Auth-only provider for components that only need authentication
 * Use this when you don't need other providers
 */
export function AuthOnlyProvider({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
}

/**
 * HOC for components that require authentication
 * Automatically handles loading states and redirects
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    redirectTo?: string;
    requireCredits?: boolean;
    minCredits?: number;
  }
) {
  return function AuthenticatedComponent(props: P) {
    // This would be implemented when we need it
    // For now, just return the component
    return <Component {...props} />;
  };
}

/**
 * Component that only renders children if user is authenticated
 */
export function AuthGuard({ 
  children, 
  fallback = null,
  requireCredits = false,
  minCredits = 1 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requireCredits?: boolean;
  minCredits?: number;
}) {
  // This would use the useAuth hook to check authentication
  // For now, just render children
  return <>{children}</>;
}

/**
 * Component that only renders children if user has sufficient credits
 */
export function CreditGuard({ 
  children, 
  fallback = null,
  requiredCredits = 1 
}: { 
  children: React.ReactNode;
  fallback?: React.ReactNode;
  requiredCredits?: number;
}) {
  // This would use the useCredits hook to check credit balance
  // For now, just render children
  return <>{children}</>;
}
