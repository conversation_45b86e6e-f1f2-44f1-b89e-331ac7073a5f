"use client";

import { useState, useEffect, useCallback } from "react";
import Link from "next/link";
import { ArrowLeft, History, Loader2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CreditDisplay } from "@/components/credit-display";
import { useAuth } from "@/lib/auth/auth-context";
import { useCredits } from "@/lib/hooks/use-credits";
import type { CreditTransaction } from "@/lib/types/credit";
import { TransactionItem } from "@/components/credit-history/transaction-item";
import { NoTransactionsState, ErrorState } from "@/components/credit-history/empty-state";
import { TransactionFilters } from "@/components/credit-history/transaction-filters";
import { SimplePagination } from "@/components/credit-history/transaction-pagination";

export default function CreditHistoryPage() {
  const { credits, user } = useAuth();
  const { getCreditHistory, isLoadingHistory } = useCredits();
  const [transactions, setTransactions] = useState<CreditTransaction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const itemsPerPage = 20;

  // Filter state
  const [dateFilter, setDateFilter] = useState<string>("all");
  const [typeFilter, setTypeFilter] = useState<string>("all");

  const loadTransactions = useCallback(async () => {
    if (!user?.id) {
      setError("User not authenticated");
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const offset = (currentPage - 1) * itemsPerPage;
      let result = await getCreditHistory(itemsPerPage, offset);

      // Apply client-side filtering for now
      // TODO: Move filtering to server-side for better performance
      if (dateFilter !== "all" || typeFilter !== "all") {
        result = result.filter((transaction) => {
          // Date filtering
          if (dateFilter !== "all") {
            const transactionDate = new Date(transaction.created_at);
            const now = new Date();

            switch (dateFilter) {
              case "today": {
                if (transactionDate.toDateString() !== now.toDateString()) return false;
                break;
              }
              case "week": {
                const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                if (transactionDate < weekAgo) return false;
                break;
              }
              case "month": {
                const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                if (transactionDate < monthAgo) return false;
                break;
              }
              case "quarter": {
                const quarterAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                if (transactionDate < quarterAgo) return false;
                break;
              }
            }
          }

          // Type filtering
          if (typeFilter !== "all" && transaction.transaction_type !== typeFilter) {
            return false;
          }

          return true;
        });
      }

      setTransactions(result);
      setHasMore(result.length === itemsPerPage);
    } catch (err) {
      console.error("Failed to load credit history:", err);
      setError("Failed to load transaction history. Please try again.");
    } finally {
      setLoading(false);
    }
  }, [user?.id, currentPage, dateFilter, typeFilter, getCreditHistory, itemsPerPage]);

  useEffect(() => {
    if (user?.id) {
      loadTransactions();
    }
  }, [user?.id, currentPage, dateFilter, typeFilter, loadTransactions]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleDateFilterChange = (value: string) => {
    setDateFilter(value);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleTypeFilterChange = (value: string) => {
    setTypeFilter(value);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleClearFilters = () => {
    setDateFilter("all");
    setTypeFilter("all");
    setCurrentPage(1);
  };

  return (
    <div className="flex flex-col min-w-0 h-dvh bg-background">
      {/* Header - following chat header pattern */}
      <header className="flex sticky top-0 bg-background py-1.5 items-center px-2 md:px-2 gap-2 safe-area-top border-b">
        <Button variant="ghost" size="sm" asChild className="shrink-0">
          <Link href="/" className="gap-2">
            <ArrowLeft className="size-4" />
            <span className="hidden sm:inline">Back to Chat</span>
            <span className="sm:hidden">Back</span>
          </Link>
        </Button>
      </header>

      {/* Main Content - scrollable area */}
      <main className="flex-1 overflow-y-auto px-3 sm:px-4 md:px-6 py-4 sm:py-6">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-center gap-3 mb-4 sm:mb-6">
            <div className="flex items-center justify-center size-8 sm:size-10 rounded-full bg-primary/10 shrink-0">
              <History className="size-4 sm:size-5 text-primary" />
            </div>
            <div className="min-w-0">
              <h1 className="text-xl sm:text-2xl font-bold tracking-tight">Credit History</h1>
              <p className="text-sm sm:text-base text-muted-foreground">View your credit usage and transaction history</p>
            </div>
          </div>

          {/* Current Balance Card */}
          <Card className="mb-4 sm:mb-6">
            <CardHeader className="pb-3 sm:pb-6">
              <CardTitle className="text-base sm:text-lg">Current Balance</CardTitle>
              <CardDescription className="text-sm">Your available credits</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-start gap-3">
                <CreditDisplay size="md" />
                <div className="text-xs sm:text-sm text-muted-foreground">
                  <p>Credits are used for chat messages and tool usage.</p>
                  <p>Each message costs 1 credit.</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Filters Section */}
          <TransactionFilters
            dateFilter={dateFilter}
            typeFilter={typeFilter}
            onDateFilterChange={handleDateFilterChange}
            onTypeFilterChange={handleTypeFilterChange}
            onClearFilters={handleClearFilters}
            className="mb-4 sm:mb-6"
          />

          {/* Transaction List */}
          <Card>
            <CardHeader className="pb-3 sm:pb-6">
              <CardTitle className="text-base sm:text-lg">Transaction History</CardTitle>
              <CardDescription className="text-sm">All your credit transactions and usage history</CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              {loading ? (
                <div className="flex items-center justify-center py-6 sm:py-8">
                  <Loader2 className="size-5 sm:size-6 animate-spin mr-2" />
                  <span className="text-sm sm:text-base">Loading transaction history...</span>
                </div>
              ) : error ? (
                <ErrorState error={error} onRetry={loadTransactions} />
              ) : transactions.length === 0 ? (
                <NoTransactionsState />
              ) : (
                <div className="space-y-2 sm:space-y-3">
                  {transactions.map((transaction) => (
                    <TransactionItem key={transaction.id} transaction={transaction} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Pagination */}
          {!loading && !error && transactions.length > 0 && (
            <div className="mt-4 sm:mt-6">
              <SimplePagination currentPage={currentPage} hasMore={hasMore} onPageChange={handlePageChange} loading={loading} />
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
