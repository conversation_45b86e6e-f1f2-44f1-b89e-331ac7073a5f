// Helper function to convert god names to elements
function convertGodNamesToElements(godNames, nhatTruElement) {
  const CAN_TO_ELEMENT = {
    Giáp: "Mộ<PERSON>",
    Ất: "<PERSON>ộ<PERSON>",
    <PERSON><PERSON>h: "Hỏa",
    <PERSON><PERSON><PERSON>: "Hỏa",
    <PERSON><PERSON><PERSON>: "<PERSON><PERSON><PERSON>",
    Kỷ: "<PERSON>h<PERSON>",
    <PERSON>h: "<PERSON>",
    <PERSON><PERSON>: "<PERSON>",
    <PERSON><PERSON>âm: "Thủ<PERSON>",
    <PERSON><PERSON><PERSON>: "Thủy",
  };

  // Element relationships based on the day master element
  const getElementRelationships = (element) => {
    const elements = ["<PERSON>", "Thủy", "<PERSON>ộ<PERSON>", "Hỏa", "Thổ"];
    const relationships = [
      ["Th<PERSON>", "Hỏa", "<PERSON>ộ<PERSON>", "<PERSON>", "<PERSON>hủ<PERSON>"], // <PERSON>
      ["<PERSON>", "<PERSON>h<PERSON>", "Hỏa", "Thủy", "<PERSON>ộ<PERSON>"], // Thủy
      ["Thủy", "<PERSON>", "Thổ", "<PERSON>ộ<PERSON>", "Hỏa"], // <PERSON><PERSON><PERSON>
      ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Hỏa", "<PERSON><PERSON><PERSON>"], // Hỏa
      ["Hỏa", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], // Thổ
    ];

    const labels = ["Ấn Tinh", "Quan Tinh", "Tài Tinh", "Tỷ Kiếp", "Thực Thương"];
    const index = elements.indexOf(element);

    if (index === -1) {
      return null;
    }

    const result = {};
    labels.forEach((label, i) => {
      result[label] = relationships[index][i];
    });

    return result;
  };

  const relationships = getElementRelationships(nhatTruElement);
  if (!relationships) return godNames; // fallback to original names if can't determine relationships

  // Map god names to their corresponding elements
  const godToElementMap = {
    // Ấn Tinh group
    "Chính Ấn": relationships["Ấn Tinh"],
    "Thiên Ấn": relationships["Ấn Tinh"],
    "Ấn Tinh": relationships["Ấn Tinh"],

    // Quan Tinh group
    "Chính Quan": relationships["Quan Tinh"],
    "Thiên Quan": relationships["Quan Tinh"],
    "Thất Sát": relationships["Quan Tinh"],
    "Quan Tinh": relationships["Quan Tinh"],

    // Tài Tinh group
    "Chính Tài": relationships["Tài Tinh"],
    "Thiên Tài": relationships["Tài Tinh"],
    "Tài Tinh": relationships["Tài Tinh"],

    // Tỷ Kiếp group
    "Tỷ Kiên": relationships["Tỷ Kiếp"],
    "Kiếp Tài": relationships["Tỷ Kiếp"],
    "Tỷ Kiếp": relationships["Tỷ Kiếp"],

    // Thực Thương group
    "Thực Thần": relationships["Thực Thương"],
    "Thương Quan": relationships["Thực Thương"],
    "Thực Thương": relationships["Thực Thương"],
  };

  // Convert god names to elements, removing duplicates
  const elements = [...new Set(godNames.map((godName) => godToElementMap[godName] || godName))];
  return elements;
}

/**
 * Phân tích Tứ Trụ để thu thập 8 Thập Thần chính (4 Can và 4 Can ẩn mạnh nhất của Chi).
 * @param {object} data - Đối tượng dữ liệu đầu vào.
 * @returns {string[]} - Mảng chứa 8 Thập Thần.
 */
function thuThapTamThapThan(data) {
  const tamThapThan = [];
  const tuTru = data["Tứ trụ restructure"];
  const thapThanMap = data["Thập Thần"]; // Map Thập Thần đã so với Nhật Chủ

  const cacTruKey = ["year", "month", "day", "hour"];
  console.log(data);
  for (const truKey of cacTruKey) {
    const truData = tuTru[truKey]; // Now this is an object with can/chi structure
    if (!truData || !truData.can || !truData.chi) {
      console.warn(`Trụ ${truKey} không hợp lệ:`, truData);
      continue;
    }

    const canCuaTru = truData.can.name; // Get can name from the new structure
    const chiCuaTru = truData.chi.name; // Get chi name from the new structure

    // 1. Thập Thần của Thiên Can của Trụ
    if (thapThanMap[canCuaTru]) {
      tamThapThan.push(thapThanMap[canCuaTru]);
    } else {
      console.warn(`Không tìm thấy Thập Thần cho Can: ${canCuaTru} trong thapThanMap`);
    }

    // 2. Thập Thần của Thiên Can ẩn mạnh nhất trong Địa Chi của Trụ
    // Now get hidden cans from chi.hiddens instead of separate array
    const mangTangCan = truData.chi.hiddens;
    if (mangTangCan && mangTangCan.length > 0) {
      const canAnManhNhat = mangTangCan[0]; // Lấy Can ẩn đầu tiên (mạnh nhất)
      if (thapThanMap[canAnManhNhat]) {
        tamThapThan.push(thapThanMap[canAnManhNhat]);
      } else {
        console.warn(`Không tìm thấy Thập Thần cho Can ẩn: ${canAnManhNhat} từ Trụ ${truKey}`);
      }
    } else {
      console.warn(`Không có tàng can cho Trụ: ${truKey}`);
    }
  }
  return tamThapThan;
}

/**
 * Tính Dụng Thần và Kỵ Thần dựa trên Tứ Trụ và Thập Thần đã được xử lý.
 * @param {object} dataInput - Đối tượng dữ liệu đầu vào như bạn cung cấp.
 * @returns {object} - Đối tượng chứa Dụng Thần và Kỵ Thần (trả về elements thay vì god names).
 */
function tinhDungThanTuDuLieu(dataInput) {
  let dungThan = [];
  let kyThan = [];
  let thongBao = "";

  // Get day master element for conversion
  const nhatChuCanChi = dataInput["Tứ trụ restructure"].day;
  const nhatChuCan = nhatChuCanChi.can.name; // Updated to use new structure
  const CAN_TO_ELEMENT = {
    Giáp: "Mộc",
    Ất: "Mộc",
    Bính: "Hỏa",
    Đinh: "Hỏa",
    Mậu: "Thổ",
    Kỷ: "Thổ",
    Canh: "Kim",
    Tân: "Kim",
    Nhâm: "Thủy",
    Quý: "Thủy",
  };
  const nhatTruElement = CAN_TO_ELEMENT[nhatChuCan];

  const allThapThan = thuThapTamThapThan(dataInput);
  //   console.log(allThapThan);
  if (allThapThan.length !== 8) {
    console.warn("Không thu thập đủ 8 Thập Thần, kết quả có thể không chính xác. Số lượng Thập Thần thu được:", allThapThan.length, allThapThan);
    // Có thể quyết định dừng ở đây hoặc tiếp tục với những gì có
  }

  // Hàm tính tổng số lượng Thập Thần cụ thể
  function demThapThan(tenThapThanArray) {
    return allThapThan.filter((tt) => tenThapThanArray.includes(tt)).length;
  }

  // Các loại Thập Thần (điều chỉnh cho phù hợp với tên trong dataInput['Thập Thần'])
  // Ví dụ: dataInput['Thập Thần'] có 'Tân': 'Chính Ấn'
  const AN_TINH = ["Chính Ấn", "Thiên Ấn"];
  const TY_KIEP = ["Tỷ Kiên", "Kiếp Tài"];
  const TAI_TINH = ["Chính Tài", "Thiên Tài"];
  const THUC_THUONG = ["Thực Thần", "Thương Quan"];
  // Lưu ý: 'Thất Sát' và 'Thiên Quan' thường là một. Nếu data['Thập Thần'] dùng 'Thiên Quan', cần đưa vào đây.
  const THAT_SAT_GROUP = ["Thất Sát", "Thiên Quan"]; // Gộp cả 2 để đảm bảo khớp
  const CHINH_QUAN_GROUP = ["Chính Quan"];

  // 1. Nếu tổng ngũ hành của Can và Chi trong bát tự của Ấn Tinh và Tỷ Kiếp >= 7
  const tongAnTy = demThapThan([...AN_TINH, ...TY_KIEP]);
  if (tongAnTy >= 7) {
    dungThan = [...AN_TINH, ...TY_KIEP];
    kyThan = [...TAI_TINH, ...THUC_THUONG];
    thongBao = `Dụng thần là ${[...new Set(dungThan)].join(", ")} vì tổng Ấn Tinh và Tỷ Kiếp (${tongAnTy}) >= 7.`;

    // Convert god names to elements
    const dungThanElements = convertGodNamesToElements([...new Set(dungThan)], nhatTruElement);
    const kyThanElements = convertGodNamesToElements([...new Set(kyThan)], nhatTruElement);
    return { dungThan: dungThanElements, kyThan: kyThanElements, thongBao, allThapThan };
  }

  // 2. Nếu không thỏa mãn điều kiện trên, xét tiếp tới tổng ngũ hành của Can và chi của Tài Tinh >= 5
  const tongTaiTinh = demThapThan(TAI_TINH);
  if (tongTaiTinh >= 5) {
    dungThan = [...TAI_TINH, ...THUC_THUONG];
    kyThan = [...TY_KIEP, ...AN_TINH];
    thongBao = `Dụng thần là ${[...new Set(dungThan)].join(", ")} vì tổng Tài Tinh (${tongTaiTinh}) >= 5.`;

    // Convert god names to elements
    const dungThanElements = convertGodNamesToElements([...new Set(dungThan)], nhatTruElement);
    const kyThanElements = convertGodNamesToElements([...new Set(kyThan)], nhatTruElement);
    return { dungThan: dungThanElements, kyThan: kyThanElements, thongBao, allThapThan };
  }

  // 3. Tương tự, xét tiếp tổng ngũ hành của can và chi của Thất Sát >= 5
  const tongThatSat = demThapThan(THAT_SAT_GROUP);
  if (tongThatSat >= 5) {
    dungThan = [...THAT_SAT_GROUP, ...TAI_TINH];
    kyThan = [...TY_KIEP, ...THUC_THUONG];
    thongBao = `Dụng thần là ${[...new Set(dungThan)].join(", ")} vì tổng Thất Sát (${tongThatSat}) >= 5.`;

    // Convert god names to elements
    const dungThanElements = convertGodNamesToElements([...new Set(dungThan)], nhatTruElement);
    const kyThanElements = convertGodNamesToElements([...new Set(kyThan)], nhatTruElement);
    return { dungThan: dungThanElements, kyThan: kyThanElements, thongBao, allThapThan };
  }

  // 4. Tiếp tục, nếu không đạt thì xét tổng ngũ hành can và chi của Thực Thương >= 5
  const tongThucThuong = demThapThan(THUC_THUONG);
  if (tongThucThuong >= 5) {
    dungThan = [...TAI_TINH, ...CHINH_QUAN_GROUP];
    kyThan = [...TY_KIEP];
    thongBao = `Dụng thần là ${[...new Set(dungThan)].join(", ")} vì tổng Thực Thương (${tongThucThuong}) >= 5.`;

    // Convert god names to elements
    const dungThanElements = convertGodNamesToElements([...new Set(dungThan)], nhatTruElement);
    const kyThanElements = convertGodNamesToElements([...new Set(kyThan)], nhatTruElement);
    return { dungThan: dungThanElements, kyThan: kyThanElements, thongBao, allThapThan };
  }

  // Nếu không có quy tắc nào được thỏa mãn
  thongBao = "Không xác định được Dụng Thần theo các quy tắc đã cho.";
  return { dungThan, kyThan, thongBao, allThapThan };
}

// ----- VÍ DỤ SỬ DỤNG -----

const sampleInput = {
  "Tứ trụ": {
    month: {
      can: { name: "Mậu", element: "Thổ" },
      chi: { name: "Tuất", element: "Thổ", hiddens: ["Mậu", "Tân", "Đinh"] },
    },
    day: {
      can: { name: "Đinh", element: "Hỏa" },
      chi: { name: "Mão", element: "Mộc", hiddens: ["Ất"] },
    },
    year: {
      can: { name: "Tân", element: "Kim" },
      chi: { name: "Mùi", element: "Thổ", hiddens: ["Kỷ", "Đinh", "Ất"] },
    },
    hour: {
      can: { name: "Bính", element: "Hỏa" },
      chi: { name: "Ngọ", element: "Hỏa", hiddens: ["Đinh", "Kỷ"] },
    },
  },
  "Tứ Trụ Power": {
    month: 0,
    day: 0, // Power của Nhật chủ
    year: 0,
    hour: 0,
  },
  "Lực lượng ngũ hành": { Mộc: 120, Hỏa: 270, Thổ: 280, Kim: 130, Thủy: 0 },
  "Thập Thần": {
    // So với Nhật chủ Đinh (Hỏa)
    Canh: "Thiên Tài",
    Tân: "Chính Tài", // Kim bị Hỏa khắc (Tài)
    Mậu: "Thực Thần",
    Kỷ: "Thương Quan", // Hỏa sinh Thổ (Thực Thương)
    Bính: "Tỷ Kiên",
    Đinh: "Tỷ Kiên", // Hỏa cùng hành (Tỷ Kiếp)
    Nhâm: "Thiên Quan",
    Quý: "Chính Quan", // Thủy khắc Hỏa (Quan)
    Giáp: "Thiên Ấn",
    Ất: "Chính Ấn", // Mộc sinh Hỏa (Ấn)
  },
};

// const ketQuaTinhToan = tinhDungThanTuDuLieu(sampleInput);
// console.log("Danh sách 8 Thập Thần đã thu thập:", ketQuaTinhToan.allThapThan);
// console.log("Kết quả Dụng Thần - Kỵ Thần:", {
//   dungThan: ketQuaTinhToan.dungThan,
//   kyThan: ketQuaTinhToan.kyThan,
//   thongBao: ketQuaTinhToan.thongBao,
// });

function tinhDungThanKyThan(input) {
  // --- 0. Dữ liệu cố định và Ánh xạ (Hardcoded Mappings) ---
  const CAN_TO_ELEMENT = {
    Giáp: "Mộc",
    Ất: "Mộc",
    Bính: "Hỏa",
    Đinh: "Hỏa",
    Mậu: "Thổ",
    Kỷ: "Thổ",
    Canh: "Kim",
    Tân: "Kim",
    Nhâm: "Thủy",
    Quý: "Thủy",
  };

  const DIACHI_TO_ELEMENT = {
    // Ngũ hành chính của Địa Chi
    Tý: "Thủy",
    Sửu: "Thổ",
    Dần: "Mộc",
    Mão: "Mộc",
    Thìn: "Thổ",
    Tỵ: "Hỏa",
    Ngọ: "Hỏa",
    Mùi: "Thổ",
    Thân: "Kim",
    Dậu: "Kim",
    Tuất: "Thổ",
    Hợi: "Thủy",
  };

  // Các nhóm Thập Thần (sử dụng tên chung như trong logic)
  // Key là tên nhóm, value là mảng các Thập Thần cụ thể thuộc nhóm đó
  const THAPTHAN_GROUPS = {
    QuanTinh: ["Chính Quan", "Thiên Quan"], // Dùng cho các trường hợp "Quan Tinh" chung
    AnTinh: ["Chính Ấn", "Thiên Ấn"],
    TaiTinh: ["Chính Tài", "Thiên Tài"],
    ThucThuong: ["Thực Thần", "Thương Quan"],
    TyKiep: ["Tỷ Kiên", "Kiếp Tài"],
    // Thêm các Thập Thần cụ thể để dễ tra cứu khi logic yêu cầu đích danh
    ChinhQuan: ["Chính Quan"],
    ThienQuan: ["Thiên Quan"],
    ChinhAn: ["Chính Ấn"],
    ThienAn: ["Thiên Ấn"],
    ChinhTai: ["Chính Tài"],
    ThienTai: ["Thiên Tài"],
    ThucThan: ["Thực Thần"],
    ThuongQuan: ["Thương Quan"],
    TyKien: ["Tỷ Kiên"],
    KiepTai: ["Kiếp Tài"],
  };

  // --- 1. Chuẩn bị dữ liệu đầu vào ---

  const nhatChuCanChi = input["Tứ trụ restructure"].day;
  const nhatChuCan = nhatChuCanChi.can.name;
  const nhatChuElement = CAN_TO_ELEMENT[nhatChuCan];

  const fullPillarsInfo = ["year", "month", "day", "hour"].map((key) => {
    const canChi = input["Tứ trụ restructure"][key];
    const thienCan = canChi.can.name;
    const diaChi = canChi.chi.name;
    return {
      key: key, // year, month, day, hour
      canChi: `${thienCan} ${diaChi}`, // Reconstruct for compatibility
      thienCan: thienCan,
      diaChi: diaChi,
      thienCanElement: CAN_TO_ELEMENT[thienCan],
      thapThanThienCan: input["Thập Thần"][thienCan], // Đây là Thập Thần cụ thể
      power: input["Tứ Trụ Power"][key] === 0 ? "Yếu" : "Mạnh",
    };
  });

  let tongLucLuongNguHanh = 0;
  for (const element in input["Lực lượng ngũ hành"]) {
    tongLucLuongNguHanh += input["Lực lượng ngũ hành"][element];
  }
  if (tongLucLuongNguHanh === 0) tongLucLuongNguHanh = 1; // Tránh chia cho 0

  const monthCanChi = input["Tứ trụ restructure"].month;
  const monthDiaChi = monthCanChi.chi.name;
  const nguyetChiElement = DIACHI_TO_ELEMENT[monthDiaChi];

  let nguyetChiThapThan = null; // Thập thần cụ thể (VD: 'Chính Quan')
  let monthHiddenCans = [];

  // Get hidden cans from the new structure
  monthHiddenCans = monthCanChi.chi.hiddens || [];
  if (monthHiddenCans.length > 0) {
    const firstHiddenCan = monthHiddenCans[0]; // Can ẩn đầu tiên, mạnh nhất
    if (input["Thập Thần"][firstHiddenCan]) {
      nguyetChiThapThan = input["Thập Thần"][firstHiddenCan];
    }
  }

  // --- Helper Functions ---

  // Lấy danh sách các Thập Thần cụ thể từ tên nhóm hoặc tên cụ thể
  function getSpecificThapThanNames(groupOrSpecificName) {
    return THAPTHAN_GROUPS[groupOrSpecificName] || [groupOrSpecificName];
  }

  // Kiểm tra Thập Thần (nhóm hoặc cụ thể) có hiện diện ở Thiên Can không
  function isThapThanPresent(groupOrSpecificName) {
    const targetSpecificNames = getSpecificThapThanNames(groupOrSpecificName);
    // should check if all elements in the group are present in the fullPillarsInfo
    return targetSpecificNames.some((name) => fullPillarsInfo.some((pillar) => pillar.thapThanThienCan === name));
  }

  // Kiểm tra "mạnh" cho Bước 1 và Bước 2
  function isThapThanManhBuoc1Buoc2(groupOrSpecificName) {
    const targetSpecificNames = getSpecificThapThanNames(groupOrSpecificName);
    return fullPillarsInfo.some((pillar) => pillar.key !== "day" && targetSpecificNames.includes(pillar.thapThanThienCan) && pillar.power === "Mạnh");
  }

  // Tính toán "mạnh" theo quy tắc 0.35
  function isManh05(thapThanGroupNamesArray) {
    // VD: ['AnTinh'] hoặc ['TyKiep', 'AnTinh']
    let combinedLucLuong = 0;

    const specificNamesToSum = [];
    thapThanGroupNamesArray.forEach((groupName) => {
      const specificNames = getSpecificThapThanNames(groupName);
      specificNames.forEach((sName) => {
        if (!specificNamesToSum.includes(sName)) {
          specificNamesToSum.push(sName);
        }
      });
    });

    // Iterate through all Can names in the Thập Thần mapping
    for (const can in input["Thập Thần"]) {
      const thapThanOfCan = input["Thập Thần"][can]; // Thập thần của Can này so với Nhật chủ
      if (specificNamesToSum.includes(thapThanOfCan)) {
        // Map the Can to its element and add the element's strength
        const canElement = CAN_TO_ELEMENT[can];
        if (canElement && input["Lực lượng ngũ hành"][canElement] !== undefined) {
          combinedLucLuong += input["Lực lượng ngũ hành"][canElement];
        }
      }
    }
    return combinedLucLuong / tongLucLuongNguHanh > 0.5;
  }

  // Kiểm tra một Thập Thần cụ thể có thuộc nhóm nào đó không
  function isThapThanInGroup(specificThapThanName, groupName) {
    if (!specificThapThanName) return false;
    const groupMembers = THAPTHAN_GROUPS[groupName];
    return groupMembers ? groupMembers.includes(specificThapThanName) : false;
  }

  let dungThan = [];
  let kyThan = [];
  let hyThan = [];
  let resultFound = false;

  // --- LOGIC CÁC BƯỚC ---

  // Bước 1: Xét Chính Quan
  const coChinhQuan = isThapThanPresent("ChinhQuan");
  const chinhQuanManhBuoc1 = coChinhQuan && isThapThanManhBuoc1Buoc2("ChinhQuan");

  if (chinhQuanManhBuoc1) {
    const coThienQuanBuoc1 = isThapThanPresent("ThienQuan");
    if (coThienQuanBuoc1) {
      dungThan = ["Ấn Tinh"]; // Nên lấy tên nhóm chung
      kyThan = ["Tài Tinh"];
    } else {
      dungThan = ["Quan Tinh"];
      kyThan = ["Thực Thương"];
    }
    resultFound = true;
  }

  // Bước 2: Nếu không rơi vào Bước 1, xét Thiên Quan
  if (!resultFound) {
    const coThienQuan = isThapThanPresent("ThienQuan");
    const thienQuanManhBuoc2 = coThienQuan && isThapThanManhBuoc1Buoc2("ThienQuan");
    if (thienQuanManhBuoc2) {
      // const coChinhQuanBuoc2 = isThapThanPresent('ChinhQuan'); // Đã kiểm tra ở Bước 1 là không mạnh hoặc không có
      if (coChinhQuan) {
        // Chính Quan có thể có nhưng không mạnh
        dungThan = ["Ấn Tinh"];
        kyThan = ["Tài Tinh"];
      } else {
        dungThan = ["Thực Thương"];
        kyThan = ["Quan Tinh"];
      }
      resultFound = true;
    }
  }

  // Bước 3: (Ghi nhận `nguyetChiThapThan` đã được chuẩn bị)
  // Từ đây "mạnh" sẽ được tính theo isManh05

  // Bước 3.1: Xet nguyetChiThapThan là 'ChinhQuan'
  if (!resultFound && isThapThanInGroup(nguyetChiThapThan, "ChinhQuan")) {
    const coThienQuanBuoc31 = isThapThanPresent("ThienQuan");
    if (coThienQuanBuoc31) {
      dungThan = ["Ấn Tinh"];
      kyThan = ["Tài Tinh"];
    } else {
      dungThan = ["Quan Tinh"];
      kyThan = ["Thực Thương"];
    }
    resultFound = true;
  }

  // Bước 3.2: Xet nguyetChiThapThan là 'ThienQuan'
  if (!resultFound && isThapThanInGroup(nguyetChiThapThan, "ThienQuan")) {
    const coChinhQuanBuoc32 = isThapThanPresent("ChinhQuan");
    if (coChinhQuanBuoc32) {
      dungThan = ["Quan Tinh"];
      kyThan = ["Thực Thương"];
    } else {
      const coThienQuanBuoc32 = isThapThanPresent("ThienQuan");
      if (coThienQuanBuoc32) {
        dungThan = ["Thực Thương"];
        kyThan = ["Quan Tinh"];
      } else {
        const coAnTinhBuoc32 = isThapThanPresent("AnTinh");
        const anTinhManhBuoc32 = coAnTinhBuoc32 && isThapThanManhBuoc1Buoc2("AnTinh");
        if (anTinhManhBuoc32) {
          dungThan = ["Ấn Tinh"];
          kyThan = ["Tài Tinh"];
        } else {
          const coThucThuongBuoc32 = isThapThanPresent("ThucThuong");
          const thucThuongManhBuoc32 = coThucThuongBuoc32 && isThapThanManhBuoc1Buoc2("ThucThuong");
          if (thucThuongManhBuoc32) {
            dungThan = ["Quan Tinh"];
            kyThan = ["Thực Thương"];
          } else {
          }
        }
      }
    }
    if (dungThan.length > 0 || kyThan.length > 0) {
      resultFound = true;
    }
  }

  // Bước 4: Nếu nguyetChiThapThan là 'TaiTinh'
  if (!resultFound && isThapThanInGroup(nguyetChiThapThan, "TaiTinh")) {
    // const coQuanTinhBuoc4 = isThapThanPresent("QuanTinh"); // QuanTinh là nhóm (CQ hoặc TQ)
    // if (coQuanTinhBuoc4) {
    //   const coThienQuanBuoc4 = isThapThanPresent("ThienQuan");
    //   if (coThienQuanBuoc4) {
    //     dungThan = ["Ấn Tinh"];
    //     kyThan = ["Tài Tinh"];
    //   } else {
    //     // Chỉ có Chính Quan
    //     dungThan = ["Tài Tinh"];
    //     kyThan = ["Tỷ Kiếp"];
    //   }
    // } else {
    //   // Không có Quan Tinh (CQ lẫn TQ)
    const thucThuongThauBuoc4 = isThapThanPresent("ThucThuong");
    if (thucThuongThauBuoc4) {
      dungThan = ["Thực Thương"];
      kyThan = ["Ấn Tinh"];
    } else {
      dungThan = ["Tài Tinh"]; // Theo yêu cầu, dù Quan Tinh có thể không có
      kyThan = ["Tỷ Kiếp"];
    }
    // }
    resultFound = true;
  }

  if (!resultFound && isThapThanInGroup(nguyetChiThapThan, "AnTinh")) {
    // const coQuanTinhBuoc4 = isThapThanPresent("QuanTinh"); // QuanTinh là nhóm (CQ hoặc TQ)
    // if (coQuanTinhBuoc4) {
    //   const coThienQuanBuoc4 = isThapThanPresent("ThienQuan");
    //   if (coThienQuanBuoc4) {
    //     dungThan = ["Ấn Tinh"];
    //     kyThan = ["Tài Tinh"];
    //   } else {
    //     // Chỉ có Chính Quan
    //     dungThan = ["Tài Tinh"];
    //     kyThan = ["Tỷ Kiếp"];
    //   }
    // } else {
    dungThan = ["Ấn Tinh"];
    kyThan = ["Tài Tinh"];
    // }
    resultFound = true;
  }

  // Bước 5: Nếu nguyetChiThapThan là 'TyKiep'
  if (!resultFound && isThapThanInGroup(nguyetChiThapThan, "TyKiep")) {
    const quanTinhBuoc5 = isThapThanPresent("QuanTinh");
    if (quanTinhBuoc5) {
      dungThan = ["Tài Tinh"];
      kyThan = ["Tỷ Kiếp"];
    } else {
      const thucThuongTaiTinhManhBuoc5 = isManh05(["ThucThuong", "TaiTinh"]);
      if (thucThuongTaiTinhManhBuoc5) {
        dungThan = ["Thực Thương"];
        kyThan = ["Ấn Tinh"];
      } else {
        // const coChinhQuanBuoc5 = isThapThanPresent("ChinhQuan");
        // if (coChinhQuanBuoc5) {
        //   const coThienQuanBuoc5 = isThapThanPresent("ThienQuan");
        //   if (coThienQuanBuoc5) {
        //   }
        // } else {
        // Không có Chính Quan
        const tyKiepAnTinhManhBuoc5 = isManh05(["TyKiep", "AnTinh"]);
        if (tyKiepAnTinhManhBuoc5) {
          // Không mạnh
          dungThan = ["Tỷ Kiếp"];
          kyThan = ["Thực Thương"];
        } else {
          // Có mạnh
          dungThan = ["Thực Thương"];
          kyThan = ["Ấn Tinh"];
        }
        // }
      }
      // Nếu có DT/KT được xác định ở nhánh này (trừ trường hợp TODO)
      if (dungThan.length > 0 || kyThan.length > 0) {
        resultFound = true;
      }
    }
  }

  // Bước 6: Nếu nguyetChiThapThan là 'ThucThuong'
  if (!resultFound && isThapThanInGroup(nguyetChiThapThan, "ThucThuong")) {
    const anTinhManhBuoc6 = isManh05(["AnTinh"]);
    if (anTinhManhBuoc6) {
      dungThan = ["Thực Thương"];
      kyThan = ["Ấn Tinh"];
    } else {
      // // Ấn Tinh không mạnh hoặc không có
      // const coQuanTinhBuoc6 = isThapThanPresent("QuanTinh");
      // if (coQuanTinhBuoc6) {
      //   const coThienQuanBuoc6 = isThapThanPresent("ThienQuan");
      //   if (coThienQuanBuoc6) {
      //     dungThan = ["Ấn Tinh"];
      //     kyThan = ["Tài Tinh"];
      //   } else {
      //     // Chỉ có Chính Quan
      //     dungThan = ["Quan Tinh"];
      //     kyThan = ["Thực Thương"];
      //   }
      // } else {
      // Quan Tinh không có
      const taiTinhThauBuoc6 = isThapThanPresent("TaiTinh");
      if (taiTinhThauBuoc6) {
        dungThan = ["Tài Tinh"]; // Theo yêu cầu
        kyThan = ["Tỷ Kiếp"];
      } else {
        const tyKiepThauBuoc6 = isThapThanPresent("TyKiep");
        if (tyKiepThauBuoc6) {
          dungThan = ["Tài Tinh"]; // Theo yêu cầu
          kyThan = ["Ấn Tinh"];
        } else {
          // Không có gì, sẽ rơi vào Bước 7
        }
      }
      // }
    }
    if (dungThan.length > 0 || kyThan.length > 0) {
      resultFound = true;
    }
  }

  // Bước 7: Trường hợp không ứng với bất kỳ trường hợp nào ở trên
  if (!resultFound && dungThan.length === 0) {
    // Kiểm tra dungThan.length để tránh ghi đè nếu có TODO ở bước 5
    if (nguyetChiThapThan) {
      // Phải có Thập Thần Nguyệt Chi để làm Dụng Thần
      dungThan = [nguyetChiThapThan]; // Dụng thần là Thập Thần cụ thể của Nguyệt Chi

      const NGUHANH_SINH = { Kim: "Thổ", Mộc: "Thủy", Thủy: "Kim", Hỏa: "Mộc", Thổ: "Hỏa" };
      const NGUHANH_KHAC = { Kim: "Hỏa", Mộc: "Kim", Thủy: "Thổ", Hỏa: "Thủy", Thổ: "Mộc" };

      const dtElement = nguyetChiElement;
      // console.log(dtElement); // Ngũ hành của Địa chi tháng
      if (dtElement) {
        const hyThanElement = NGUHANH_SINH[dtElement];
        const kyThanElement = NGUHANH_KHAC[dtElement];

        const tempHyThan = [];
        const tempKyThan = [];

        for (const can in input["Thập Thần"]) {
          if (CAN_TO_ELEMENT[can] === hyThanElement) {
            const tt = input["Thập Thần"][can];
            if (!tempHyThan.includes(tt)) tempHyThan.push(tt);
          }
          if (CAN_TO_ELEMENT[can] === kyThanElement) {
            const tt = input["Thập Thần"][can];
            if (!tempKyThan.includes(tt)) tempKyThan.push(tt);
          }
        }
        hyThan = tempHyThan;
        kyThan = tempKyThan.length > 0 ? tempKyThan : [kyThanElement]; // Nếu không tìm thấy Thập Thần cụ thể, dùng tên ngũ hành
      }
    } else {
      console.log("Không xác định được Dụng Thần do không có Thập Thần Nguyệt Chi.");
    }
  }

  // Đảm bảo các mảng không rỗng nếu không có gì được thêm vào
  // Theo logic, nếu 1 cái có thì cái kia cũng có, trừ Hỷ Thần chỉ có ở bước 7

  // Convert god names to elements before returning
  const finalDungThan = dungThan.length > 0 ? dungThan : resultFound || nguyetChiThapThan ? [] : ["Không xác định"];
  const finalKyThan = kyThan.length > 0 ? kyThan : resultFound || nguyetChiThapThan ? [] : ["Không xác định"];
  const finalHyThan = hyThan;

  // Only convert if we have valid god names (not "Không xác định")
  const dungThanElements = finalDungThan.includes("Không xác định") ? finalDungThan : convertGodNamesToElements(finalDungThan, nhatChuElement);
  const kyThanElements = finalKyThan.includes("Không xác định") ? finalKyThan : convertGodNamesToElements(finalKyThan, nhatChuElement);
  const hyThanElements = finalHyThan.length > 0 ? convertGodNamesToElements(finalHyThan, nhatChuElement) : finalHyThan;

  return {
    dungThan: dungThanElements,
    kyThan: kyThanElements,
    hyThan: hyThanElements,
  };
}

const getDungThan = (input) => {
  const step1 = tinhDungThanTuDuLieu(input);
  if (step1.dungThan.length > 0 && !step1.dungThan.includes("Không xác định")) {
    console.log("Bước 1: ", step1);
    return step1;
  }
  const step2 = tinhDungThanKyThan(input);
  if (step2.dungThan.length > 0 && !step2.dungThan.includes("Không xác định")) {
    return step2;
  }
  return { dungThan: ["Không xác định"], kyThan: ["Không xác định"], hyThan: [] };
};

// console.log(getDungThan(sampleInput));

module.exports = { getDungThan };
