/**
 * Admin Analytics API Route
 * Provides credit analytics and statistics for admin dashboard
 */

import { NextResponse } from "next/server";
import { getAdminService } from "@/lib/services/admin-service";
import { ChatSDKError } from "@/lib/errors";

export async function GET() {
  try {
    // Get admin service and fetch analytics
    const adminService = await getAdminService();
    const analytics = await adminService.getCreditAnalytics();

    return NextResponse.json(analytics);
  } catch (error) {
    console.error("Admin analytics API error:", error);

    if (error instanceof ChatSDKError) {
      if (error.type === "unauthorized") {
        return NextResponse.json({ error: "Unauthorized access" }, { status: 401 });
      }

      if (error.type === "bad_request") {
        return NextResponse.json({ error: "Database error" }, { status: 500 });
      }
    }

    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
