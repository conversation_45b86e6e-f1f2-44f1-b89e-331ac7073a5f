import { cookies } from "next/headers";
import { redirect } from "next/navigation";

import { Chat } from "@/components/chat";
import { DEFAULT_CHAT_MODEL } from "@/lib/ai/models";
import { generateUUID } from "@/lib/utils";
import { DataStreamHandler } from "@/components/data-stream-handler";
import { getServerSession } from "../(auth)/auth-server";

export default async function Page() {
  const session = await getServerSession();

  // Strict authentication check
  if (!session || !session.user) {
    console.log("Chat page: No session or user, redirecting to login");
    redirect("/login");
  }

  // Check if user is a guest (should be caught by middleware, but double-check)
  const isGuest = session.user.email && /^guest-\d+$/.test(session.user.email);
  if (isGuest) {
    console.log("Chat page: Guest user detected, redirecting to login");
    redirect("/login");
  }

  console.log("Chat page: Authenticated user, rendering chat page");

  const id = generateUUID();

  const cookieStore = await cookies();
  const modelIdFromCookie = cookieStore.get("chat-model");

  if (!modelIdFromCookie) {
    return (
      <>
        <Chat
          key={id}
          id={id}
          initialMessages={[]}
          initialChatModel={DEFAULT_CHAT_MODEL}
          initialVisibilityType="private"
          isReadonly={false}
          session={session}
          autoResume={false}
        />
        <DataStreamHandler id={id} />
      </>
    );
  }

  return (
    <>
      <Chat
        key={id}
        id={id}
        initialMessages={[]}
        initialChatModel={modelIdFromCookie.value}
        initialVisibilityType="private"
        isReadonly={false}
        session={session}
        autoResume={false}
      />
      <DataStreamHandler id={id} />
    </>
  );
}
