"use client";

import { useCallback, useState } from "react";
import { useAuth } from "@/lib/auth/auth-context";
import { clientCreditService } from "@/lib/services/credit-service-client";
import { CREDIT_CONSTANTS } from "@/lib/types/credit";
import type { CreditDeductionResult, CreditAdditionResult, CreditTransaction } from "@/lib/types/credit";

interface UseCreditReturn {
  // Current state
  credits: number;
  isLoadingCredits: boolean;
  creditError: string | null;

  // Credit operations
  deductCredit: (amount: number, description: string) => Promise<CreditDeductionResult>;
  addCredits: (amount: number, description: string) => Promise<CreditAdditionResult>;

  // Validation and checks
  validateSufficientCredits: (amount: number) => boolean;
  canAfford: (amount: number) => boolean;
  hasCredits: boolean;

  // Utility functions
  refreshCredits: () => Promise<void>;
  getCreditHistory: (limit?: number, offset?: number) => Promise<CreditTransaction[]>;

  // Loading states for operations
  isDeducting: boolean;
  isAdding: boolean;
  isLoadingHistory: boolean;
}

/**
 * Custom hook for credit operations
 * Provides easy access to credit balance, operations, and validation
 */
export function useCredits(): UseCreditReturn {
  const { user, credits, isLoadingCredits, creditError, refreshCredits } = useAuth();

  const [isDeducting, setIsDeducting] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [isLoadingHistory, setIsLoadingHistory] = useState(false);

  // Deduct credits with loading state
  const deductCredit = useCallback(
    async (amount: number, description: string): Promise<CreditDeductionResult> => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }

      setIsDeducting(true);
      try {
        const result = await clientCreditService.deductCredit(user.id, amount, description);

        // Refresh credits after successful deduction
        if (result.success) {
          await refreshCredits();
        }

        return result;
      } finally {
        setIsDeducting(false);
      }
    },
    [user?.id, refreshCredits]
  );

  // Add credits with loading state
  const addCredits = useCallback(
    async (amount: number, description: string): Promise<CreditAdditionResult> => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }

      setIsAdding(true);
      try {
        const result = await clientCreditService.addCredits(user.id, amount, description);

        // Refresh credits after successful addition
        if (result.success) {
          await refreshCredits();
        }

        return result;
      } finally {
        setIsAdding(false);
      }
    },
    [user?.id, refreshCredits]
  );

  // Validate if user has sufficient credits
  const validateSufficientCredits = useCallback(
    (amount: number): boolean => {
      return credits >= amount;
    },
    [credits]
  );

  // Alias for validateSufficientCredits for better readability
  const canAfford = useCallback(
    (amount: number): boolean => {
      return validateSufficientCredits(amount);
    },
    [validateSufficientCredits]
  );

  // Check if user has any credits
  const hasCredits = credits > 0;

  // Get credit history with loading state
  const getCreditHistory = useCallback(
    async (limit?: number, offset?: number): Promise<CreditTransaction[]> => {
      if (!user?.id) {
        throw new Error("User not authenticated");
      }

      setIsLoadingHistory(true);
      try {
        return await clientCreditService.getCreditHistory(user.id, limit || CREDIT_CONSTANTS.DEFAULT_HISTORY_LIMIT, offset || 0);
      } finally {
        setIsLoadingHistory(false);
      }
    },
    [user?.id]
  );

  return {
    // Current state
    credits,
    isLoadingCredits,
    creditError,

    // Credit operations
    deductCredit,
    addCredits,

    // Validation and checks
    validateSufficientCredits,
    canAfford,
    hasCredits,

    // Utility functions
    refreshCredits,
    getCreditHistory,

    // Loading states
    isDeducting,
    isAdding,
    isLoadingHistory,
  };
}

/**
 * Hook for credit validation only (lighter weight)
 * Use this when you only need to check credit balance without operations
 */
export function useCreditValidation() {
  const { credits, isLoadingCredits } = useAuth();

  const validateSufficientCredits = useCallback(
    (amount: number): boolean => {
      return credits >= amount;
    },
    [credits]
  );

  const canAfford = useCallback(
    (amount: number): boolean => {
      return validateSufficientCredits(amount);
    },
    [validateSufficientCredits]
  );

  const hasCredits = credits > 0;
  const hasEnoughForQuestion = credits >= CREDIT_CONSTANTS.COST_PER_QUESTION;
  const hasEnoughForTool = credits >= CREDIT_CONSTANTS.COST_PER_TOOL_USE;

  return {
    credits,
    isLoadingCredits,
    validateSufficientCredits,
    canAfford,
    hasCredits,
    hasEnoughForQuestion,
    hasEnoughForTool,
  };
}

/**
 * Hook for credit display formatting
 * Provides formatted credit display strings
 */
export function useCreditDisplay() {
  const { credits, isLoadingCredits } = useAuth();

  const formatCredits = useCallback((amount: number): string => {
    return amount.toLocaleString();
  }, []);

  const creditDisplayText = isLoadingCredits ? "Loading..." : formatCredits(credits);
  const creditBadgeColor = credits > 10 ? "green" : credits > 5 ? "yellow" : "red";
  const isLowCredits = !isLoadingCredits && credits <= 5 && credits > 0;
  const isOutOfCredits = !isLoadingCredits && credits === 0;

  return {
    credits,
    isLoadingCredits,
    creditDisplayText,
    creditBadgeColor,
    isLowCredits,
    isOutOfCredits,
    formatCredits,
  };
}

/**
 * Hook for credit operations with optimistic updates
 * Provides immediate UI feedback before server confirmation
 */
export function useOptimisticCredits() {
  const { credits, refreshCredits } = useAuth();
  const [optimisticCredits, setOptimisticCredits] = useState(credits);
  const [pendingOperations, setPendingOperations] = useState<number>(0);

  // Update optimistic credits when actual credits change
  useState(() => {
    setOptimisticCredits(credits);
  });

  const optimisticDeduct = useCallback(
    async (amount: number, description: string) => {
      // Immediately update UI
      setOptimisticCredits((prev) => Math.max(0, prev - amount));
      setPendingOperations((prev) => prev + 1);

      try {
        const result = await clientCreditService.deductCredit(
          // This would need user ID - simplified for example
          "",
          amount,
          description
        );

        // Refresh actual credits
        await refreshCredits();

        return result;
      } catch (error) {
        // Revert optimistic update on error
        setOptimisticCredits(credits);
        throw error;
      } finally {
        setPendingOperations((prev) => prev - 1);
      }
    },
    [credits, refreshCredits]
  );

  return {
    credits: optimisticCredits,
    actualCredits: credits,
    hasPendingOperations: pendingOperations > 0,
    optimisticDeduct,
  };
}
