"use client";

import dynamic from "next/dynamic";
import { useEffect, useState } from "react";
import { getUser } from "@/app/(auth)/auth";

// Dynamically import the PWA installer to avoid SSR issues
const PWAInstaller = dynamic(() => import("@/components/pwa-installer").then((mod) => mod.PWAInstaller), {
  ssr: false,
});

export function PWAInstallerWrapper() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        setIsLoading(true);
        const user = await getUser(); // Following Supabase best practices
        setIsAuthenticated(!!user);
      } catch (error) {
        console.error("Error checking authentication status:", error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  // Don't render anything while checking authentication status
  if (isLoading) {
    return null;
  }

  return <PWAInstaller isAuthenticated={isAuthenticated} />;
}
