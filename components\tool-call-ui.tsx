"use client";

import { useState } from "react";
import { ChevronDownIcon, ChevronRightIcon, TerminalIcon, CheckCircleIcon } from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "./ui/collapsible";
import { Badge } from "./ui/badge";

interface ToolCallUIProps {
  toolName: string;
  toolCallId: string;
  state: "call" | "result";
  args?: Record<string, any>;
  result?: any;
  children: React.ReactNode;
}

export function ToolCallUI({ toolName, toolCallId, state, args, result, children }: ToolCallUIProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Format the tool name for display
  const formattedToolName = toolName
    .replace(/([A-Z])/g, " $1") // Add space before capital letters
    .replace(/^./, (str) => str.toUpperCase()) // Capitalize first letter
    .replace(/([a-z])([A-Z])/g, "$1 $2") // Add space between camelCase
    .replace(/-/g, " ") // Replace hyphens with spaces
    .replace(/_/g, " ") // Replace underscores with spaces
    .trim();

  return (
    <div className="w-full rounded-md border border-border bg-muted/40 overflow-hidden mb-2 chat-text">
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <Button variant="ghost" className="flex w-full justify-between p-3 h-auto rounded-none hover:bg-muted">
            <div className="flex items-center gap-2 text-sm font-medium">
              <div className="flex items-center justify-center size-5">
                {state === "call" ? (
                  <TerminalIcon size={16} className="text-muted-foreground" />
                ) : (
                  <CheckCircleIcon size={16} className="text-accent" />
                )}
              </div>
              <span>{formattedToolName}</span>
              <Badge variant={state === "call" ? "outline" : "success"} className="ml-2 text-xs font-normal">
                {state === "call" ? "Calling" : "Completed"}
              </Badge>
            </div>
            <div className="flex items-center">
              {isOpen ? (
                <ChevronDownIcon size={16} className="text-muted-foreground" />
              ) : (
                <ChevronRightIcon size={16} className="text-muted-foreground" />
              )}
            </div>
          </Button>
        </CollapsibleTrigger>

        <CollapsibleContent>
          <div className="p-3 pt-0 text-sm chat-text">
            {state === "call" && args && (
              <div className="mb-3">
                <div className="font-medium text-xs text-muted-foreground mb-1">Arguments:</div>
                <pre className="bg-muted p-2 rounded-md overflow-x-auto text-xs">{JSON.stringify(args, null, 2)}</pre>
              </div>
            )}

            {/* {state === "result" && result && (
              <div className="mb-3">
                <div className="font-medium text-xs text-muted-foreground mb-1">Result:</div>
                <pre className="bg-muted p-2 rounded-md overflow-x-auto text-xs">{JSON.stringify(result, null, 2)}</pre>
              </div>
            )} */}
          </div>
        </CollapsibleContent>
      </Collapsible>
    </div>
  );
}
