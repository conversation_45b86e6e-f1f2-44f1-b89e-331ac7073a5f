-- Migration: Fix credit history filtering to exclude admin audit entries
-- This migration updates the get_credit_history function to filter out
-- admin audit entries that don't represent actual credit changes for users

-- Drop the existing function
DROP FUNCTION IF EXISTS get_credit_history(UUID, INTEGER, INTEGER);

-- Recreate the function with improved filtering
CREATE OR REPLACE FUNCTION get_credit_history(
  p_user_id UUID,
  p_limit INTEGER DEFAULT 50,
  p_offset INTEGER DEFAULT 0
) RETURNS TABLE(
  id UUID,
  user_id UUID,
  amount INTEGER,
  description TEXT,
  transaction_type VARCHAR,
  created_at TIMESTAMP
) AS $$
BEGIN
  -- Validate input parameters
  IF p_user_id IS NULL THEN
    RETURN;
  END IF;

  -- Set reasonable defaults and limits
  p_limit := COALESCE(LEAST(p_limit, 1000), 50);
  p_offset := COALESCE(GREATEST(p_offset, 0), 0);

  RETURN QUERY
  SELECT
    ct.id,
    ct.user_id,
    ct.amount,
    ct.description,
    ct.transaction_type,
    ct.created_at
  FROM credit_transactions ct
  WHERE ct.user_id = p_user_id
    -- Exclude admin audit entries that don't represent actual credit changes
    -- These are entries with amount = 0 and descriptions starting with "Bulk operation:"
    AND NOT (
      ct.amount = 0 
      AND ct.transaction_type = 'admin_adjustment' 
      AND ct.description LIKE 'Bulk operation:%'
    )
  ORDER BY ct.created_at DESC
  LIMIT p_limit
  OFFSET p_offset;
EXCEPTION
  WHEN OTHERS THEN
    -- Return empty result on error
    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Clean up existing problematic admin audit entries
-- These are entries with amount = 0 that describe bulk operations
-- but don't represent actual credit changes for users
DELETE FROM credit_transactions 
WHERE amount = 0 
  AND transaction_type = 'admin_adjustment' 
  AND description LIKE 'Bulk operation:%';

-- Add a comment explaining the filtering logic
COMMENT ON FUNCTION get_credit_history(UUID, INTEGER, INTEGER) IS 
'Returns credit transaction history for a user, excluding admin audit entries that do not represent actual credit changes (bulk operation summaries with amount = 0)';
