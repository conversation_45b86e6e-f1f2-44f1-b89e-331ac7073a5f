# PayOS Payment Credit Addition Debug Guide

## Issue Summary

Credits are not being added to user accounts after successful PayOS payments, despite the payment being processed successfully.

## Debugging Steps

### 1. Check PayOS Webhook Reception

First, verify that PayOS webhooks are being received:

```sql
-- Check recent webhook events
SELECT
    id,
    provider,
    event_type,
    payload->>'data' as payment_data,
    processed,
    created_at
FROM payment_webhook_events
WHERE provider = 'payos'
ORDER BY created_at DESC
LIMIT 10;
```

### 2. Check Payment Transaction Records

Verify payment transactions are being created and updated:

```sql
-- Check recent payment transactions
SELECT
    id,
    user_id,
    package_id,
    order_code,
    amount,
    status,
    payment_provider,
    provider_payment_id,
    paid_at,
    created_at,
    updated_at
FROM payment_transactions
ORDER BY created_at DESC
LIMIT 10;
```

### 3. Check Credit Transactions

Look for credit addition records:

```sql
-- Check recent credit transactions
SELECT
    id,
    user_id,
    amount,
    description,
    transaction_type,
    created_at
FROM credit_transactions
WHERE transaction_type = 'addition'
ORDER BY created_at DESC
LIMIT 10;
```

### 4. Check User Credit Balances

Verify user credit balances:

```sql
-- Check user credits for recent payments
SELECT
    u.id,
    u.email,
    u.credits,
    pt.order_code,
    pt.amount as payment_amount,
    pt.status as payment_status,
    pt.package_id
FROM "User" u
JOIN payment_transactions pt ON u.id = pt.user_id
WHERE pt.created_at > NOW() - INTERVAL '24 hours'
ORDER BY pt.created_at DESC;
```

## Common Issues and Solutions

### Issue 1: Webhook Signature Validation Failing

**Symptoms**: Webhooks received but marked as invalid signature
**Check**: Look for "Invalid PayOS webhook signature" in logs
**Solution**: Verify PayOS configuration and signature validation

### Issue 2: Payment Status Not Mapped Correctly

**Symptoms**: Payment marked as successful in PayOS but not PAID in database
**Check**: Verify `mapPayOSStatus` function in PayOS provider
**Solution**: Ensure PayOS status codes are correctly mapped

### Issue 3: Package ID Missing or Invalid

**Symptoms**: Payment successful but no credits added
**Check**: Verify `package_id` field in payment_transactions table
**Solution**: Ensure package ID is correctly stored during payment creation

### Issue 4: Credit Service RPC Function Failing

**Symptoms**: Payment processed but credit addition fails
**Check**: Look for "RPC add_credits failed" in logs
**Solution**: Verify `add_credits` RPC function exists and works

### Issue 5: Transaction Not Found

**Symptoms**: Webhook received but payment transaction not found
**Check**: Look for "Payment transaction not found" in logs
**Solution**: Verify order code matching between PayOS and database

## Enhanced Logging

Add these console.log statements to track the flow:

### In PayOS Webhook Handler (`app/api/webhooks/payment/payos/route.ts`):

```typescript
// Add after line 46
console.log("Webhook result:", JSON.stringify(webhookResult, null, 2));

// Add after line 50
console.log("About to call handleWebhookResult with:", webhookResult);
```

### In Payment Service (`lib/payment/payment-service.ts`):

```typescript
// Add at start of handleWebhookResult method (line 131)
console.log("handleWebhookResult called with:", JSON.stringify(result, null, 2));

// Add after line 135
console.log("Found transaction:", JSON.stringify(transaction, null, 2));

// Add after line 154
console.log("Payment is PAID, checking for package_id:", transaction.package_id);

// Add after line 155
console.log("Found credit package:", JSON.stringify(creditPackage, null, 2));

// Add before line 157
console.log("About to add credits:", {
  userId: transaction.user_id,
  amount: creditPackage.credits,
  description: `Credit package purchase: ${creditPackage.name} (Order: ${result.orderCode})`,
});

// Add after line 161
console.log("Credits added successfully");
```

### In Credit Service (`lib/services/credit-service.ts`):

```typescript
// Add at start of addCredits method (line 261)
console.log("addCredits called with:", { userId, amount, description });

// Add after line 282
console.log("RPC add_credits result:", { data, error });

// Add after line 289
console.log("Credit addition result:", JSON.stringify(result, null, 2));
```

## Testing Webhook Manually

You can test the webhook endpoint manually:

```bash
curl -X POST http://localhost:3000/api/webhooks/payment/payos \
  -H "Content-Type: application/json" \
  -H "signature: test-signature" \
  -d '{
    "data": {
      "orderCode": "YOUR_ORDER_CODE",
      "code": "00",
      "amount": 120000,
      "paymentLinkId": "test-payment-id",
      "transactionDateTime": "2024-01-01T00:00:00Z",
      "reference": "test-ref",
      "description": "Test payment"
    }
  }'
```

## Debug Tools Created

### 1. Enhanced Logging

The webhook handler and payment service now include comprehensive logging to track the entire flow.

### 2. Debug API Endpoint

Use the debug API to analyze any payment:

```bash
# Get comprehensive payment flow analysis
GET /api/debug/payment-flow?orderCode=YOUR_ORDER_CODE

# Manually trigger credit addition (for testing)
POST /api/debug/payment-flow
{
  "orderCode": "YOUR_ORDER_CODE",
  "force": true  // Optional: bypass status checks
}
```

### 3. RPC Function Test

Run the SQL script `test-credit-rpc.sql` to verify the database function works.

## Next Steps

1. **Use Debug API**: Call `/api/debug/payment-flow?orderCode=YOUR_ORDER_CODE` with a failed payment
2. **Check Logs**: Monitor server logs during a test payment
3. **Test RPC Function**: Run the SQL test script
4. **Manual Credit Addition**: Use the POST endpoint to manually add credits if needed
5. **Verify Database**: Run the SQL queries to check data consistency

## Expected Flow

1. PayOS sends webhook → `POST /api/webhooks/payment/payos`
2. Webhook validated → `payosProvider.processWebhook()`
3. Webhook result processed → `paymentService.handleWebhookResult()`
4. Transaction updated → `updatePaymentTransaction()`
5. Credits added → `creditService.addCredits()`
6. RPC function called → `add_credits(p_user_id, p_amount, p_description)`
7. User credits updated in database
8. Cache invalidated → User sees new credit balance
