// No longer need Form import

import { signOut } from "@/app/(auth)/auth";
import { useRouter } from "next/navigation";
import { toast } from "@/components/toast";

export const SignOutForm = () => {
  const router = useRouter();

  const handleSignOut = async () => {
    try {
      // Sign out from Supabase (which now handles PostHog reset internally)
      const result = await signOut();

      if (!result.ok) {
        throw new Error(result.error || "Sign out failed");
      }

      // Use router.push for navigation
      router.push("/login");

      // Force a clean reload after a short delay
      // This ensures all state is cleared properly
      setTimeout(() => {
        // Clear any localStorage items that might be causing issues
        try {
          localStorage.removeItem("posthog_distinct_id");
          localStorage.removeItem("ph_loaded");
          localStorage.removeItem("ph_init_called");
          localStorage.removeItem("ph_date_created");
        } catch (e) {
          console.warn("Error clearing localStorage:", e);
        }

        // Use replace instead of href to avoid adding to history
        window.location.replace("/login");
      }, 100);
    } catch (error) {
      console.error("Error signing out:", error);
      toast({
        type: "error",
        description: "There was a problem signing out. Please try again.",
      });
      // Still try to redirect to login
      router.push("/login");
    }
  };

  return (
    <div className="w-full">
      <button type="button" className="w-full text-left px-1 py-0.5 text-red-500" onClick={handleSignOut}>
        Sign out
      </button>
    </div>
  );
};
