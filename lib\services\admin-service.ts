/**
 * Admin service for managing users and credit operations
 * Provides data access layer for admin dashboard functionality
 */

import "server-only";

import { createClient } from "@/lib/supabase/server";
import { ChatSDKError } from "@/lib/errors";
import { requireAdmin } from "@/lib/auth/admin-utils";
import { auth } from "@/app/(auth)/auth-server";

/**
 * Admin user data interface (without last_activity and created_at as they don't exist in schema)
 */
export interface AdminUserData {
  id: string;
  email: string;
  credits: number;
  total_credits_used: number;
}

/**
 * User list response with pagination
 */
export interface AdminUserListResponse {
  users: AdminUserData[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

/**
 * Credit analytics data interface
 */
export interface CreditAnalytics {
  total_credits_consumed: number;
  total_users: number;
  average_credits_per_user: number;
  most_active_users: Array<{
    user_id: string;
    email: string;
    credits_used: number;
  }>;
}

/**
 * User search and filter options
 */
export interface UserSearchOptions {
  search?: string;
  minCredits?: number;
  maxCredits?: number;
  page?: number;
  limit?: number;
}

/**
 * Admin service class for user and credit management
 */
export class AdminService {
  private supabase: Awaited<ReturnType<typeof createClient>>;

  constructor(supabase: Awaited<ReturnType<typeof createClient>>) {
    this.supabase = supabase;
  }

  /**
   * Validate admin access for all operations
   */
  private async validateAdminAccess(): Promise<void> {
    const session = await auth();
    if (!session?.user) {
      throw new ChatSDKError("unauthorized:auth", "Authentication required");
    }

    requireAdmin(session.user, "admin service operation");
  }

  /**
   * Get paginated list of users with credit information
   */
  async getUserList(options: UserSearchOptions = {}): Promise<AdminUserListResponse> {
    await this.validateAdminAccess();

    const { search = "", minCredits, maxCredits, page = 1, limit = 50 } = options;

    try {
      // Build the query
      let query = this.supabase.from("User").select(
        `
          id,
          email,
          credits
        `,
        { count: "exact" }
      );

      // Add search filter
      if (search.trim()) {
        query = query.ilike("email", `%${search.trim()}%`);
      }

      // Add credit range filters
      if (minCredits !== undefined) {
        query = query.gte("credits", minCredits);
      }
      if (maxCredits !== undefined) {
        query = query.lte("credits", maxCredits);
      }

      // Add pagination
      const offset = (page - 1) * limit;
      query = query.order("email", { ascending: true }).range(offset, offset + limit - 1);

      const { data: users, error, count } = await query;

      if (error) {
        console.error("Failed to fetch users:", error);
        throw new ChatSDKError("bad_request:database", "Failed to fetch users");
      }

      // Get total credits used for each user (placeholder for now)
      const usersWithTotalCredits: AdminUserData[] = (users || []).map((user) => ({
        ...user,
        total_credits_used: 0, // TODO: Calculate from credit_transactions table
      }));

      return {
        users: usersWithTotalCredits,
        total: count || 0,
        page,
        limit,
        hasMore: (count || 0) > offset + limit,
      };
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Unexpected error in getUserList:", error);
      throw new ChatSDKError("bad_request:database", "Failed to fetch user list");
    }
  }

  /**
   * Get credit analytics and statistics
   */
  async getCreditAnalytics(): Promise<CreditAnalytics> {
    await this.validateAdminAccess();

    try {
      // Get total users
      const { count: totalUsers, error: usersError } = await this.supabase.from("User").select("*", { count: "exact", head: true });

      if (usersError) {
        console.error("Failed to get total users:", usersError);
        throw new ChatSDKError("bad_request:database", "Failed to get user statistics");
      }

      // Get total credits consumed (sum of all deductions)
      const { data: creditData, error: creditError } = await this.supabase
        .from("credit_transactions")
        .select("amount")
        .eq("transaction_type", "deduction");

      if (creditError) {
        console.error("Failed to get credit transactions:", creditError);
        throw new ChatSDKError("bad_request:database", "Failed to get credit statistics");
      }

      const totalCreditsConsumed = (creditData || []).reduce((sum, transaction) => sum + Math.abs(transaction.amount), 0);

      // Calculate average credits per user
      const { data: userData, error: userDataError } = await this.supabase.from("User").select("credits");

      if (userDataError) {
        console.error("Failed to get user credits:", userDataError);
        throw new ChatSDKError("bad_request:database", "Failed to get user credit data");
      }

      const totalCurrentCredits = (userData || []).reduce((sum, user) => sum + user.credits, 0);
      const averageCreditsPerUser = totalUsers ? totalCurrentCredits / totalUsers : 0;

      // Get most active users (placeholder for now)
      const mostActiveUsers: Array<{ user_id: string; email: string; credits_used: number }> = []; // TODO: Implement based on credit usage

      return {
        total_credits_consumed: totalCreditsConsumed,
        total_users: totalUsers || 0,
        average_credits_per_user: Math.round(averageCreditsPerUser * 100) / 100,
        most_active_users: mostActiveUsers,
      };
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Unexpected error in getCreditAnalytics:", error);
      throw new ChatSDKError("bad_request:database", "Failed to fetch credit analytics");
    }
  }

  /**
   * Get a specific user by ID
   */
  async getUserById(userId: string): Promise<AdminUserData | null> {
    await this.validateAdminAccess();

    try {
      const { data: user, error } = await this.supabase
        .from("User")
        .select(
          `
          id,
          email,
          credits
        `
        )
        .eq("id", userId)
        .single();

      if (error) {
        if (error.code === "PGRST116") {
          return null; // User not found
        }
        console.error("Failed to fetch user:", error);
        throw new ChatSDKError("bad_request:database", "Failed to fetch user");
      }

      return {
        ...user,
        total_credits_used: 0, // TODO: Calculate from credit_transactions table
      };
    } catch (error) {
      if (error instanceof ChatSDKError) {
        throw error;
      }
      console.error("Unexpected error in getUserById:", error);
      throw new ChatSDKError("bad_request:database", "Failed to fetch user");
    }
  }
}

/**
 * Get admin service instance
 */
export async function getAdminService(): Promise<AdminService> {
  const supabase = await createClient();
  return new AdminService(supabase);
}
