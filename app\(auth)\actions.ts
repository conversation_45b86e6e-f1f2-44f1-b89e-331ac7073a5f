"use server";

import { z } from "zod";
import { createClient } from "@/lib/supabase/server";
import { signIn } from "./auth-server";

const authFormSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export interface LoginActionState {
  status: "idle" | "in_progress" | "success" | "failed" | "invalid_data" | "email_not_confirmed";
  email?: string;
}

export const login = async (_: LoginActionState, formData: FormData): Promise<LoginActionState> => {
  try {
    const validatedData = authFormSchema.parse({
      email: formData.get("email"),
      password: formData.get("password"),
    });

    // Sign in with the provided credentials using Supabase
    const signInResult = await signIn({
      email: validatedData.email,
      password: validatedData.password,
    });

    // Check if sign in was successful
    if (!signInResult.ok) {
      console.error("Failed to sign in:", signInResult.error);

      // Check for "Email not confirmed" error
      if (signInResult.error?.includes("<PERSON>ail not confirmed")) {
        console.log("Email not confirmed, redirecting to verification page");
        return {
          status: "email_not_confirmed",
          email: validatedData.email,
        };
      }

      return { status: "failed" };
    }

    // If we get here, sign in was successful
    return { status: "success" };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { status: "invalid_data" };
    }

    return { status: "failed" };
  }
};

export interface RegisterActionState {
  status: "idle" | "in_progress" | "success" | "failed" | "user_exists" | "invalid_data" | "verification_required";
}

export const register = async (_: RegisterActionState, formData: FormData): Promise<RegisterActionState> => {
  try {
    console.log("Starting registration process");

    const validatedData = authFormSchema.parse({
      email: formData.get("email"),
      password: formData.get("password"),
    });

    console.log("Validated form data for email:", validatedData.email);

    // Create a Supabase client
    const supabase = await createClient();

    // A better approach to check if a user exists
    // We'll use the signUp method with a special option that only checks if the user exists
    console.log("Attempting to sign up user with Supabase");
    const { data, error } = await supabase.auth.signUp({
      email: validatedData.email,
      password: validatedData.password,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000"}/auth/callback`,
      },
    });

    console.log("Supabase signUp response:", {
      hasData: !!data,
      hasSession: !!data?.session,
      hasUser: !!data?.user,
      hasError: !!error,
      errorMessage: error ? error.message : null,
    });

    // If the error contains "User already registered", the user exists
    if (error?.message.includes("User already registered")) {
      console.log("User already exists:", validatedData.email);
      return { status: "user_exists" } as RegisterActionState;
    }

    // If we have data but no session, it means the user was created but needs to confirm their email
    if (data && !data.session) {
      console.log("User created, email confirmation required:", validatedData.email);
      return { status: "verification_required" };
    }

    // If we get here, the user was created and we have a session (auto-confirmed email)
    if (error) {
      console.error("Failed to sign up:", error.message);
      return { status: "failed" };
    }

    // If we have a session, the user was auto-confirmed and we can sign them in
    if (data?.session) {
      console.log("User created and auto-confirmed:", validatedData.email);

      // No need to sign in again as Supabase already created a session
      return { status: "success" };
    }

    // If we get here, something unexpected happened
    console.error("Unexpected state after registration");
    return { status: "success" };
  } catch (error) {
    if (error instanceof z.ZodError) {
      console.error("Validation error during registration:", error.errors);
      return { status: "invalid_data" };
    }

    console.error("Registration error:", error);
    // Log more details about the error
    if (error instanceof Error) {
      console.error("Error name:", error.name);
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    } else {
      console.error("Unknown error type:", typeof error);
    }

    return { status: "failed" };
  }
};
